/**
 * Authentication API Middleware Unit Tests (TEST-004)
 * Tests for authentication API middleware
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { pocketBaseService } from '../../../common/services/pocketbase.js'

// Mock the pocketBaseService
vi.mock('../../../common/services/pocketbase.js', () => ({
  pocketBaseService: {
    login: vi.fn(),
    register: vi.fn(),
    logout: vi.fn(),
    refreshToken: vi.fn(),
    requestPasswordReset: vi.fn(),
    resetPassword: vi.fn(),
    getCurrentUser: vi.fn(),
    updateProfile: vi.fn(),
    changePassword: vi.fn(),
    isAuthenticated: vi.fn(),
    getToken: vi.fn(),
  },
}))

// Create a mock authService for testing middleware
const authService = {
  login: async (email, password) => {
    return await pocketBaseService.login(email, password)
  },
  register: async (userData) => {
    return await pocketBaseService.register(userData)
  },
  logout: async () => {
    return await pocketBaseService.logout()
  },
  refreshToken: async () => {
    return await pocketBaseService.refreshToken()
  },
  requestPasswordReset: async (email) => {
    return await pocketBaseService.requestPasswordReset(email)
  },
  resetPassword: async (resetData) => {
    return await pocketBaseService.resetPassword(resetData)
  },
  getCurrentUser: async () => {
    return await pocketBaseService.getCurrentUser()
  },
  updateProfile: async (profileData) => {
    return await pocketBaseService.updateProfile(profileData)
  },
  changePassword: async (passwordData) => {
    return await pocketBaseService.changePassword(passwordData)
  },
  isAuthenticated: () => {
    return pocketBaseService.isAuthenticated()
  },
  getToken: () => {
    return pocketBaseService.getToken()
  },
}

describe('Authentication API Middleware (TEST-004)', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('Login API', () => {
    it('should call PocketBase login with correct parameters', async () => {
      const mockResponse = {
        success: true,
        user: { id: '1', email: '<EMAIL>' },
        token: 'jwt-token-123',
      }
      
      pocketBaseService.login.mockResolvedValue(mockResponse)

      const result = await authService.login('<EMAIL>', 'password123')

      expect(pocketBaseService.login).toHaveBeenCalledWith('<EMAIL>', 'password123')
      expect(result).toEqual(mockResponse)
    })

    it('should handle login API errors', async () => {
      const mockError = new Error('API Error')
      pocketBaseService.login.mockRejectedValue(mockError)

      await expect(authService.login('<EMAIL>', 'password123')).rejects.toThrow('API Error')
    })

    it('should validate input parameters', async () => {
      // Test with empty email
      await expect(authService.login('', 'password123')).rejects.toThrow()
      
      // Test with empty password
      await expect(authService.login('<EMAIL>', '')).rejects.toThrow()
    })
  })

  describe('Registration API', () => {
    it('should call PocketBase register with correct parameters', async () => {
      const userData = {
        email: '<EMAIL>',
        password: 'password123',
        name: 'Test User',
      }
      const mockResponse = {
        success: true,
        user: { id: '1', ...userData },
      }
      
      pocketBaseService.register.mockResolvedValue(mockResponse)

      const result = await authService.register(userData)

      expect(pocketBaseService.register).toHaveBeenCalledWith(userData)
      expect(result).toEqual(mockResponse)
    })

    it('should handle registration API errors', async () => {
      const userData = { email: '<EMAIL>', password: 'password123' }
      const mockError = new Error('Registration failed')
      pocketBaseService.register.mockRejectedValue(mockError)

      await expect(authService.register(userData)).rejects.toThrow('Registration failed')
    })

    it('should validate registration data', async () => {
      // Test with invalid email format
      const invalidData = { email: 'invalid-email', password: 'password123' }
      await expect(authService.register(invalidData)).rejects.toThrow()
    })
  })

  describe('Token Refresh API', () => {
    it('should call PocketBase refreshToken', async () => {
      const mockResponse = {
        success: true,
        token: 'new-jwt-token-456',
        user: { id: '1', email: '<EMAIL>' },
      }
      
      pocketBaseService.refreshToken.mockResolvedValue(mockResponse)

      const result = await authService.refreshToken()

      expect(pocketBaseService.refreshToken).toHaveBeenCalled()
      expect(result).toEqual(mockResponse)
    })

    it('should handle token refresh API errors', async () => {
      const mockError = new Error('Token refresh failed')
      pocketBaseService.refreshToken.mockRejectedValue(mockError)

      await expect(authService.refreshToken()).rejects.toThrow('Token refresh failed')
    })

    it('should handle expired refresh tokens', async () => {
      const mockResponse = { success: false, error: 'Refresh token expired' }
      pocketBaseService.refreshToken.mockResolvedValue(mockResponse)

      const result = await authService.refreshToken()

      expect(result.success).toBe(false)
      expect(result.error).toBe('Refresh token expired')
    })
  })

  describe('Password Reset API', () => {
    it('should call PocketBase requestPasswordReset', async () => {
      const mockResponse = { success: true }
      pocketBaseService.requestPasswordReset.mockResolvedValue(mockResponse)

      const result = await authService.requestPasswordReset('<EMAIL>')

      expect(pocketBaseService.requestPasswordReset).toHaveBeenCalledWith('<EMAIL>')
      expect(result).toEqual(mockResponse)
    })

    it('should call PocketBase resetPassword', async () => {
      const resetData = { token: 'reset-token', password: 'newpassword123' }
      const mockResponse = { success: true }
      pocketBaseService.resetPassword.mockResolvedValue(mockResponse)

      const result = await authService.resetPassword(resetData)

      expect(pocketBaseService.resetPassword).toHaveBeenCalledWith(resetData)
      expect(result).toEqual(mockResponse)
    })

    it('should validate email format for password reset', async () => {
      await expect(authService.requestPasswordReset('invalid-email')).rejects.toThrow()
    })

    it('should validate reset token and password', async () => {
      const invalidData = { token: '', password: '123' }
      await expect(authService.resetPassword(invalidData)).rejects.toThrow()
    })
  })

  describe('Profile Management API', () => {
    it('should call PocketBase updateProfile', async () => {
      const profileData = { name: 'Updated Name', email: '<EMAIL>' }
      const mockResponse = {
        success: true,
        user: { id: '1', ...profileData },
      }
      pocketBaseService.updateProfile.mockResolvedValue(mockResponse)

      const result = await authService.updateProfile(profileData)

      expect(pocketBaseService.updateProfile).toHaveBeenCalledWith(profileData)
      expect(result).toEqual(mockResponse)
    })

    it('should call PocketBase changePassword', async () => {
      const passwordData = { currentPassword: 'oldpass', newPassword: 'newpass123' }
      const mockResponse = { success: true }
      pocketBaseService.changePassword.mockResolvedValue(mockResponse)

      const result = await authService.changePassword(passwordData)

      expect(pocketBaseService.changePassword).toHaveBeenCalledWith(passwordData)
      expect(result).toEqual(mockResponse)
    })

    it('should validate profile update data', async () => {
      const invalidData = { email: 'invalid-email' }
      await expect(authService.updateProfile(invalidData)).rejects.toThrow()
    })

    it('should validate password change data', async () => {
      const invalidData = { currentPassword: '', newPassword: '123' }
      await expect(authService.changePassword(invalidData)).rejects.toThrow()
    })
  })

  describe('Authentication Status API', () => {
    it('should call PocketBase isAuthenticated', () => {
      pocketBaseService.isAuthenticated.mockReturnValue(true)

      const result = authService.isAuthenticated()

      expect(pocketBaseService.isAuthenticated).toHaveBeenCalled()
      expect(result).toBe(true)
    })

    it('should call PocketBase getCurrentUser', async () => {
      const mockUser = { id: '1', email: '<EMAIL>' }
      pocketBaseService.getCurrentUser.mockResolvedValue({
        success: true,
        user: mockUser,
      })

      const result = await authService.getCurrentUser()

      expect(pocketBaseService.getCurrentUser).toHaveBeenCalled()
      expect(result.user).toEqual(mockUser)
    })

    it('should call PocketBase getToken', () => {
      const mockToken = 'jwt-token-123'
      pocketBaseService.getToken.mockReturnValue(mockToken)

      const result = authService.getToken()

      expect(pocketBaseService.getToken).toHaveBeenCalled()
      expect(result).toBe(mockToken)
    })

    it('should handle authentication check errors', async () => {
      pocketBaseService.getCurrentUser.mockRejectedValue(new Error('Auth check failed'))

      await expect(authService.getCurrentUser()).rejects.toThrow('Auth check failed')
    })
  })

  describe('Logout API', () => {
    it('should call PocketBase logout', async () => {
      const mockResponse = { success: true }
      pocketBaseService.logout.mockResolvedValue(mockResponse)

      const result = await authService.logout()

      expect(pocketBaseService.logout).toHaveBeenCalled()
      expect(result).toEqual(mockResponse)
    })

    it('should handle logout API errors', async () => {
      const mockError = new Error('Logout failed')
      pocketBaseService.logout.mockRejectedValue(mockError)

      await expect(authService.logout()).rejects.toThrow('Logout failed')
    })

    it('should handle logout when not authenticated', async () => {
      pocketBaseService.isAuthenticated.mockReturnValue(false)
      
      const result = await authService.logout()

      expect(result.success).toBe(true) // Should succeed even if not authenticated
    })
  })

  describe('API Response Handling', () => {
    it('should handle malformed API responses', async () => {
      pocketBaseService.login.mockResolvedValue(null)

      await expect(authService.login('<EMAIL>', 'password123')).rejects.toThrow()
    })

    it('should handle network timeouts', async () => {
      pocketBaseService.login.mockImplementation(() => {
        return new Promise((_, reject) => {
          setTimeout(() => reject(new Error('Network timeout')), 100)
        })
      })

      await expect(authService.login('<EMAIL>', 'password123')).rejects.toThrow('Network timeout')
    })

    it('should handle rate limiting', async () => {
      const rateLimitError = new Error('Too many requests')
      rateLimitError.status = 429
      pocketBaseService.login.mockRejectedValue(rateLimitError)

      await expect(authService.login('<EMAIL>', 'password123')).rejects.toThrow('Too many requests')
    })
  })
})
