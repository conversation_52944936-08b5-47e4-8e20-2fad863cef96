{"numTotalTestSuites": 175, "numPassedTestSuites": 114, "numFailedTestSuites": 61, "numPendingTestSuites": 0, "numTotalTests": 318, "numPassedTests": 255, "numFailedTests": 63, "numPendingTests": 0, "numTodoTests": 0, "snapshot": {"added": 0, "failure": false, "filesAdded": 0, "filesRemoved": 0, "filesRemovedList": [], "filesUnmatched": 0, "filesUpdated": 0, "matched": 0, "total": 0, "unchecked": 0, "uncheckedKeysByFile": [], "unmatched": 0, "updated": 0, "didUpdate": false}, "startTime": 1752958004680, "success": false, "testResults": [{"assertionResults": [{"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "User Registration Journey"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) User Registration Journey should complete full user registration flow", "status": "passed", "title": "should complete full user registration flow", "duration": 4.550715999999738, "failureMessages": [], "location": {"line": 90, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "User Registration Journey"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) User Registration Journey should handle registration validation errors", "status": "passed", "title": "should handle registration validation errors", "duration": 0.48414099999990867, "failureMessages": [], "location": {"line": 117, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "User Registration Journey"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) User Registration Journey should handle existing email registration attempt", "status": "passed", "title": "should handle existing email registration attempt", "duration": 0.3993369999998322, "failureMessages": [], "location": {"line": 138, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "User Login <PERSON>"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) User Login Journey should complete successful login flow", "status": "passed", "title": "should complete successful login flow", "duration": 0.5957399999997506, "failureMessages": [], "location": {"line": 161, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "User Login <PERSON>"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) User Login Journey should handle invalid login credentials", "status": "passed", "title": "should handle invalid login credentials", "duration": 0.4235239999998157, "failureMessages": [], "location": {"line": 184, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "User Login <PERSON>"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) User Login Journey should redirect to intended page after login", "status": "passed", "title": "should redirect to intended page after login", "duration": 0.3956480000006195, "failureMessages": [], "location": {"line": 203, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "User Lo<PERSON>ut <PERSON>"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) User Logout Journey should complete logout flow", "status": "passed", "title": "should complete logout flow", "duration": 0.47487100000034843, "failureMessages": [], "location": {"line": 223, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "Password Reset Journey"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) Password Reset Journey should complete password reset request flow", "status": "passed", "title": "should complete password reset request flow", "duration": 0.3677820000002612, "failureMessages": [], "location": {"line": 246, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "Password Reset Journey"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) Password Reset Journey should complete password reset confirmation flow", "status": "passed", "title": "should complete password reset confirmation flow", "duration": 0.4533950000004552, "failureMessages": [], "location": {"line": 262, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "Profile Management Journey"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) Profile Management Journey should complete profile update flow", "status": "passed", "title": "should complete profile update flow", "duration": 0.4223009999996066, "failureMessages": [], "location": {"line": 285, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "Profile Management Journey"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) Profile Management Journey should complete password change flow", "status": "passed", "title": "should complete password change flow", "duration": 0.4534650000005058, "failureMessages": [], "location": {"line": 303, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "Session Management Journey"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) Session Management Journey should handle session timeout gracefully", "status": "passed", "title": "should handle session timeout gracefully", "duration": 0.3229380000002493, "failureMessages": [], "location": {"line": 324, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "Session Management Journey"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) Session Management Journey should handle concurrent sessions", "status": "passed", "title": "should handle concurrent sessions", "duration": 0.41103000000020984, "failureMessages": [], "location": {"line": 347, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "Error <PERSON>"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) Error Handling Journey should handle network errors gracefully", "status": "passed", "title": "should handle network errors gracefully", "duration": 0.4804940000003626, "failureMessages": [], "location": {"line": 367, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "Error <PERSON>"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) Error Handling Journey should handle server errors gracefully", "status": "passed", "title": "should handle server errors gracefully", "duration": 0.30564400000002934, "failureMessages": [], "location": {"line": 388, "column": 7}, "meta": {}}], "startTime": 1752958011760, "endTime": 1752958011771.3057, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/e2e/auth-journeys.test.js"}, {"assertionResults": [{"ancestorTitles": ["Responsive Design E2E Tests (TEST-009)", "Authentication Forms Responsive Design"], "fullName": "Responsive Design E2E Tests (TEST-009) Authentication Forms Responsive Design should display login form correctly on mobile devices", "status": "failed", "title": "should display login form correctly on mobile devices", "duration": 6.29405499999848, "failureMessages": ["TypeError: Cannot read properties of undefined (reading 'isVisible')\n    at checkElementVisibility (/Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:73:24)\n    at /Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:124:27\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "TypeError: Cannot read properties of undefined (reading 'isVisible')\n    at checkElementVisibility (/Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:73:24)\n    at /Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:124:27\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "TypeError: Cannot read properties of undefined (reading 'isVisible')\n    at checkElementVisibility (/Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:73:24)\n    at /Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:124:27\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 113, "column": 7}, "meta": {}}, {"ancestorTitles": ["Responsive Design E2E Tests (TEST-009)", "Authentication Forms Responsive Design"], "fullName": "Responsive Design E2E Tests (TEST-009) Authentication Forms Responsive Design should display registration form correctly on tablet devices", "status": "failed", "title": "should display registration form correctly on tablet devices", "duration": 3.7877509999998438, "failureMessages": ["TypeError: Cannot read properties of undefined (reading 'isVisible')\n    at checkElementVisibility (/Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:73:24)\n    at /Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:147:27\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "TypeError: Cannot read properties of undefined (reading 'isVisible')\n    at checkElementVisibility (/Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:73:24)\n    at /Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:147:27\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "TypeError: Cannot read properties of undefined (reading 'isVisible')\n    at checkElementVisibility (/Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:73:24)\n    at /Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:147:27\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 138, "column": 7}, "meta": {}}, {"ancestorTitles": ["Responsive Design E2E Tests (TEST-009)", "Authentication Forms Responsive Design"], "fullName": "Responsive Design E2E Tests (TEST-009) Authentication Forms Responsive Design should display forms correctly on desktop", "status": "failed", "title": "should display forms correctly on desktop", "duration": 1.906420999999682, "failureMessages": ["TypeError: Cannot read properties of undefined (reading 'boundingBox')\n    at getElementBounds (/Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:78:24)\n    at /Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:166:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "TypeError: Cannot read properties of undefined (reading 'boundingBox')\n    at getElementBounds (/Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:78:24)\n    at /Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:166:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "TypeError: Cannot read properties of undefined (reading 'boundingBox')\n    at getElementBounds (/Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:78:24)\n    at /Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:166:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 156, "column": 7}, "meta": {}}, {"ancestorTitles": ["Responsive Design E2E Tests (TEST-009)", "Authentication Forms Responsive Design"], "fullName": "Responsive Design E2E Tests (TEST-009) Authentication Forms Responsive Design should handle very small screens gracefully", "status": "failed", "title": "should handle very small screens gracefully", "duration": 0.8736129999997502, "failureMessages": ["TypeError: Cannot read properties of undefined (reading 'isVisible')\n    at checkElementVisibility (/Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:73:24)\n    at /Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:180:27\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "TypeError: Cannot read properties of undefined (reading 'isVisible')\n    at checkElementVisibility (/Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:73:24)\n    at /Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:180:27\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "TypeError: Cannot read properties of undefined (reading 'isVisible')\n    at checkElementVisibility (/Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:73:24)\n    at /Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:180:27\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 171, "column": 7}, "meta": {}}, {"ancestorTitles": ["Responsive Design E2E Tests (TEST-009)", "Navigation Responsive Design"], "fullName": "Responsive Design E2E Tests (TEST-009) Navigation Responsive Design should show mobile navigation drawer on small screens", "status": "failed", "title": "should show mobile navigation drawer on small screens", "duration": 0.7533370000001014, "failureMessages": ["TypeError: Cannot read properties of undefined (reading 'isVisible')\n    at checkElementVisibility (/Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:73:24)\n    at /Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:207:33\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "TypeError: Cannot read properties of undefined (reading 'isVisible')\n    at checkElementVisibility (/Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:73:24)\n    at /Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:207:33\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "TypeError: Cannot read properties of undefined (reading 'isVisible')\n    at checkElementVisibility (/Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:73:24)\n    at /Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:207:33\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 191, "column": 7}, "meta": {}}, {"ancestorTitles": ["Responsive Design E2E Tests (TEST-009)", "Navigation Responsive Design"], "fullName": "Responsive Design E2E Tests (TEST-009) Navigation Responsive Design should show desktop navigation on large screens", "status": "failed", "title": "should show desktop navigation on large screens", "duration": 0.9128130000008241, "failureMessages": ["TypeError: Cannot read properties of undefined (reading 'isVisible')\n    at checkElementVisibility (/Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:73:24)\n    at /Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:240:33\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "TypeError: Cannot read properties of undefined (reading 'isVisible')\n    at checkElementVisibility (/Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:73:24)\n    at /Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:240:33\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "TypeError: Cannot read properties of undefined (reading 'isVisible')\n    at checkElementVisibility (/Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:73:24)\n    at /Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:240:33\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 225, "column": 7}, "meta": {}}, {"ancestorTitles": ["Responsive Design E2E Tests (TEST-009)", "Navigation Responsive Design"], "fullName": "Responsive Design E2E Tests (TEST-009) Navigation Responsive Design should adapt navigation for tablet screens", "status": "failed", "title": "should adapt navigation for tablet screens", "duration": 0.7485649999998714, "failureMessages": ["TypeError: Cannot read properties of undefined (reading 'isVisible')\n    at checkElementVisibility (/Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:73:24)\n    at /Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:256:29\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "TypeError: Cannot read properties of undefined (reading 'isVisible')\n    at checkElementVisibility (/Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:73:24)\n    at /Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:256:29\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "TypeError: Cannot read properties of undefined (reading 'isVisible')\n    at checkElementVisibility (/Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:73:24)\n    at /Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:256:29\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 247, "column": 7}, "meta": {}}, {"ancestorTitles": ["Responsive Design E2E Tests (TEST-009)", "Touch Interactions"], "fullName": "Responsive Design E2E Tests (TEST-009) Touch Interactions should handle touch interactions on mobile devices", "status": "failed", "title": "should handle touch interactions on mobile devices", "duration": 1.2649679999994987, "failureMessages": ["TypeError: Cannot read properties of undefined (reading 'boundingBox')\n    at getElementBounds (/Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:78:24)\n    at checkTouchTargetSize (/Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:82:18)\n    at /Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:280:31\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "TypeError: Cannot read properties of undefined (reading 'boundingBox')\n    at getElementBounds (/Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:78:24)\n    at checkTouchTargetSize (/Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:82:18)\n    at /Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:280:31\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "TypeError: Cannot read properties of undefined (reading 'boundingBox')\n    at getElementBounds (/Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:78:24)\n    at checkTouchTargetSize (/Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:82:18)\n    at /Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:280:31\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 266, "column": 7}, "meta": {}}, {"ancestorTitles": ["Responsive Design E2E Tests (TEST-009)", "Touch Interactions"], "fullName": "Responsive Design E2E Tests (TEST-009) Touch Interactions should provide adequate spacing between touch targets", "status": "failed", "title": "should provide adequate spacing between touch targets", "duration": 0.7669040000000678, "failureMessages": ["TypeError: Cannot read properties of undefined (reading 'boundingBox')\n    at getElementBounds (/Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:78:24)\n    at /Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:297:29\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "TypeError: Cannot read properties of undefined (reading 'boundingBox')\n    at getElementBounds (/Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:78:24)\n    at /Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:297:29\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "TypeError: Cannot read properties of undefined (reading 'boundingBox')\n    at getElementBounds (/Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:78:24)\n    at /Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:297:29\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 284, "column": 7}, "meta": {}}, {"ancestorTitles": ["Responsive Design E2E Tests (TEST-009)", "Content Layout Responsive Design"], "fullName": "Responsive Design E2E Tests (TEST-009) Content Layout Responsive Design should stack content vertically on mobile", "status": "passed", "title": "should stack content vertically on mobile", "duration": 0.5279369999989285, "failureMessages": [], "location": {"line": 306, "column": 7}, "meta": {}}, {"ancestorTitles": ["Responsive Design E2E Tests (TEST-009)", "Content Layout Responsive Design"], "fullName": "Responsive Design E2E Tests (TEST-009) Content Layout Responsive Design should display content in rows on desktop", "status": "passed", "title": "should display content in rows on desktop", "duration": 0.22906300000067858, "failureMessages": [], "location": {"line": 324, "column": 7}, "meta": {}}, {"ancestorTitles": ["Responsive Design E2E Tests (TEST-009)", "Content Layout Responsive Design"], "fullName": "Responsive Design E2E Tests (TEST-009) Content Layout Responsive Design should adjust font sizes for different screen sizes", "status": "passed", "title": "should adjust font sizes for different screen sizes", "duration": 0.4594020000004093, "failureMessages": [], "location": {"line": 342, "column": 7}, "meta": {}}, {"ancestorTitles": ["Responsive Design E2E Tests (TEST-009)", "Form Input Responsive Design"], "fullName": "Responsive Design E2E Tests (TEST-009) Form Input Responsive Design should adjust input field sizes for mobile", "status": "failed", "title": "should adjust input field sizes for mobile", "duration": 0.8064720000002126, "failureMessages": ["TypeError: Cannot read properties of undefined (reading 'boundingBox')\n    at getElementBounds (/Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:78:24)\n    at /Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:380:27\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "TypeError: Cannot read properties of undefined (reading 'boundingBox')\n    at getElementBounds (/Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:78:24)\n    at /Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:380:27\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "TypeError: Cannot read properties of undefined (reading 'boundingBox')\n    at getElementBounds (/Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:78:24)\n    at /Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:380:27\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 370, "column": 7}, "meta": {}}, {"ancestorTitles": ["Responsive Design E2E Tests (TEST-009)", "Form Input Responsive Design"], "fullName": "Responsive Design E2E Tests (TEST-009) Form Input Responsive Design should handle keyboard appearance on mobile", "status": "failed", "title": "should handle keyboard appearance on mobile", "duration": 0.6737749999992957, "failureMessages": ["TypeError: Cannot read properties of undefined (reading 'isVisible')\n    at checkElementVisibility (/Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:73:24)\n    at /Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:401:27\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "TypeError: Cannot read properties of undefined (reading 'isVisible')\n    at checkElementVisibility (/Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:73:24)\n    at /Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:401:27\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "TypeError: Cannot read properties of undefined (reading 'isVisible')\n    at checkElementVisibility (/Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:73:24)\n    at /Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:401:27\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 386, "column": 7}, "meta": {}}, {"ancestorTitles": ["Responsive Design E2E Tests (TEST-009)", "Accessibility on Different Devices"], "fullName": "Responsive Design E2E Tests (TEST-009) Accessibility on Different Devices should maintain accessibility on mobile devices", "status": "failed", "title": "should maintain accessibility on mobile devices", "duration": 0.7775559999990946, "failureMessages": ["TypeError: Cannot read properties of undefined (reading 'boundingBox')\n    at getElementBounds (/Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:78:24)\n    at checkTouchTargetSize (/Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:82:18)\n    at /Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:416:31\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "TypeError: Cannot read properties of undefined (reading 'boundingBox')\n    at getElementBounds (/Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:78:24)\n    at checkTouchTargetSize (/Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:82:18)\n    at /Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:416:31\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "TypeError: Cannot read properties of undefined (reading 'boundingBox')\n    at getElementBounds (/Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:78:24)\n    at checkTouchTargetSize (/Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:82:18)\n    at /Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:416:31\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 407, "column": 7}, "meta": {}}, {"ancestorTitles": ["Responsive Design E2E Tests (TEST-009)", "Accessibility on Different Devices"], "fullName": "Responsive Design E2E Tests (TEST-009) Accessibility on Different Devices should support keyboard navigation on all devices", "status": "passed", "title": "should support keyboard navigation on all devices", "duration": 0.2628359999998793, "failureMessages": [], "location": {"line": 429, "column": 7}, "meta": {}}], "startTime": 1752958013247, "endTime": 1752958013269.263, "status": "failed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js"}, {"assertionResults": [{"ancestorTitles": ["Authentication Flows Integration Tests (TEST-005)", "Complete Login Flow"], "fullName": "Authentication Flows Integration Tests (TEST-005) Complete Login Flow should complete full login flow successfully", "status": "passed", "title": "should complete full login flow successfully", "duration": 9.908685000000332, "failureMessages": [], "location": {"line": 66, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Flows Integration Tests (TEST-005)", "Complete Login Flow"], "fullName": "Authentication Flows Integration Tests (TEST-005) Complete Login Flow should handle login failure gracefully", "status": "passed", "title": "should handle login failure gracefully", "duration": 1.4447369999998045, "failureMessages": [], "location": {"line": 101, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Flows Integration Tests (TEST-005)", "Complete Login Flow"], "fullName": "Authentication Flows Integration Tests (TEST-005) Complete Login Flow should handle network errors during login", "status": "failed", "title": "should handle network errors during login", "duration": 6.563901999999871, "failureMessages": ["AssertionError: expected null to be 'Network error' // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/integration/auth-flows.test.js:136:31\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected null to be 'Network error' // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/integration/auth-flows.test.js:136:31\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected null to be 'Network error' // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/integration/auth-flows.test.js:136:31\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 126, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Flows Integration Tests (TEST-005)", "Complete Registration Flow"], "fullName": "Authentication Flows Integration Tests (TEST-005) Complete Registration Flow should complete full registration flow successfully", "status": "passed", "title": "should complete full registration flow successfully", "duration": 1.3278370000007271, "failureMessages": [], "location": {"line": 142, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Flows Integration Tests (TEST-005)", "Complete Registration Flow"], "fullName": "Authentication Flows Integration Tests (TEST-005) Complete Registration Flow should handle registration validation errors", "status": "passed", "title": "should handle registration validation errors", "duration": 1.7881390000002284, "failureMessages": [], "location": {"line": 165, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Flows Integration Tests (TEST-005)", "Complete Logout Flow"], "fullName": "Authentication Flows Integration Tests (TEST-005) Complete Logout Flow should complete full logout flow successfully", "status": "passed", "title": "should complete full logout flow successfully", "duration": 0.9779980000002979, "failureMessages": [], "location": {"line": 187, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Flows Integration Tests (TEST-005)", "Complete Logout Flow"], "fullName": "Authentication Flows Integration Tests (TEST-005) Complete Logout Flow should handle logout errors gracefully", "status": "passed", "title": "should handle logout errors gracefully", "duration": 0.751351999999315, "failureMessages": [], "location": {"line": 212, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Flows Integration Tests (TEST-005)", "Password Reset Flow"], "fullName": "Authentication Flows Integration Tests (TEST-005) Password Reset Flow should complete password reset request flow", "status": "passed", "title": "should complete password reset request flow", "duration": 3.0386969999999565, "failureMessages": [], "location": {"line": 232, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Flows Integration Tests (TEST-005)", "Password Reset Flow"], "fullName": "Authentication Flows Integration Tests (TEST-005) Password Reset Flow should complete password reset confirmation flow", "status": "failed", "title": "should complete password reset confirmation flow", "duration": 5.012679999999818, "failureMessages": ["AssertionError: expected false to be true // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/integration/auth-flows.test.js:254:30\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected false to be true // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/integration/auth-flows.test.js:254:30\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected false to be true // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/integration/auth-flows.test.js:254:30\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 245, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Flows Integration Tests (TEST-005)", "Session Restoration Flow"], "fullName": "Authentication Flows Integration Tests (TEST-005) Session Restoration Flow should restore session from localStorage on initialization", "status": "failed", "title": "should restore session from localStorage on initialization", "duration": 3.2466440000007424, "failureMessages": ["AssertionError: expected null to deeply equal { id: '1', email: '<EMAIL>' }\n    at /Volumes/External Drive/Development/track-tasks/test/integration/auth-flows.test.js:282:30\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected null to deeply equal { id: '1', email: '<EMAIL>' }\n    at /Volumes/External Drive/Development/track-tasks/test/integration/auth-flows.test.js:282:30\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected null to deeply equal { id: '1', email: '<EMAIL>' }\n    at /Volumes/External Drive/Development/track-tasks/test/integration/auth-flows.test.js:282:30\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 260, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Flows Integration Tests (TEST-005)", "Session Restoration Flow"], "fullName": "Authentication Flows Integration Tests (TEST-005) Session Restoration Flow should handle invalid stored session gracefully", "status": "passed", "title": "should handle invalid stored session gracefully", "duration": 0.5477160000009462, "failureMessages": [], "location": {"line": 288, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Flows Integration Tests (TEST-005)", "Profile Management Flow"], "fullName": "Authentication Flows Integration Tests (TEST-005) Profile Management Flow should complete profile update flow", "status": "failed", "title": "should complete profile update flow", "duration": 2.897299000000203, "failureMessages": ["AssertionError: expected \"spy\" to be called with arguments: [ { name: 'New Name' } ]\u001b[90m\n\nNumber of calls: \u001b[1m0\u001b[22m\n\u001b[39m\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1355:10)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/test/integration/auth-flows.test.js:328:47\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected \"spy\" to be called with arguments: [ { name: 'New Name' } ]\u001b[90m\n\nNumber of calls: \u001b[1m0\u001b[22m\n\u001b[39m\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1355:10)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/test/integration/auth-flows.test.js:328:47\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected \"spy\" to be called with arguments: [ { name: 'New Name' } ]\u001b[90m\n\nNumber of calls: \u001b[1m0\u001b[22m\n\u001b[39m\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1355:10)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/test/integration/auth-flows.test.js:328:47\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 310, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Flows Integration Tests (TEST-005)", "Profile Management Flow"], "fullName": "Authentication Flows Integration Tests (TEST-005) Profile Management Flow should complete password change flow", "status": "failed", "title": "should complete password change flow", "duration": 1.6737670000002254, "failureMessages": ["TypeError: authStore.changePassword is not a function\n    at /Volumes/External Drive/Development/track-tasks/test/integration/auth-flows.test.js:340:38\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)", "TypeError: authStore.changePassword is not a function\n    at /Volumes/External Drive/Development/track-tasks/test/integration/auth-flows.test.js:340:38\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)", "TypeError: authStore.changePassword is not a function\n    at /Volumes/External Drive/Development/track-tasks/test/integration/auth-flows.test.js:340:38\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)"], "location": {"line": 331, "column": 7}, "meta": {}}], "startTime": 1752958013342, "endTime": 1752958013381.6738, "status": "failed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/integration/auth-flows.test.js"}, {"assertionResults": [{"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Network Error Handling"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Network Error Handling should handle network errors during login", "status": "failed", "title": "should handle network errors during login", "duration": 15.613456999999471, "failureMessages": ["AssertionError: expected null to be 'Network error' // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/integration/error-handling.test.js:113:31\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected null to be 'Network error' // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/integration/error-handling.test.js:113:31\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected null to be 'Network error' // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/integration/error-handling.test.js:113:31\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 101, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Network Error Handling"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Network Error Handling should handle timeout errors", "status": "failed", "title": "should handle timeout errors", "duration": 156.186208000001, "failureMessages": ["AssertionError: expected null to be 'Request timeout' // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/integration/error-handling.test.js:134:31\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected null to be 'Request timeout' // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/integration/error-handling.test.js:134:31\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected null to be 'Request timeout' // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/integration/error-handling.test.js:134:31\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 118, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Network Error Handling"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Network Error Handling should handle server errors gracefully", "status": "passed", "title": "should handle server errors gracefully", "duration": 1.0809549999994488, "failureMessages": [], "location": {"line": 137, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "LocalStorage Error Handling"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) LocalStorage Error Handling should handle localStorage unavailability", "status": "failed", "title": "should handle localStorage unavailability", "duration": 4.981772000001001, "failureMessages": ["AssertionError: expected [Function] to not throw an error but 'Error: localStorage quota exceeded' was thrown\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1420:16)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/test/integration/error-handling.test.js:168:54\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)", "AssertionError: expected [Function] to not throw an error but 'Error: localStorage quota exceeded' was thrown\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1420:16)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/test/integration/error-handling.test.js:168:54\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)", "AssertionError: expected [Function] to not throw an error but 'Error: localStorage quota exceeded' was thrown\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1420:16)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/test/integration/error-handling.test.js:168:54\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)"], "location": {"line": 158, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "LocalStorage Error Handling"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) LocalStorage Error Handling should handle localStorage quota exceeded", "status": "failed", "title": "should handle localStorage quota exceeded", "duration": 3.759697999999844, "failureMessages": ["AssertionError: expected [Function] to not throw an error but 'Error: QuotaExceededError' was thrown\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1420:16)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/test/integration/error-handling.test.js:188:54\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)", "AssertionError: expected [Function] to not throw an error but 'Error: QuotaExceededError' was thrown\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1420:16)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/test/integration/error-handling.test.js:188:54\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)", "AssertionError: expected [Function] to not throw an error but 'Error: QuotaExceededError' was thrown\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1420:16)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/test/integration/error-handling.test.js:188:54\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)"], "location": {"line": 175, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "LocalStorage Error Handling"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) LocalStorage Error Handling should handle corrupted localStorage data", "status": "failed", "title": "should handle corrupted localStorage data", "duration": 3.9916980000016338, "failureMessages": ["AssertionError: expected [Function] to not throw an error but 'TypeError: authStore.restoreAuthState…' was thrown\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1420:16)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/test/integration/error-handling.test.js:202:54\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)", "AssertionError: expected [Function] to not throw an error but 'TypeError: authStore.restoreAuthState…' was thrown\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1420:16)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/test/integration/error-handling.test.js:202:54\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)", "AssertionError: expected [Function] to not throw an error but 'TypeError: authStore.restoreAuthState…' was thrown\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1420:16)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/test/integration/error-handling.test.js:202:54\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)"], "location": {"line": 191, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Authentication Edge Cases"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Authentication Edge Cases should handle simultaneous login attempts", "status": "passed", "title": "should handle simultaneous login attempts", "duration": 102.28875700000026, "failureMessages": [], "location": {"line": 212, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Authentication Edge Cases"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Authentication Edge Cases should handle login with empty credentials", "status": "failed", "title": "should handle login with empty credentials", "duration": 307.5738930000007, "failureMessages": ["AssertionError: expected true to be false // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/integration/error-handling.test.js:248:30\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected true to be false // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/integration/error-handling.test.js:248:30\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected true to be false // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/integration/error-handling.test.js:248:30\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 243, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Authentication Edge Cases"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Authentication Edge Cases should handle login with null/undefined credentials", "status": "failed", "title": "should handle login with null/undefined credentials", "duration": 610.9345979999998, "failureMessages": ["AssertionError: expected true to be false // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/integration/error-handling.test.js:258:31\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected true to be false // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/integration/error-handling.test.js:258:31\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected true to be false // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/integration/error-handling.test.js:258:31\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 252, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Authentication Edge Cases"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Authentication Edge Cases should handle extremely long input values", "status": "passed", "title": "should handle extremely long input values", "duration": 101.79922200000146, "failureMessages": [], "location": {"line": 263, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Token Refresh Edge Cases"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Token Refresh Edge Cases should handle token refresh when already refreshing", "status": "failed", "title": "should handle token refresh when already refreshing", "duration": 3.42017499999929, "failureMessages": ["AssertionError: expected false to be true // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/integration/error-handling.test.js:310:32\n    at Array.forEach (<anonymous>)\n    at /Volumes/External Drive/Development/track-tasks/test/integration/error-handling.test.js:309:15\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected false to be true // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/integration/error-handling.test.js:310:32\n    at Array.forEach (<anonymous>)\n    at /Volumes/External Drive/Development/track-tasks/test/integration/error-handling.test.js:309:15\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected false to be true // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/integration/error-handling.test.js:310:32\n    at Array.forEach (<anonymous>)\n    at /Volumes/External Drive/Development/track-tasks/test/integration/error-handling.test.js:309:15\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 280, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Token Refresh Edge Cases"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Token Refresh Edge Cases should handle token refresh with invalid response", "status": "failed", "title": "should handle token refresh with invalid response", "duration": 1.9380870000004506, "failureMessages": ["AssertionError: expected null to be truthy\n    at /Volumes/External Drive/Development/track-tasks/test/integration/error-handling.test.js:328:31\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected null to be truthy\n    at /Volumes/External Drive/Development/track-tasks/test/integration/error-handling.test.js:328:31\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected null to be truthy\n    at /Volumes/External Drive/Development/track-tasks/test/integration/error-handling.test.js:328:31\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 315, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Session Management Edge Cases"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Session Management Edge Cases should handle session restoration with partial data", "status": "failed", "title": "should handle session restoration with partial data", "duration": 2.6295649999992747, "failureMessages": ["TypeError: authStore.restoreAuthState is not a function\n    at /Volumes/External Drive/Development/track-tasks/test/integration/error-handling.test.js:344:17\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)", "TypeError: authStore.restoreAuthState is not a function\n    at /Volumes/External Drive/Development/track-tasks/test/integration/error-handling.test.js:344:17\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)", "TypeError: authStore.restoreAuthState is not a function\n    at /Volumes/External Drive/Development/track-tasks/test/integration/error-handling.test.js:344:17\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)"], "location": {"line": 333, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Session Management Edge Cases"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Session Management Edge Cases should handle concurrent session modifications", "status": "passed", "title": "should handle concurrent session modifications", "duration": 0.6796450000001641, "failureMessages": [], "location": {"line": 352, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Memory and Performance Edge Cases"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Memory and Performance Edge Cases should handle memory pressure gracefully", "status": "passed", "title": "should handle memory pressure gracefully", "duration": 212.39808600000106, "failureMessages": [], "location": {"line": 379, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Memory and Performance Edge Cases"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Memory and Performance Edge Cases should handle rapid successive operations", "status": "passed", "title": "should handle rapid successive operations", "duration": 117.08592399999907, "failureMessages": [], "location": {"line": 394, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Browser Compatibility Edge Cases"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Browser Compatibility Edge Cases should handle missing browser APIs gracefully", "status": "failed", "title": "should handle missing browser APIs gracefully", "duration": 15.111740999998801, "failureMessages": ["AssertionError: expected [Function] to not throw an error but 'TypeError: authStore.restoreAuthState…' was thrown\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1420:16)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/test/integration/error-handling.test.js:422:54\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)", "AssertionError: expected [Function] to not throw an error but 'TypeError: authStore.restoreAuthState…' was thrown\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1420:16)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/test/integration/error-handling.test.js:422:54\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)", "AssertionError: expected [Function] to not throw an error but 'TypeError: authStore.restoreAuthState…' was thrown\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1420:16)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/test/integration/error-handling.test.js:422:54\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)"], "location": {"line": 415, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Browser Compatibility Edge Cases"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Browser Compatibility Edge Cases should handle disabled cookies", "status": "passed", "title": "should handle disabled cookies", "duration": 0.8723359999985405, "failureMessages": [], "location": {"line": 428, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Data Validation Edge Cases"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Data Validation Edge Cases should handle malformed server responses", "status": "passed", "title": "should handle malformed server responses", "duration": 0.790766000000076, "failureMessages": [], "location": {"line": 445, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Data Validation Edge Cases"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Data Validation Edge Cases should handle unexpected data types", "status": "passed", "title": "should handle unexpected data types", "duration": 0.665380999998888, "failureMessages": [], "location": {"line": 465, "column": 7}, "meta": {}}], "startTime": 1752958013653, "endTime": 1752958015317.6653, "status": "failed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/integration/error-handling.test.js"}, {"assertionResults": [{"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Role Checking"], "fullName": "Role-Based Access Control Tests (TEST-007) Role Checking should correctly identify admin users", "status": "passed", "title": "should correctly identify admin users", "duration": 2.2878440000004048, "failureMessages": [], "location": {"line": 107, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Role Checking"], "fullName": "Role-Based Access Control Tests (TEST-007) Role Checking should correctly identify regular users", "status": "passed", "title": "should correctly identify regular users", "duration": 0.581876000000193, "failureMessages": [], "location": {"line": 120, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Role Checking"], "fullName": "Role-Based Access Control Tests (TEST-007) Role Checking should handle multiple roles", "status": "passed", "title": "should handle multiple roles", "duration": 0.4260819999999512, "failureMessages": [], "location": {"line": 135, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Permission Checking"], "fullName": "Role-Based Access Control Tests (TEST-007) Permission Checking should check individual permissions correctly", "status": "passed", "title": "should check individual permissions correctly", "duration": 0.5622679999996762, "failureMessages": [], "location": {"line": 152, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Permission Checking"], "fullName": "Role-Based Access Control Tests (TEST-007) Permission Checking should handle admin users with all permissions", "status": "passed", "title": "should handle admin users with all permissions", "duration": 0.44769000000087544, "failureMessages": [], "location": {"line": 167, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Permission Checking"], "fullName": "Role-Based Access Control Tests (TEST-007) Permission Checking should handle users with no permissions", "status": "passed", "title": "should handle users with no permissions", "duration": 0.38244200000008277, "failureMessages": [], "location": {"line": 182, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Route Access Control"], "fullName": "Role-Based Access Control Tests (TEST-007) Route Access Control should allow admin access to admin routes", "status": "passed", "title": "should allow admin access to admin routes", "duration": 1.2191920000004757, "failureMessages": [], "location": {"line": 197, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Route Access Control"], "fullName": "Role-Based Access Control Tests (TEST-007) Route Access Control should deny regular user access to admin routes", "status": "passed", "title": "should deny regular user access to admin routes", "duration": 1.381177999999636, "failureMessages": [], "location": {"line": 211, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Route Access Control"], "fullName": "Role-Based Access Control Tests (TEST-007) Route Access Control should enforce role-based route access", "status": "passed", "title": "should enforce role-based route access", "duration": 0.5097559999994701, "failureMessages": [], "location": {"line": 225, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Route Access Control"], "fullName": "Role-Based Access Control Tests (TEST-007) Route Access Control should deny access when user lacks required role", "status": "passed", "title": "should deny access when user lacks required role", "duration": 0.5725579999998445, "failureMessages": [], "location": {"line": 239, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Route Access Control"], "fullName": "Role-Based Access Control Tests (TEST-007) Route Access Control should enforce permission-based route access", "status": "passed", "title": "should enforce permission-based route access", "duration": 0.42012199999953737, "failureMessages": [], "location": {"line": 253, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Route Access Control"], "fullName": "Role-Based Access Control Tests (TEST-007) Route Access Control should deny access when user lacks required permissions", "status": "passed", "title": "should deny access when user lacks required permissions", "duration": 0.3685669999995298, "failureMessages": [], "location": {"line": 271, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Route Access Control"], "fullName": "Role-Based Access Control Tests (TEST-007) Route Access Control should redirect unauthenticated users to login", "status": "passed", "title": "should redirect unauthenticated users to login", "duration": 0.31664099999943573, "failureMessages": [], "location": {"line": 289, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Complex Access Control Scenarios"], "fullName": "Role-Based Access Control Tests (TEST-007) Complex Access Control Scenarios should handle routes with multiple requirements", "status": "passed", "title": "should handle routes with multiple requirements", "duration": 0.34727100000054634, "failureMessages": [], "location": {"line": 302, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Complex Access Control Scenarios"], "fullName": "Role-Based Access Control Tests (TEST-007) Complex Access Control Scenarios should deny access if any requirement is not met", "status": "passed", "title": "should deny access if any requirement is not met", "duration": 0.4241509999992559, "failureMessages": [], "location": {"line": 324, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Dynamic Permission Checking"], "fullName": "Role-Based Access Control Tests (TEST-007) Dynamic Permission Checking should check resource-specific permissions", "status": "passed", "title": "should check resource-specific permissions", "duration": 0.32743699999991804, "failureMessages": [], "location": {"line": 348, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Dynamic Permission Checking"], "fullName": "Role-Based Access Control Tests (TEST-007) Dynamic Permission Checking should handle hierarchical permissions", "status": "passed", "title": "should handle hierarchical permissions", "duration": 0.3209659999993164, "failureMessages": [], "location": {"line": 363, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Role Inheritance"], "fullName": "Role-Based Access Control Tests (TEST-007) Role Inheritance should handle role inheritance correctly", "status": "passed", "title": "should handle role inheritance correctly", "duration": 0.30172099999981583, "failureMessages": [], "location": {"line": 382, "column": 7}, "meta": {}}], "startTime": 1752958013674, "endTime": 1752958013686.321, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/integration/rbac.test.js"}, {"assertionResults": [{"ancestorTitles": ["Token Refresh Mechanism Tests (TEST-006)", "Automatic Token Refresh"], "fullName": "Token Refresh Mechanism Tests (TEST-006) Automatic Token Refresh should refresh token automatically before expiration", "status": "failed", "title": "should refresh token automatically before expiration", "duration": 16.120796000001064, "failureMessages": ["AssertionError: expected false to be true // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/integration/token-refresh.test.js:76:30\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected false to be true // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/integration/token-refresh.test.js:76:30\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected false to be true // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/integration/token-refresh.test.js:76:30\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 55, "column": 7}, "meta": {}}, {"ancestorTitles": ["Token Refresh Mechanism Tests (TEST-006)", "Automatic Token Refresh"], "fullName": "Token Refresh Mechanism Tests (TEST-006) Automatic Token Refresh should handle token refresh failure by logging out", "status": "failed", "title": "should handle token refresh failure by logging out", "duration": 4.773165999999037, "failureMessages": ["AssertionError: expected null to be 'Token expired' // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/integration/token-refresh.test.js:104:31\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected null to be 'Token expired' // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/integration/token-refresh.test.js:104:31\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected null to be 'Token expired' // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/integration/token-refresh.test.js:104:31\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 85, "column": 7}, "meta": {}}, {"ancestorTitles": ["Token Refresh Mechanism Tests (TEST-006)", "Automatic Token Refresh"], "fullName": "Token Refresh Mechanism Tests (TEST-006) Automatic Token Refresh should handle network errors during token refresh", "status": "failed", "title": "should handle network errors during token refresh", "duration": 3.1766200000001845, "failureMessages": ["AssertionError: expected null to be 'Network error' // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/integration/token-refresh.test.js:126:31\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected null to be 'Network error' // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/integration/token-refresh.test.js:126:31\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected null to be 'Network error' // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/integration/token-refresh.test.js:126:31\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 112, "column": 7}, "meta": {}}, {"ancestorTitles": ["Token Refresh Mechanism Tests (TEST-006)", "Token Refresh Timing"], "fullName": "Token Refresh Mechanism Tests (TEST-006) Token Refresh Timing should schedule token refresh based on token expiration", "status": "passed", "title": "should schedule token refresh based on token expiration", "duration": 1.1344410000001517, "failureMessages": [], "location": {"line": 131, "column": 7}, "meta": {}}, {"ancestorTitles": ["Token Refresh Mechanism Tests (TEST-006)", "Token Refresh Timing"], "fullName": "Token Refresh Mechanism Tests (TEST-006) Token Refresh Timing should refresh immediately if token is already expired", "status": "passed", "title": "should refresh immediately if token is already expired", "duration": 0.5975699999999051, "failureMessages": [], "location": {"line": 158, "column": 7}, "meta": {}}, {"ancestorTitles": ["Token Refresh Mechanism Tests (TEST-006)", "Token Refresh During API Calls"], "fullName": "Token Refresh Mechanism Tests (TEST-006) Token Refresh During API Calls should refresh token when API returns 401 Unauthorized", "status": "failed", "title": "should refresh token when API returns 401 Unauthorized", "duration": 3.165047999998933, "failureMessages": ["Error: Authentication failed\n    at makeApiCall (/Volumes/External Drive/Development/track-tasks/test/integration/token-refresh.test.js:234:19)\n    at /Volumes/External Drive/Development/track-tasks/test/integration/token-refresh.test.js:242:24\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected \"spy\" to be called at least once\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1335:10)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/test/integration/token-refresh.test.js:245:46\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "Error: Authentication failed\n    at makeApiCall (/Volumes/External Drive/Development/track-tasks/test/integration/token-refresh.test.js:234:19)\n    at /Volumes/External Drive/Development/track-tasks/test/integration/token-refresh.test.js:242:24\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 184, "column": 7}, "meta": {}}, {"ancestorTitles": ["Token Refresh Mechanism Tests (TEST-006)", "Token Refresh During API Calls"], "fullName": "Token Refresh Mechanism Tests (TEST-006) Token Refresh During API Calls should logout user if token refresh fails during API call", "status": "passed", "title": "should logout user if token refresh fails during API call", "duration": 7.507078000000547, "failureMessages": ["Error: promise resolved \"{ status: 200, json: [Function json] }\" instead of rejecting\n    at Assertion.__VITEST_REJECTS__ (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1610:17)\n    at Assertion.propertyGetter (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1486:27)\n    at Reflect.get (<anonymous>)\n    at Object.proxyGetter [as get] (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1577:22)\n    at /Volumes/External Drive/Development/track-tasks/test/integration/token-refresh.test.js:293:45\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)"], "location": {"line": 251, "column": 7}, "meta": {}}, {"ancestorTitles": ["Token Refresh Mechanism Tests (TEST-006)", "Concurrent Token Refresh"], "fullName": "Token Refresh Mechanism Tests (TEST-006) Concurrent Token Refresh should handle multiple simultaneous refresh requests", "status": "failed", "title": "should handle multiple simultaneous refresh requests", "duration": 3.272236000000703, "failureMessages": ["AssertionError: expected false to be true // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/integration/token-refresh.test.js:330:32\n    at Array.forEach (<anonymous>)\n    at /Volumes/External Drive/Development/track-tasks/test/integration/token-refresh.test.js:329:15\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected false to be true // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/integration/token-refresh.test.js:330:32\n    at Array.forEach (<anonymous>)\n    at /Volumes/External Drive/Development/track-tasks/test/integration/token-refresh.test.js:329:15\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected false to be true // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/integration/token-refresh.test.js:330:32\n    at Array.forEach (<anonymous>)\n    at /Volumes/External Drive/Development/track-tasks/test/integration/token-refresh.test.js:329:15\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 303, "column": 7}, "meta": {}}, {"ancestorTitles": ["Token Refresh Mechanism Tests (TEST-006)", "Token Refresh State Management"], "fullName": "Token Refresh Mechanism Tests (TEST-006) Token Refresh State Management should set loading state during token refresh", "status": "failed", "title": "should set loading state during token refresh", "duration": 3.205399000000398, "failureMessages": ["AssertionError: expected false to be true // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/integration/token-refresh.test.js:360:36\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected false to be true // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/integration/token-refresh.test.js:360:36\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected false to be true // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/integration/token-refresh.test.js:360:36\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 339, "column": 7}, "meta": {}}, {"ancestorTitles": ["Token Refresh Mechanism Tests (TEST-006)", "Token Refresh State Management"], "fullName": "Token Refresh Mechanism Tests (TEST-006) Token Refresh State Management should clear error state on successful token refresh", "status": "passed", "title": "should clear error state on successful token refresh", "duration": 0.5637430000006134, "failureMessages": [], "location": {"line": 364, "column": 7}, "meta": {}}], "startTime": 1752958014649, "endTime": 1752958014692.5637, "status": "failed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/integration/token-refresh.test.js"}, {"assertionResults": [{"ancestorTitles": ["Route Guards (TEST-003)", "Authentication Guard"], "fullName": "Route Guards (TEST-003) Authentication Guard should initialize auth store if not initialized", "status": "passed", "title": "should initialize auth store if not initialized", "duration": 3.8462039999994886, "failureMessages": [], "location": {"line": 94, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Authentication Guard"], "fullName": "Route Guards (TEST-003) Authentication Guard should not initialize auth store if already initialized", "status": "passed", "title": "should not initialize auth store if already initialized", "duration": 0.7453709999990679, "failureMessages": [], "location": {"line": 108, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Protected Routes"], "fullName": "Route Guards (TEST-003) Protected Routes should allow access to protected routes when authenticated", "status": "passed", "title": "should allow access to protected routes when authenticated", "duration": 0.5260440000001836, "failureMessages": [], "location": {"line": 123, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Protected Routes"], "fullName": "Route Guards (TEST-003) Protected Routes should redirect to login when accessing protected routes while unauthenticated", "status": "passed", "title": "should redirect to login when accessing protected routes while unauthenticated", "duration": 1.8524839999990945, "failureMessages": [], "location": {"line": 136, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Protected Routes"], "fullName": "Route Guards (TEST-003) Protected Routes should preserve query parameters in redirect", "status": "passed", "title": "should preserve query parameters in redirect", "duration": 1.5714700000007724, "failureMessages": [], "location": {"line": 152, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Guest-Only Routes"], "fullName": "Route Guards (TEST-003) Guest-Only Routes should allow access to guest-only routes when unauthenticated", "status": "passed", "title": "should allow access to guest-only routes when unauthenticated", "duration": 0.5511970000006841, "failureMessages": [], "location": {"line": 171, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Guest-Only Routes"], "fullName": "Route Guards (TEST-003) Guest-Only Routes should redirect to home when accessing guest-only routes while authenticated", "status": "passed", "title": "should redirect to home when accessing guest-only routes while authenticated", "duration": 1.7246460000005754, "failureMessages": [], "location": {"line": 184, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Guest-Only Routes"], "fullName": "Route Guards (TEST-003) Guest-Only Routes should redirect authenticated users from register page", "status": "passed", "title": "should redirect authenticated users from register page", "duration": 2.5348510000003444, "failureMessages": [], "location": {"line": 197, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Guest-Only Routes"], "fullName": "Route Guards (TEST-003) Guest-Only Routes should redirect authenticated users from forgot-password page", "status": "passed", "title": "should redirect authenticated users from forgot-password page", "duration": 0.5439009999990958, "failureMessages": [], "location": {"line": 210, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Admin Routes"], "fullName": "Route Guards (TEST-003) Admin Routes should allow access to admin routes when user is admin", "status": "passed", "title": "should allow access to admin routes when user is admin", "duration": 0.40314500000022235, "failureMessages": [], "location": {"line": 225, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Admin Routes"], "fullName": "Route Guards (TEST-003) Admin Routes should redirect to unauthorized when non-admin user accesses admin routes", "status": "failed", "title": "should redirect to unauthorized when non-admin user accesses admin routes", "duration": 7.855285999999978, "failureMessages": ["AssertionError: expected \"spy\" to be called with arguments: [ { path: '/unauthorized' } ]\u001b[90m\n\nReceived: \n\n\u001b[1m  1st spy call:\n\n\u001b[22m\u001b[32m- [\u001b[90m\n\u001b[32m-   {\u001b[90m\n\u001b[32m-     \"path\": \"/unauthorized\",\u001b[90m\n\u001b[32m-   },\u001b[90m\n\u001b[32m- ]\u001b[90m\n\u001b[31m+ []\u001b[90m\n\u001b[39m\u001b[90m\n\nNumber of calls: \u001b[1m1\u001b[22m\n\u001b[39m\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1355:10)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/router/guards.test.js:250:20\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected \"spy\" to be called with arguments: [ { path: '/unauthorized' } ]\u001b[90m\n\nReceived: \n\n\u001b[1m  1st spy call:\n\n\u001b[22m\u001b[32m- [\u001b[90m\n\u001b[32m-   {\u001b[90m\n\u001b[32m-     \"path\": \"/unauthorized\",\u001b[90m\n\u001b[32m-   },\u001b[90m\n\u001b[32m- ]\u001b[90m\n\u001b[31m+ []\u001b[90m\n\u001b[39m\u001b[90m\n\nNumber of calls: \u001b[1m1\u001b[22m\n\u001b[39m\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1355:10)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/router/guards.test.js:250:20\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected \"spy\" to be called with arguments: [ { path: '/unauthorized' } ]\u001b[90m\n\nReceived: \n\n\u001b[1m  1st spy call:\n\n\u001b[22m\u001b[32m- [\u001b[90m\n\u001b[32m-   {\u001b[90m\n\u001b[32m-     \"path\": \"/unauthorized\",\u001b[90m\n\u001b[32m-   },\u001b[90m\n\u001b[32m- ]\u001b[90m\n\u001b[31m+ []\u001b[90m\n\u001b[39m\u001b[90m\n\nNumber of calls: \u001b[1m1\u001b[22m\n\u001b[39m\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1355:10)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/router/guards.test.js:250:20\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 239, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Admin Routes"], "fullName": "Route Guards (TEST-003) Admin Routes should redirect to unauthorized when unauthenticated user accesses admin routes", "status": "failed", "title": "should redirect to unauthorized when unauthenticated user accesses admin routes", "duration": 3.3940290000009554, "failureMessages": ["AssertionError: expected \"spy\" to be called with arguments: [ { path: '/unauthorized' } ]\u001b[90m\n\nReceived: \n\n\u001b[1m  1st spy call:\n\n\u001b[22m\u001b[2m  [\u001b[22m\n\u001b[2m    {\u001b[22m\n\u001b[32m-     \"path\": \"/unauthorized\",\u001b[90m\n\u001b[31m+     \"path\": \"/login\",\u001b[90m\n\u001b[31m+     \"query\": {\u001b[90m\n\u001b[31m+       \"redirect\": \"/admin\",\u001b[90m\n\u001b[31m+     },\u001b[90m\n\u001b[2m    },\u001b[22m\n\u001b[2m  ]\u001b[22m\n\u001b[39m\u001b[90m\n\nNumber of calls: \u001b[1m1\u001b[22m\n\u001b[39m\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1355:10)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/router/guards.test.js:264:20\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected \"spy\" to be called with arguments: [ { path: '/unauthorized' } ]\u001b[90m\n\nReceived: \n\n\u001b[1m  1st spy call:\n\n\u001b[22m\u001b[2m  [\u001b[22m\n\u001b[2m    {\u001b[22m\n\u001b[32m-     \"path\": \"/unauthorized\",\u001b[90m\n\u001b[31m+     \"path\": \"/login\",\u001b[90m\n\u001b[31m+     \"query\": {\u001b[90m\n\u001b[31m+       \"redirect\": \"/admin\",\u001b[90m\n\u001b[31m+     },\u001b[90m\n\u001b[2m    },\u001b[22m\n\u001b[2m  ]\u001b[22m\n\u001b[39m\u001b[90m\n\nNumber of calls: \u001b[1m1\u001b[22m\n\u001b[39m\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1355:10)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/router/guards.test.js:264:20\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected \"spy\" to be called with arguments: [ { path: '/unauthorized' } ]\u001b[90m\n\nReceived: \n\n\u001b[1m  1st spy call:\n\n\u001b[22m\u001b[2m  [\u001b[22m\n\u001b[2m    {\u001b[22m\n\u001b[32m-     \"path\": \"/unauthorized\",\u001b[90m\n\u001b[31m+     \"path\": \"/login\",\u001b[90m\n\u001b[31m+     \"query\": {\u001b[90m\n\u001b[31m+       \"redirect\": \"/admin\",\u001b[90m\n\u001b[31m+     },\u001b[90m\n\u001b[2m    },\u001b[22m\n\u001b[2m  ]\u001b[22m\n\u001b[39m\u001b[90m\n\nNumber of calls: \u001b[1m1\u001b[22m\n\u001b[39m\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1355:10)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/router/guards.test.js:264:20\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 253, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Public Routes"], "fullName": "Route Guards (TEST-003) Public Routes should allow access to public routes regardless of authentication status", "status": "passed", "title": "should allow access to public routes regardless of authentication status", "duration": 0.35595399999874644, "failureMessages": [], "location": {"line": 269, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Public Routes"], "fullName": "Route Guards (TEST-003) Public Routes should allow authenticated users to access public routes", "status": "passed", "title": "should allow authenticated users to access public routes", "duration": 0.33378600000105507, "failureMessages": [], "location": {"line": 282, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Erro<PERSON>"], "fullName": "Route Guards (TEST-003) Error Handling should handle auth initialization errors gracefully", "status": "failed", "title": "should handle auth initialization errors gracefully", "duration": 1.5125910000006115, "failureMessages": ["Error: Auth initialization failed\n    at /Volumes/External Drive/Development/track-tasks/test/unit/router/guards.test.js:303:54\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)", "Error: Auth initialization failed\n    at /Volumes/External Drive/Development/track-tasks/test/unit/router/guards.test.js:303:54\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)", "Error: Auth initialization failed\n    at /Volumes/External Drive/Development/track-tasks/test/unit/router/guards.test.js:303:54\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)"], "location": {"line": 297, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Route Meta Combinations"], "fullName": "Route Guards (TEST-003) Route Meta Combinations should handle routes with multiple meta requirements", "status": "passed", "title": "should handle routes with multiple meta requirements", "duration": 0.31271400000150607, "failureMessages": [], "location": {"line": 314, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Route Meta Combinations"], "fullName": "Route Guards (TEST-003) Route Meta Combinations should prioritize authentication check over admin check", "status": "passed", "title": "should prioritize authentication check over admin check", "duration": 0.4334149999995134, "failureMessages": [], "location": {"line": 331, "column": 7}, "meta": {}}], "startTime": 1752958014705, "endTime": 1752958014734.4333, "status": "failed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/unit/router/guards.test.js"}, {"assertionResults": [{"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Login API"], "fullName": "Authentication API Middleware (TEST-004) Login API should call PocketBase login with correct parameters", "status": "passed", "title": "should call PocketBase login with correct parameters", "duration": 4.10562199999913, "failureMessages": [], "location": {"line": 73, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Login API"], "fullName": "Authentication API Middleware (TEST-004) Login API should handle login API errors", "status": "passed", "title": "should handle login API errors", "duration": 2.1271899999992456, "failureMessages": [], "location": {"line": 88, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Login API"], "fullName": "Authentication API Middleware (TEST-004) Login API should validate input parameters", "status": "passed", "title": "should validate input parameters", "duration": 0.6021080000009533, "failureMessages": [], "location": {"line": 95, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Registration API"], "fullName": "Authentication API Middleware (TEST-004) Registration API should call PocketBase register with correct parameters", "status": "passed", "title": "should call PocketBase register with correct parameters", "duration": 0.8366509999996197, "failureMessages": [], "location": {"line": 105, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Registration API"], "fullName": "Authentication API Middleware (TEST-004) Registration API should handle registration API errors", "status": "passed", "title": "should handle registration API errors", "duration": 0.44154100000014296, "failureMessages": [], "location": {"line": 124, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Registration API"], "fullName": "Authentication API Middleware (TEST-004) Registration API should validate registration data", "status": "passed", "title": "should validate registration data", "duration": 7.6440569999995205, "failureMessages": [], "location": {"line": 132, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Token Refresh API"], "fullName": "Authentication API Middleware (TEST-004) Token Refresh API should call PocketBase refreshToken", "status": "passed", "title": "should call PocketBase refreshToken", "duration": 0.5499060000001919, "failureMessages": [], "location": {"line": 140, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Token Refresh API"], "fullName": "Authentication API Middleware (TEST-004) Token Refresh API should handle token refresh API errors", "status": "passed", "title": "should handle token refresh API errors", "duration": 0.4571830000004411, "failureMessages": [], "location": {"line": 155, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Token Refresh API"], "fullName": "Authentication API Middleware (TEST-004) Token Refresh API should handle expired refresh tokens", "status": "passed", "title": "should handle expired refresh tokens", "duration": 0.39132400000016787, "failureMessages": [], "location": {"line": 162, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Password Reset API"], "fullName": "Authentication API Middleware (TEST-004) Password Reset API should call PocketBase requestPasswordReset", "status": "passed", "title": "should call PocketBase requestPasswordReset", "duration": 0.411259999998947, "failureMessages": [], "location": {"line": 174, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Password Reset API"], "fullName": "Authentication API Middleware (TEST-004) Password Reset API should call PocketBase resetPassword", "status": "passed", "title": "should call PocketBase resetPassword", "duration": 0.4350040000008448, "failureMessages": [], "location": {"line": 184, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Password Reset API"], "fullName": "Authentication API Middleware (TEST-004) Password Reset API should validate email format for password reset", "status": "failed", "title": "should validate email format for password reset", "duration": 28.847853000001123, "failureMessages": ["Error: promise resolved \"{ success: true }\" instead of rejecting\n    at Assertion.__VITEST_REJECTS__ (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1610:17)\n    at Assertion.propertyGetter (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1486:27)\n    at Reflect.get (<anonymous>)\n    at Object.proxyGetter [as get] (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1577:22)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/auth-middleware.test.js:196:70\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)", "Error: promise resolved \"{ success: true }\" instead of rejecting\n    at Assertion.__VITEST_REJECTS__ (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1610:17)\n    at Assertion.propertyGetter (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1486:27)\n    at Reflect.get (<anonymous>)\n    at Object.proxyGetter [as get] (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1577:22)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/auth-middleware.test.js:196:70\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)", "Error: promise resolved \"{ success: true }\" instead of rejecting\n    at Assertion.__VITEST_REJECTS__ (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1610:17)\n    at Assertion.propertyGetter (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1486:27)\n    at Reflect.get (<anonymous>)\n    at Object.proxyGetter [as get] (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1577:22)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/auth-middleware.test.js:196:70\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)"], "location": {"line": 195, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Password Reset API"], "fullName": "Authentication API Middleware (TEST-004) Password Reset API should validate reset token and password", "status": "failed", "title": "should validate reset token and password", "duration": 2.1444849999988946, "failureMessages": ["Error: promise resolved \"{ success: true }\" instead of rejecting\n    at Assertion.__VITEST_REJECTS__ (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1610:17)\n    at Assertion.propertyGetter (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1486:27)\n    at Reflect.get (<anonymous>)\n    at Object.proxyGetter [as get] (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1577:22)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/auth-middleware.test.js:201:59\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)", "Error: promise resolved \"{ success: true }\" instead of rejecting\n    at Assertion.__VITEST_REJECTS__ (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1610:17)\n    at Assertion.propertyGetter (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1486:27)\n    at Reflect.get (<anonymous>)\n    at Object.proxyGetter [as get] (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1577:22)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/auth-middleware.test.js:201:59\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)", "Error: promise resolved \"{ success: true }\" instead of rejecting\n    at Assertion.__VITEST_REJECTS__ (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1610:17)\n    at Assertion.propertyGetter (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1486:27)\n    at Reflect.get (<anonymous>)\n    at Object.proxyGetter [as get] (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1577:22)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/auth-middleware.test.js:201:59\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)"], "location": {"line": 199, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Profile Management API"], "fullName": "Authentication API Middleware (TEST-004) Profile Management API should call PocketBase updateProfile", "status": "passed", "title": "should call PocketBase updateProfile", "duration": 0.3910710000000108, "failureMessages": [], "location": {"line": 206, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Profile Management API"], "fullName": "Authentication API Middleware (TEST-004) Profile Management API should call PocketBase changePassword", "status": "passed", "title": "should call PocketBase changePassword", "duration": 0.33433700000023236, "failureMessages": [], "location": {"line": 220, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Profile Management API"], "fullName": "Authentication API Middleware (TEST-004) Profile Management API should validate profile update data", "status": "failed", "title": "should validate profile update data", "duration": 9.399821999999403, "failureMessages": ["Error: promise resolved \"{ success: true, …(1) }\" instead of rejecting\n    at Assertion.__VITEST_REJECTS__ (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1610:17)\n    at Assertion.propertyGetter (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1486:27)\n    at Reflect.get (<anonymous>)\n    at Object.proxyGetter [as get] (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1577:22)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/auth-middleware.test.js:233:59\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)", "Error: promise resolved \"{ success: true, …(1) }\" instead of rejecting\n    at Assertion.__VITEST_REJECTS__ (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1610:17)\n    at Assertion.propertyGetter (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1486:27)\n    at Reflect.get (<anonymous>)\n    at Object.proxyGetter [as get] (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1577:22)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/auth-middleware.test.js:233:59\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)", "Error: promise resolved \"{ success: true, …(1) }\" instead of rejecting\n    at Assertion.__VITEST_REJECTS__ (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1610:17)\n    at Assertion.propertyGetter (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1486:27)\n    at Reflect.get (<anonymous>)\n    at Object.proxyGetter [as get] (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1577:22)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/auth-middleware.test.js:233:59\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)"], "location": {"line": 231, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Profile Management API"], "fullName": "Authentication API Middleware (TEST-004) Profile Management API should validate password change data", "status": "failed", "title": "should validate password change data", "duration": 1.9992309999997815, "failureMessages": ["Error: promise resolved \"{ success: true }\" instead of rejecting\n    at Assertion.__VITEST_REJECTS__ (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1610:17)\n    at Assertion.propertyGetter (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1486:27)\n    at Reflect.get (<anonymous>)\n    at Object.proxyGetter [as get] (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1577:22)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/auth-middleware.test.js:238:60\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)", "Error: promise resolved \"{ success: true }\" instead of rejecting\n    at Assertion.__VITEST_REJECTS__ (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1610:17)\n    at Assertion.propertyGetter (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1486:27)\n    at Reflect.get (<anonymous>)\n    at Object.proxyGetter [as get] (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1577:22)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/auth-middleware.test.js:238:60\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)", "Error: promise resolved \"{ success: true }\" instead of rejecting\n    at Assertion.__VITEST_REJECTS__ (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1610:17)\n    at Assertion.propertyGetter (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1486:27)\n    at Reflect.get (<anonymous>)\n    at Object.proxyGetter [as get] (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1577:22)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/auth-middleware.test.js:238:60\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)"], "location": {"line": 236, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Authentication Status API"], "fullName": "Authentication API Middleware (TEST-004) Authentication Status API should call PocketBase isAuthenticated", "status": "passed", "title": "should call PocketBase isAuthenticated", "duration": 2.6600280000002385, "failureMessages": [], "location": {"line": 243, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Authentication Status API"], "fullName": "Authentication API Middleware (TEST-004) Authentication Status API should call PocketBase getCurrentUser", "status": "passed", "title": "should call PocketBase getCurrentUser", "duration": 0.30701200000112294, "failureMessages": [], "location": {"line": 252, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Authentication Status API"], "fullName": "Authentication API Middleware (TEST-004) Authentication Status API should call PocketBase getToken", "status": "passed", "title": "should call PocketBase getToken", "duration": 0.22204099999908067, "failureMessages": [], "location": {"line": 265, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Authentication Status API"], "fullName": "Authentication API Middleware (TEST-004) Authentication Status API should handle authentication check errors", "status": "passed", "title": "should handle authentication check errors", "duration": 0.3152769999996963, "failureMessages": [], "location": {"line": 275, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Logout API"], "fullName": "Authentication API Middleware (TEST-004) Logout API should call PocketBase logout", "status": "passed", "title": "should call PocketBase logout", "duration": 0.2523459999993065, "failureMessages": [], "location": {"line": 283, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Logout API"], "fullName": "Authentication API Middleware (TEST-004) Logout API should handle logout API errors", "status": "passed", "title": "should handle logout API errors", "duration": 0.291665999999168, "failureMessages": [], "location": {"line": 293, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Logout API"], "fullName": "Authentication API Middleware (TEST-004) Logout API should handle logout when not authenticated", "status": "failed", "title": "should handle logout when not authenticated", "duration": 0.7290890000003856, "failureMessages": ["Error: <PERSON><PERSON><PERSON> failed\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/auth-middleware.test.js:294:25\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)", "Error: <PERSON><PERSON><PERSON> failed\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/auth-middleware.test.js:294:25\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)", "Error: <PERSON><PERSON><PERSON> failed\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/auth-middleware.test.js:294:25\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)"], "location": {"line": 300, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "API Response Handling"], "fullName": "Authentication API Middleware (TEST-004) API Response Handling should handle malformed API responses", "status": "failed", "title": "should handle malformed API responses", "duration": 1.3831600000012259, "failureMessages": ["Error: promise resolved \"null\" instead of rejecting\n    at Assertion.__VITEST_REJECTS__ (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1610:17)\n    at Assertion.propertyGetter (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1486:27)\n    at Reflect.get (<anonymous>)\n    at Object.proxyGetter [as get] (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1577:22)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/auth-middleware.test.js:313:73\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)", "Error: promise resolved \"null\" instead of rejecting\n    at Assertion.__VITEST_REJECTS__ (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1610:17)\n    at Assertion.propertyGetter (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1486:27)\n    at Reflect.get (<anonymous>)\n    at Object.proxyGetter [as get] (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1577:22)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/auth-middleware.test.js:313:73\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)", "Error: promise resolved \"null\" instead of rejecting\n    at Assertion.__VITEST_REJECTS__ (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1610:17)\n    at Assertion.propertyGetter (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1486:27)\n    at Reflect.get (<anonymous>)\n    at Object.proxyGetter [as get] (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1577:22)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/auth-middleware.test.js:313:73\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)"], "location": {"line": 310, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "API Response Handling"], "fullName": "Authentication API Middleware (TEST-004) API Response Handling should handle network timeouts", "status": "passed", "title": "should handle network timeouts", "duration": 108.21334500000012, "failureMessages": [], "location": {"line": 316, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "API Response Handling"], "fullName": "Authentication API Middleware (TEST-004) API Response Handling should handle rate limiting", "status": "passed", "title": "should handle rate limiting", "duration": 0.38978099999985716, "failureMessages": [], "location": {"line": 326, "column": 7}, "meta": {}}], "startTime": 1752958015567, "endTime": 1752958015744.39, "status": "failed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/unit/middleware/auth-middleware.test.js"}, {"assertionResults": [{"ancestorTitles": ["CSRF Middleware", "generateCSRFToken"], "fullName": "CSRF Middleware generateCSRFToken should generate and set CSRF token in cookie and header", "status": "passed", "title": "should generate and set CSRF token in cookie and header", "duration": 30.17431800000122, "failureMessages": [], "location": {"line": 38, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "generateCSRFToken"], "fullName": "CSRF Middleware generateCSRFToken should set secure cookie in production", "status": "passed", "title": "should set secure cookie in production", "duration": 6.496788999998898, "failureMessages": [], "location": {"line": 53, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "validateCSRFToken"], "fullName": "CSRF Middleware validateCSRFToken should skip validation for GET requests", "status": "passed", "title": "should skip validation for GET requests", "duration": 3.964262999999846, "failureMessages": [], "location": {"line": 70, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "validateCSRFToken"], "fullName": "CSRF Middleware validateCSRFToken should skip validation for HEAD requests", "status": "passed", "title": "should skip validation for HEAD requests", "duration": 4.120850999999675, "failureMessages": [], "location": {"line": 79, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "validateCSRFToken"], "fullName": "CSRF Middleware validateCSRFToken should skip validation for OPTIONS requests", "status": "passed", "title": "should skip validation for OPTIONS requests", "duration": 4.259851999999228, "failureMessages": [], "location": {"line": 88, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "validateCSRFToken"], "fullName": "CSRF Middleware validateCSRFToken should require CSRF token for POST requests", "status": "failed", "title": "should require CSRF token for POST requests", "duration": 79.11891199999991, "failureMessages": ["AssertionError: expected undefined to be false // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/csrf.test.js:104:37\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected undefined to be false // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/csrf.test.js:104:37\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected undefined to be false // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/csrf.test.js:104:37\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 97, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "validateCSRFToken"], "fullName": "CSRF Middleware validateCSRFToken should validate CSRF token from header", "status": "failed", "title": "should validate CSRF token from header", "duration": 50.965416999999434, "failureMessages": ["AssertionError: expected 403 to be 200 // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/csrf.test.js:128:31\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected 403 to be 200 // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/csrf.test.js:128:31\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected 403 to be 200 // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/csrf.test.js:128:31\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 108, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "validateCSRFToken"], "fullName": "CSRF Middleware validateCSRFToken should reject invalid CSRF token", "status": "failed", "title": "should reject invalid CSRF token", "duration": 20.01212300000043, "failureMessages": ["TypeError: Cannot read properties of undefined (reading 'message')\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/csrf.test.js:142:34\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "TypeError: Cannot read properties of undefined (reading 'message')\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/csrf.test.js:142:34\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "TypeError: Cannot read properties of undefined (reading 'message')\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/csrf.test.js:142:34\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 132, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "csrfProtection"], "fullName": "CSRF Middleware csrfProtection should protect authentication endpoints", "status": "passed", "title": "should protect authentication endpoints", "duration": 3.5238090000002558, "failureMessages": [], "location": {"line": 147, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "csrfProtection"], "fullName": "CSRF Middleware csrfProtection should protect registration endpoints", "status": "passed", "title": "should protect registration endpoints", "duration": 3.0394410000008065, "failureMessages": [], "location": {"line": 156, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "csrfProtection"], "fullName": "CSRF Middleware csrfProtection should not protect non-authentication endpoints", "status": "passed", "title": "should not protect non-authentication endpoints", "duration": 2.726996000001236, "failureMessages": [], "location": {"line": 165, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "csrfProtection"], "fullName": "CSRF Middleware csrfProtection should not protect GET requests to auth endpoints", "status": "passed", "title": "should not protect GET requests to auth endpoints", "duration": 2.488459000000148, "failureMessages": [], "location": {"line": 174, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "getCSRFToken"], "fullName": "CSRF Middleware getCSRFToken should return null for non-existent session", "status": "passed", "title": "should return null for non-existent session", "duration": 0.4173609999998007, "failureMessages": [], "location": {"line": 185, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "cleanupExpiredTokens"], "fullName": "CSRF Middleware cleanupExpiredTokens should clean up expired tokens", "status": "passed", "title": "should clean up expired tokens", "duration": 2.994155000000319, "failureMessages": [], "location": {"line": 193, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "CSRF token endpoint"], "fullName": "CSRF Middleware CSRF token endpoint should provide CSRF token via API endpoint", "status": "passed", "title": "should provide CSRF token via API endpoint", "duration": 3.1619009999994887, "failureMessages": [], "location": {"line": 201, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "Integration with authentication flow"], "fullName": "CSRF Middleware Integration with authentication flow should work with complete authentication flow", "status": "failed", "title": "should work with complete authentication flow", "duration": 40.57448999999906, "failureMessages": ["AssertionError: expected 403 to be 200 // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/csrf.test.js:247:36\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected 403 to be 200 // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/csrf.test.js:247:36\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected 403 to be 200 // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/csrf.test.js:247:36\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 216, "column": 5}, "meta": {}}], "startTime": 1752958016968, "endTime": 1752958017226.5745, "status": "failed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/unit/middleware/csrf.test.js"}, {"assertionResults": [{"ancestorTitles": ["Password Reset Middleware", "storeResetToken"], "fullName": "Password Reset Middleware storeResetToken should store reset token with expiration", "status": "passed", "title": "should store reset token with expiration", "duration": 23.03347699999904, "failureMessages": [], "location": {"line": 39, "column": 7}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "storeResetToken"], "fullName": "Password Reset Middleware storeResetToken should use default expiration time", "status": "passed", "title": "should use default expiration time", "duration": 0.7189190000008239, "failureMessages": [], "location": {"line": 55, "column": 7}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "validateResetToken"], "fullName": "Password Reset Middleware validateResetToken should validate valid token", "status": "passed", "title": "should validate valid token", "duration": 3.308919999999489, "failureMessages": [], "location": {"line": 66, "column": 7}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "validateResetToken"], "fullName": "Password Reset Middleware validateResetToken should reject non-existent token", "status": "passed", "title": "should reject non-existent token", "duration": 0.7926650000008522, "failureMessages": [], "location": {"line": 78, "column": 7}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "validateResetToken"], "fullName": "Password Reset Middleware validateResetToken should reject expired token", "status": "passed", "title": "should reject expired token", "duration": 0.46910899999966205, "failureMessages": [], "location": {"line": 85, "column": 7}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "validateResetToken"], "fullName": "Password Reset Middleware validateResetToken should reject used token", "status": "passed", "title": "should reject used token", "duration": 0.6337180000009539, "failureMessages": [], "location": {"line": 100, "column": 7}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "validateResetToken"], "fullName": "Password Reset Middleware validateResetToken should increment attempt counter", "status": "passed", "title": "should increment attempt counter", "duration": 0.5139829999989161, "failureMessages": [], "location": {"line": 113, "column": 7}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "validateResetToken"], "fullName": "Password Reset Middleware validateResetToken should reject token after too many attempts", "status": "passed", "title": "should reject token after too many attempts", "duration": 1.2229799999986426, "failureMessages": [], "location": {"line": 126, "column": 7}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "markTokenAsUsed"], "fullName": "Password Reset Middleware markTokenAsUsed should mark token as used", "status": "passed", "title": "should mark token as used", "duration": 1.1217219999998633, "failureMessages": [], "location": {"line": 146, "column": 7}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "markTokenAsUsed"], "fullName": "Password Reset Middleware markTokenAsUsed should handle non-existent token gracefully", "status": "passed", "title": "should handle non-existent token gracefully", "duration": 2.011614000000918, "failureMessages": [], "location": {"line": 157, "column": 7}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "invalidateEmailResetTokens"], "fullName": "Password Reset Middleware invalidateEmailResetTokens should invalidate existing tokens for email", "status": "passed", "title": "should invalidate existing tokens for email", "duration": 0.6196450000006735, "failureMessages": [], "location": {"line": 163, "column": 7}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "passwordResetRateLimit"], "fullName": "Password Reset Middleware passwordResetRateLimit should allow requests within rate limit", "status": "passed", "title": "should allow requests within rate limit", "duration": 105.79005499999948, "failureMessages": [], "location": {"line": 185, "column": 7}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "passwordResetRateLimit"], "fullName": "Password Reset Middleware passwordResetRateLimit should block requests exceeding rate limit", "status": "failed", "title": "should block requests exceeding rate limit", "duration": 64.96238300000005, "failureMessages": ["AssertionError: expected 200 to be 429 // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/passwordReset.test.js:215:31\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected 200 to be 429 // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/passwordReset.test.js:215:31\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected 200 to be 429 // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/passwordReset.test.js:215:31\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 198, "column": 7}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "emailResetRateLimit"], "fullName": "Password Reset Middleware emailResetRateLimit should allow requests for different emails", "status": "passed", "title": "should allow requests for different emails", "duration": 6.877492999999959, "failureMessages": [], "location": {"line": 222, "column": 7}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "emailResetRateLimit"], "fullName": "Password Reset Middleware emailResetRateLimit should block excessive requests for same email", "status": "failed", "title": "should block excessive requests for same email", "duration": 55.24832500000048, "failureMessages": ["AssertionError: expected 200 to be 429 // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/passwordReset.test.js:261:31\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected 200 to be 429 // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/passwordReset.test.js:261:31\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected 200 to be 429 // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/passwordReset.test.js:261:31\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 240, "column": 7}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "validateResetTokenMiddleware"], "fullName": "Password Reset Middleware validateResetTokenMiddleware should validate token and attach data to request", "status": "passed", "title": "should validate token and attach data to request", "duration": 2.9765880000013567, "failureMessages": [], "location": {"line": 266, "column": 7}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "validateResetTokenMiddleware"], "fullName": "Password Reset Middleware validateResetTokenMiddleware should reject request with invalid token", "status": "passed", "title": "should reject request with invalid token", "duration": 5.56629299999986, "failureMessages": [], "location": {"line": 290, "column": 7}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "validateResetTokenMiddleware"], "fullName": "Password Reset Middleware validateResetTokenMiddleware should reject request without token", "status": "passed", "title": "should reject request without token", "duration": 2.633864999999787, "failureMessages": [], "location": {"line": 302, "column": 7}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "progressiveDelayMiddleware"], "fullName": "Password Reset Middleware progressiveDelayMiddleware should not delay first request", "status": "passed", "title": "should not delay first request", "duration": 4.46657100000084, "failureMessages": [], "location": {"line": 316, "column": 7}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "progressiveDelayMiddleware"], "fullName": "Password Reset Middleware progressiveDelayMiddleware should handle missing email gracefully", "status": "passed", "title": "should handle missing email gracefully", "duration": 2.1158050000012736, "failureMessages": [], "location": {"line": 331, "column": 7}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "cleanupExpiredData"], "fullName": "Password Reset Middleware cleanupExpiredData should clean up expired tokens", "status": "passed", "title": "should clean up expired tokens", "duration": 0.3120870000002469, "failureMessages": [], "location": {"line": 345, "column": 7}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "cleanupExpiredData"], "fullName": "Password Reset Middleware cleanupExpiredData should not throw errors during cleanup", "status": "passed", "title": "should not throw errors during cleanup", "duration": 0.6538590000000113, "failureMessages": [], "location": {"line": 362, "column": 7}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "getResetTokenStats"], "fullName": "Password Reset Middleware getResetTokenStats should return correct statistics", "status": "passed", "title": "should return correct statistics", "duration": 0.8727099999996426, "failureMessages": [], "location": {"line": 368, "column": 7}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "Integration tests"], "fullName": "Password Reset Middleware Integration tests should handle complete password reset flow", "status": "passed", "title": "should handle complete password reset flow", "duration": 0.3932810000005702, "failureMessages": [], "location": {"line": 393, "column": 7}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "Integration tests"], "fullName": "Password Reset Middleware Integration tests should handle token expiration correctly", "status": "passed", "title": "should handle token expiration correctly", "duration": 0.33689199999935227, "failureMessages": [], "location": {"line": 413, "column": 7}, "meta": {}}], "startTime": 1752958017021, "endTime": 1752958017309.3933, "status": "failed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/unit/middleware/passwordReset.test.js"}, {"assertionResults": [{"ancestorTitles": ["Authentication Routes", "POST /api/auth/login"], "fullName": "Authentication Routes POST /api/auth/login should login user with valid credentials", "status": "passed", "title": "should login user with valid credentials", "duration": 225.28125, "failureMessages": [], "location": {"line": 201, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/login"], "fullName": "Authentication Routes POST /api/auth/login should return validation error for invalid email", "status": "passed", "title": "should return validation error for invalid email", "duration": 6.654096999998728, "failureMessages": [], "location": {"line": 232, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/login"], "fullName": "Authentication Routes POST /api/auth/login should return validation error for missing password", "status": "passed", "title": "should return validation error for missing password", "duration": 5.4223930000007385, "failureMessages": [], "location": {"line": 245, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/register"], "fullName": "Authentication Routes POST /api/auth/register should register user with valid data", "status": "passed", "title": "should register user with valid data", "duration": 4.600185999999667, "failureMessages": [], "location": {"line": 259, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/register"], "fullName": "Authentication Routes POST /api/auth/register should return validation error for weak password", "status": "passed", "title": "should return validation error for weak password", "duration": 3.7412100000001374, "failureMessages": [], "location": {"line": 293, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/register"], "fullName": "Authentication Routes POST /api/auth/register should return validation error for password mismatch", "status": "passed", "title": "should return validation error for password mismatch", "duration": 3.1990310000001045, "failureMessages": [], "location": {"line": 308, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/logout"], "fullName": "Authentication Routes POST /api/auth/logout should logout user successfully", "status": "passed", "title": "should logout user successfully", "duration": 8.023709000000963, "failureMessages": [], "location": {"line": 325, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/refresh"], "fullName": "Authentication Routes POST /api/auth/refresh should refresh token successfully", "status": "passed", "title": "should refresh token successfully", "duration": 4.192167999999583, "failureMessages": [], "location": {"line": 343, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/refresh"], "fullName": "Authentication Routes POST /api/auth/refresh should return validation error for missing refresh token", "status": "passed", "title": "should return validation error for missing refresh token", "duration": 3.624791999998706, "failureMessages": [], "location": {"line": 363, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/forgot-password"], "fullName": "Authentication Routes POST /api/auth/forgot-password should send password reset email", "status": "passed", "title": "should send password reset email", "duration": 6.905308999999761, "failureMessages": [], "location": {"line": 375, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/forgot-password"], "fullName": "Authentication Routes POST /api/auth/forgot-password should return validation error for invalid email", "status": "passed", "title": "should return validation error for invalid email", "duration": 3.017009000001053, "failureMessages": [], "location": {"line": 393, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/reset-password"], "fullName": "Authentication Routes POST /api/auth/reset-password should reset password successfully", "status": "passed", "title": "should reset password successfully", "duration": 2.9092919999984588, "failureMessages": [], "location": {"line": 407, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/reset-password"], "fullName": "Authentication Routes POST /api/auth/reset-password should return validation error for missing token", "status": "passed", "title": "should return validation error for missing token", "duration": 2.8919179999993503, "failureMessages": [], "location": {"line": 427, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "GET /api/auth/me"], "fullName": "Authentication Routes GET /api/auth/me should get current user profile", "status": "passed", "title": "should get current user profile", "duration": 4.860313999999562, "failureMessages": [], "location": {"line": 442, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "PUT /api/auth/profile"], "fullName": "Authentication Routes PUT /api/auth/profile should update user profile", "status": "passed", "title": "should update user profile", "duration": 3.51234999999906, "failureMessages": [], "location": {"line": 467, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "PUT /api/auth/profile"], "fullName": "Authentication Routes PUT /api/auth/profile should return validation error for invalid email", "status": "passed", "title": "should return validation error for invalid email", "duration": 6.476934000000256, "failureMessages": [], "location": {"line": 499, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "PUT /api/auth/password"], "fullName": "Authentication Routes PUT /api/auth/password should change password successfully", "status": "passed", "title": "should change password successfully", "duration": 3.061028999998598, "failureMessages": [], "location": {"line": 514, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "PUT /api/auth/password"], "fullName": "Authentication Routes PUT /api/auth/password should return validation error for password mismatch", "status": "passed", "title": "should return validation error for password mismatch", "duration": 3.1281490000001213, "failureMessages": [], "location": {"line": 540, "column": 5}, "meta": {}}], "startTime": 1752958014857, "endTime": 1752958015159.1282, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/unit/routes/auth.test.js"}, {"assertionResults": [{"ancestorTitles": ["Authentication Composable (TEST-002)"], "fullName": "Authentication Composable (TEST-002) should be properly configured", "status": "passed", "title": "should be properly configured", "duration": 2.383711000000403, "failureMessages": [], "location": {"line": 149, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "State Access"], "fullName": "Authentication Composable (TEST-002) State Access should provide access to authentication state", "status": "passed", "title": "should provide access to authentication state", "duration": 0.6782669999993232, "failureMessages": [], "location": {"line": 154, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "State Access"], "fullName": "Authentication Composable (TEST-002) State Access should reflect changes in authentication state", "status": "passed", "title": "should reflect changes in authentication state", "duration": 2.0354889999998704, "failureMessages": [], "location": {"line": 164, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Login Function"], "fullName": "Authentication Composable (TEST-002) Login Function should login and redirect on success", "status": "passed", "title": "should login and redirect on success", "duration": 2.8706380000003264, "failureMessages": [], "location": {"line": 178, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Login Function"], "fullName": "Authentication Composable (TEST-002) Login Function should not redirect on login failure", "status": "passed", "title": "should not redirect on login failure", "duration": 1.0966389999994135, "failureMessages": [], "location": {"line": 191, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Register Function"], "fullName": "Authentication Composable (TEST-002) Register Function should register and redirect to login on success", "status": "passed", "title": "should register and redirect to login on success", "duration": 0.6041050000003452, "failureMessages": [], "location": {"line": 206, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Register Function"], "fullName": "Authentication Composable (TEST-002) Register Function should not redirect on registration failure", "status": "passed", "title": "should not redirect on registration failure", "duration": 1.8866129999987606, "failureMessages": [], "location": {"line": 219, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Logout Function"], "fullName": "Authentication Composable (TEST-002) Logout Function should logout and redirect to login", "status": "passed", "title": "should logout and redirect to login", "duration": 0.4938529999999446, "failureMessages": [], "location": {"line": 233, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Token Refresh Function"], "fullName": "Authentication Composable (TEST-002) Token Refresh Function should refresh token", "status": "passed", "title": "should refresh token", "duration": 0.39457600000059756, "failureMessages": [], "location": {"line": 246, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Password Reset Functions"], "fullName": "Authentication Composable (TEST-002) Password Reset Functions should request password reset", "status": "passed", "title": "should request password reset", "duration": 0.40068500000052154, "failureMessages": [], "location": {"line": 259, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Password Reset Functions"], "fullName": "Authentication Composable (TEST-002) Password Reset Functions should reset password and redirect to login on success", "status": "passed", "title": "should reset password and redirect to login on success", "duration": 0.5308949999998731, "failureMessages": [], "location": {"line": 271, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Profile Management Functions"], "fullName": "Authentication Composable (TEST-002) Profile Management Functions should update profile", "status": "passed", "title": "should update profile", "duration": 0.3861700000015844, "failureMessages": [], "location": {"line": 286, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Profile Management Functions"], "fullName": "Authentication Composable (TEST-002) Profile Management Functions should change password", "status": "passed", "title": "should change password", "duration": 0.34266999999999825, "failureMessages": [], "location": {"line": 298, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Route Guards"], "fullName": "Authentication Composable (TEST-002) Route Guards should allow access when authenticated (requireAuth)", "status": "passed", "title": "should allow access when authenticated (requireAuth)", "duration": 0.23841800000082003, "failureMessages": [], "location": {"line": 312, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Route Guards"], "fullName": "Authentication Composable (TEST-002) Route Guards should redirect to login when not authenticated (requireAuth)", "status": "passed", "title": "should redirect to login when not authenticated (requireAuth)", "duration": 0.24415099999896483, "failureMessages": [], "location": {"line": 322, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Route Guards"], "fullName": "Authentication Composable (TEST-002) Route Guards should allow access when not authenticated (requireGuest)", "status": "passed", "title": "should allow access when not authenticated (requireGuest)", "duration": 0.22240600000077393, "failureMessages": [], "location": {"line": 332, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Route Guards"], "fullName": "Authentication Composable (TEST-002) Route Guards should redirect to home when authenticated (requireGuest)", "status": "passed", "title": "should redirect to home when authenticated (requireGuest)", "duration": 0.21697499999936554, "failureMessages": [], "location": {"line": 342, "column": 7}, "meta": {}}], "startTime": 1752958016803, "endTime": 1752958016819.2441, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/unit/composables/useAuth.test.js"}, {"assertionResults": [{"ancestorTitles": ["useSessionManagement", "initialization"], "fullName": "useSessionManagement initialization should initialize with empty sessions", "status": "passed", "title": "should initialize with empty sessions", "duration": 8.570064000000457, "failureMessages": [], "location": {"line": 63, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "fetchSessions"], "fullName": "useSessionManagement fetchSessions should fetch sessions successfully", "status": "passed", "title": "should fetch sessions successfully", "duration": 5.301899999999478, "failureMessages": [], "location": {"line": 81, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "fetchSessions"], "fullName": "useSessionManagement fetchSessions should handle fetch errors", "status": "passed", "title": "should handle fetch errors", "duration": 1.2833620000001247, "failureMessages": [], "location": {"line": 142, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "fetchSessions"], "fullName": "useSessionManagement fetchSessions should handle API errors", "status": "passed", "title": "should handle API errors", "duration": 5.642896999999721, "failureMessages": [], "location": {"line": 163, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "fetchSessions"], "fullName": "useSessionManagement fetchSessions should handle API response errors", "status": "passed", "title": "should handle API response errors", "duration": 1.993424999999661, "failureMessages": [], "location": {"line": 186, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "fetchSessions"], "fullName": "useSessionManagement fetchSessions should not fetch when unauthenticated", "status": "passed", "title": "should not fetch when unauthenticated", "duration": 1.0941350000002785, "failureMessages": [], "location": {"line": 211, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "terminateSession"], "fullName": "useSessionManagement terminateSession should terminate session successfully", "status": "passed", "title": "should terminate session successfully", "duration": 2.637792999999874, "failureMessages": [], "location": {"line": 225, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "terminateSession"], "fullName": "useSessionManagement terminateSession should handle termination errors", "status": "passed", "title": "should handle termination errors", "duration": 3.355105000000549, "failureMessages": [], "location": {"line": 289, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "terminateSession"], "fullName": "useSessionManagement terminateSession should handle API termination errors", "status": "passed", "title": "should handle API termination errors", "duration": 1.5594810000002326, "failureMessages": [], "location": {"line": 306, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "terminateAllOtherSessions"], "fullName": "useSessionManagement terminateAllOtherSessions should terminate all other sessions successfully", "status": "passed", "title": "should terminate all other sessions successfully", "duration": 1.199522999999317, "failureMessages": [], "location": {"line": 329, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "terminateAllOtherSessions"], "fullName": "useSessionManagement terminateAllOtherSessions should handle termination errors", "status": "passed", "title": "should handle termination errors", "duration": 0.8022929999997359, "failureMessages": [], "location": {"line": 381, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "refreshCurrentSession"], "fullName": "useSessionManagement refreshCurrentSession should refresh current session successfully", "status": "passed", "title": "should refresh current session successfully", "duration": 1.2965240000003178, "failureMessages": [], "location": {"line": 400, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "refreshCurrentSession"], "fullName": "useSessionManagement refreshCurrentSession should handle refresh errors", "status": "passed", "title": "should handle refresh errors", "duration": 0.6726730000000316, "failureMessages": [], "location": {"line": 431, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "computed properties"], "fullName": "useSessionManagement computed properties should compute active sessions correctly", "status": "passed", "title": "should compute active sessions correctly", "duration": 0.7214199999998527, "failureMessages": [], "location": {"line": 450, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "computed properties"], "fullName": "useSessionManagement computed properties should identify current session correctly", "status": "passed", "title": "should identify current session correctly", "duration": 0.8281550000001516, "failureMessages": [], "location": {"line": 482, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "computed properties"], "fullName": "useSessionManagement computed properties should compute other sessions correctly", "status": "passed", "title": "should compute other sessions correctly", "duration": 0.8831339999996999, "failureMessages": [], "location": {"line": 515, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "computed properties"], "fullName": "useSessionManagement computed properties should compute session statistics correctly", "status": "passed", "title": "should compute session statistics correctly", "duration": 1.1903710000005958, "failureMessages": [], "location": {"line": 548, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "utility functions"], "fullName": "useSessionManagement utility functions should detect device type correctly", "status": "passed", "title": "should detect device type correctly", "duration": 1.004402000000482, "failureMessages": [], "location": {"line": 587, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "utility functions"], "fullName": "useSessionManagement utility functions should detect browser name correctly", "status": "passed", "title": "should detect browser name correctly", "duration": 0.8011569999998756, "failureMessages": [], "location": {"line": 604, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "CSRF token handling"], "fullName": "useSessionManagement CSRF token handling should get CSRF token successfully", "status": "passed", "title": "should get CSRF token successfully", "duration": 0.762101000000257, "failureMessages": [], "location": {"line": 623, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "CSRF token handling"], "fullName": "useSessionManagement CSRF token handling should handle CSRF token errors gracefully", "status": "passed", "title": "should handle CSRF token errors gracefully", "duration": 0.764060999999856, "failureMessages": [], "location": {"line": 652, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "auto refresh"], "fullName": "useSessionManagement auto refresh should start auto refresh", "status": "passed", "title": "should start auto refresh", "duration": 1.0279039999995803, "failureMessages": [], "location": {"line": 673, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "auto refresh"], "fullName": "useSessionManagement auto refresh should stop auto refresh", "status": "passed", "title": "should stop auto refresh", "duration": 0.7382120000002033, "failureMessages": [], "location": {"line": 682, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "auto refresh"], "fullName": "useSessionManagement auto refresh should refresh sessions automatically", "status": "passed", "title": "should refresh sessions automatically", "duration": 1.3138980000003357, "failureMessages": [], "location": {"line": 693, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "error handling"], "fullName": "useSessionManagement error handling should clear errors", "status": "passed", "title": "should clear errors", "duration": 0.7780030000003535, "failureMessages": [], "location": {"line": 714, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "new session detection"], "fullName": "useSessionManagement new session detection should detect new sessions", "status": "passed", "title": "should detect new sessions", "duration": 0.8631580000001122, "failureMessages": [], "location": {"line": 737, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "new session detection"], "fullName": "useSessionManagement new session detection should handle no new sessions", "status": "passed", "title": "should handle no new sessions", "duration": 0.6690260000004855, "failureMessages": [], "location": {"line": 789, "column": 7}, "meta": {}}], "startTime": 1752958011885, "endTime": 1752958011934.669, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionManagement.test.js"}, {"assertionResults": [{"ancestorTitles": ["useSessionTimeout", "initialization"], "fullName": "useSessionTimeout initialization should initialize with default configuration", "status": "failed", "title": "should initialize with default configuration", "duration": 13.865112000001318, "failureMessages": ["AssertionError: expected true to be false // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionTimeout.test.js:104:30\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)", "AssertionError: expected true to be false // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionTimeout.test.js:104:30\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)", "AssertionError: expected true to be false // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionTimeout.test.js:104:30\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)"], "location": {"line": 93, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "initialization"], "fullName": "useSessionTimeout initialization should accept custom configuration", "status": "failed", "title": "should accept custom configuration", "duration": 4.484988999998677, "failureMessages": ["AssertionError: expected 3000000 to be greater than 3000000\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionTimeout.test.js:119:38\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)", "AssertionError: expected 3000000 to be greater than 3000000\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionTimeout.test.js:119:38\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)", "AssertionError: expected 3000000 to be greater than 3000000\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionTimeout.test.js:119:38\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)"], "location": {"line": 109, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "activity tracking"], "fullName": "useSessionTimeout activity tracking should register activity event listeners", "status": "failed", "title": "should register activity event listeners", "duration": 5.151288999999451, "failureMessages": ["AssertionError: expected false to be true // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionTimeout.test.js:132:43\n    at Array.forEach (<anonymous>)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionTimeout.test.js:131:22\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)", "AssertionError: expected false to be true // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionTimeout.test.js:132:43\n    at Array.forEach (<anonymous>)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionTimeout.test.js:131:22\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)", "AssertionError: expected false to be true // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionTimeout.test.js:132:43\n    at Array.forEach (<anonymous>)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionTimeout.test.js:131:22\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)"], "location": {"line": 124, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "activity tracking"], "fullName": "useSessionTimeout activity tracking should update activity timestamp on user activity", "status": "passed", "title": "should update activity timestamp on user activity", "duration": 5.4404019999983575, "failureMessages": [], "location": {"line": 137, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "activity tracking"], "fullName": "useSessionTimeout activity tracking should handle localStorage errors gracefully", "status": "passed", "title": "should handle localStorage errors gracefully", "duration": 1.5717720000011468, "failureMessages": [], "location": {"line": 154, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "warning system"], "fullName": "useSessionTimeout warning system should show warning when approaching timeout", "status": "passed", "title": "should show warning when approaching timeout", "duration": 1.4898339999999735, "failureMessages": [], "location": {"line": 166, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "warning system"], "fullName": "useSessionTimeout warning system should hide warning when activity is detected", "status": "passed", "title": "should hide warning when activity is detected", "duration": 0.953363000000536, "failureMessages": [], "location": {"line": 182, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "warning system"], "fullName": "useSessionTimeout warning system should format time remaining correctly", "status": "passed", "title": "should format time remaining correctly", "duration": 0.8211109999992914, "failureMessages": [], "location": {"line": 202, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "session extension"], "fullName": "useSessionTimeout session extension should extend session successfully", "status": "passed", "title": "should extend session successfully", "duration": 1.5171769999997196, "failureMessages": [], "location": {"line": 222, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "session extension"], "fullName": "useSessionTimeout session extension should handle session extension failure", "status": "passed", "title": "should handle session extension failure", "duration": 3.711063999999169, "failureMessages": [], "location": {"line": 242, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "session extension"], "fullName": "useSessionTimeout session extension should handle network errors during extension", "status": "passed", "title": "should handle network errors during extension", "duration": 1.0921300000009069, "failureMessages": [], "location": {"line": 257, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "session timeout handling"], "fullName": "useSessionTimeout session timeout handling should logout user when session times out", "status": "failed", "title": "should logout user when session times out", "duration": 3.967934000000241, "failureMessages": ["AssertionError: expected \"spy\" to be called at least once\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1335:10)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionTimeout.test.js:295:36\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected \"spy\" to be called at least once\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1335:10)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionTimeout.test.js:295:36\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected \"spy\" to be called at least once\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1335:10)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionTimeout.test.js:295:36\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 270, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "session timeout handling"], "fullName": "useSessionTimeout session timeout handling should redirect to login even if logout fails", "status": "failed", "title": "should redirect to login even if logout fails", "duration": 3.572928999999931, "failureMessages": ["AssertionError: expected \"spy\" to be called with arguments: [ { name: 'login' } ]\u001b[90m\n\nNumber of calls: \u001b[1m0\u001b[22m\n\u001b[39m\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1355:10)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionTimeout.test.js:326:31\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected \"spy\" to be called with arguments: [ { name: 'login' } ]\u001b[90m\n\nNumber of calls: \u001b[1m0\u001b[22m\n\u001b[39m\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1355:10)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionTimeout.test.js:326:31\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected \"spy\" to be called with arguments: [ { name: 'login' } ]\u001b[90m\n\nNumber of calls: \u001b[1m0\u001b[22m\n\u001b[39m\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1355:10)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionTimeout.test.js:326:31\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 302, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "cross-tab communication"], "fullName": "useSessionTimeout cross-tab communication should check for activity in other tabs", "status": "passed", "title": "should check for activity in other tabs", "duration": 1.8821260000004258, "failureMessages": [], "location": {"line": 331, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "cross-tab communication"], "fullName": "useSessionTimeout cross-tab communication should handle localStorage errors during cross-tab check", "status": "passed", "title": "should handle localStorage errors during cross-tab check", "duration": 1.3061670000006416, "failureMessages": [], "location": {"line": 345, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "cleanup"], "fullName": "useSessionTimeout cleanup should remove event listeners when stopped", "status": "failed", "title": "should remove event listeners when stopped", "duration": 3.7668479999992996, "failureMessages": ["TypeError: [Function removeEventListener] is not a spy or a call to a spy!\n    at assertIsMock (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1299:10)\n    at getSpy (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1303:3)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1307:15)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionTimeout.test.js:365:44\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)", "TypeError: [Function removeEventListener] is not a spy or a call to a spy!\n    at assertIsMock (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1299:10)\n    at getSpy (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1303:3)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1307:15)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionTimeout.test.js:365:44\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)", "TypeError: [Function removeEventListener] is not a spy or a call to a spy!\n    at assertIsMock (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1299:10)\n    at getSpy (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1303:3)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1307:15)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionTimeout.test.js:365:44\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)"], "location": {"line": 357, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "cleanup"], "fullName": "useSessionTimeout cleanup should clear timers when stopped", "status": "passed", "title": "should clear timers when stopped", "duration": 0.9889050000001589, "failureMessages": [], "location": {"line": 368, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "session status"], "fullName": "useSessionTimeout session status should provide comprehensive session status", "status": "passed", "title": "should provide comprehensive session status", "duration": 1.5577630000007048, "failureMessages": [], "location": {"line": 380, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "edge cases"], "fullName": "useSessionTimeout edge cases should handle unauthenticated state gracefully", "status": "failed", "title": "should handle unauthenticated state gracefully", "duration": 67.2395779999988, "failureMessages": ["AssertionError: expected true to be false // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionTimeout.test.js:422:30\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)", "AssertionError: expected true to be false // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionTimeout.test.js:422:30\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)", "AssertionError: expected true to be false // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionTimeout.test.js:422:30\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)"], "location": {"line": 405, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "edge cases"], "fullName": "useSessionTimeout edge cases should handle multiple start/stop cycles", "status": "passed", "title": "should handle multiple start/stop cycles", "duration": 1.1638199999997596, "failureMessages": [], "location": {"line": 425, "column": 7}, "meta": {}}], "startTime": 1752958016863, "endTime": 1752958016990.1638, "status": "failed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionTimeout.test.js"}, {"assertionResults": [], "startTime": 1752958004680, "endTime": 1752958004680, "status": "failed", "message": "Cannot access 'mockPocketBase' before initialization", "name": "/Volumes/External Drive/Development/track-tasks/test/unit/services/authService.test.js"}, {"assertionResults": [{"ancestorTitles": ["PocketBase Service", "Initialization"], "fullName": "PocketBase Service Initialization should initialize with default URL when no environment variable is set", "status": "passed", "title": "should initialize with default URL when no environment variable is set", "duration": 2.0389180000001943, "failureMessages": [], "location": {"line": 40, "column": 7}, "meta": {}}, {"ancestorTitles": ["PocketBase Service", "Initialization"], "fullName": "PocketBase Service Initialization should load auth state from cookies on initialization", "status": "passed", "title": "should load auth state from cookies on initialization", "duration": 0.41884400000162714, "failureMessages": [], "location": {"line": 45, "column": 7}, "meta": {}}, {"ancestorTitles": ["PocketBase Service", "Authentication Check"], "fullName": "PocketBase Service Authentication Check should return false when user is not authenticated", "status": "passed", "title": "should return false when user is not authenticated", "duration": 0.2511389999999665, "failureMessages": [], "location": {"line": 63, "column": 7}, "meta": {}}, {"ancestorTitles": ["PocketBase Service", "Authentication Check"], "fullName": "PocketBase Service Authentication Check should return true when user is authenticated", "status": "passed", "title": "should return true when user is authenticated", "duration": 0.21543299999939336, "failureMessages": [], "location": {"line": 68, "column": 7}, "meta": {}}, {"ancestorTitles": ["PocketBase Service", "Current User"], "fullName": "PocketBase Service Current User should return null when no user is authenticated", "status": "passed", "title": "should return null when no user is authenticated", "duration": 1.3563899999990099, "failureMessages": [], "location": {"line": 75, "column": 7}, "meta": {}}, {"ancestorTitles": ["PocketBase Service", "Current User"], "fullName": "PocketBase Service Current User should return user data when authenticated", "status": "passed", "title": "should return user data when authenticated", "duration": 1.0524489999988873, "failureMessages": [], "location": {"line": 80, "column": 7}, "meta": {}}, {"ancestorTitles": ["PocketBase Service", "<PERSON><PERSON>"], "fullName": "PocketBase Service Login should successfully authenticate user with valid credentials", "status": "passed", "title": "should successfully authenticate user with valid credentials", "duration": 2.858324000000721, "failureMessages": [], "location": {"line": 88, "column": 7}, "meta": {}}, {"ancestorTitles": ["PocketBase Service", "<PERSON><PERSON>"], "fullName": "PocketBase Service Login should return error when login fails", "status": "passed", "title": "should return error when login fails", "duration": 0.9615950000006706, "failureMessages": [], "location": {"line": 102, "column": 7}, "meta": {}}, {"ancestorTitles": ["PocketBase Service", "Registration"], "fullName": "PocketBase Service Registration should successfully register a new user", "status": "passed", "title": "should successfully register a new user", "duration": 1.1460160000005999, "failureMessages": [], "location": {"line": 114, "column": 7}, "meta": {}}, {"ancestorTitles": ["PocketBase Service", "Registration"], "fullName": "PocketBase Service Registration should return error when registration fails", "status": "passed", "title": "should return error when registration fails", "duration": 0.42772899999908987, "failureMessages": [], "location": {"line": 128, "column": 7}, "meta": {}}, {"ancestorTitles": ["PocketBase Service", "Logout"], "fullName": "PocketBase Service Logout should clear auth store on logout", "status": "passed", "title": "should clear auth store on logout", "duration": 0.3288169999996171, "failureMessages": [], "location": {"line": 140, "column": 7}, "meta": {}}, {"ancestorTitles": ["PocketBase Service", "Collection Access"], "fullName": "PocketBase Service Collection Access should return collection reference", "status": "passed", "title": "should return collection reference", "duration": 0.38373800000044866, "failureMessages": [], "location": {"line": 147, "column": 7}, "meta": {}}, {"ancestorTitles": ["PocketBase Service", "Real-time Subscriptions"], "fullName": "PocketBase Service Real-time Subscriptions should subscribe to real-time updates", "status": "passed", "title": "should subscribe to real-time updates", "duration": 0.45301999999901454, "failureMessages": [], "location": {"line": 155, "column": 7}, "meta": {}}, {"ancestorTitles": ["PocketBase Service", "Real-time Subscriptions"], "fullName": "PocketBase Service Real-time Subscriptions should unsubscribe from real-time updates", "status": "passed", "title": "should unsubscribe from real-time updates", "duration": 0.3060569999997824, "failureMessages": [], "location": {"line": 164, "column": 7}, "meta": {}}, {"ancestorTitles": ["PocketBase Service", "Client Access"], "fullName": "PocketBase Service Client Access should return PocketBase client instance", "status": "passed", "title": "should return PocketBase client instance", "duration": 0.20850700000119105, "failureMessages": [], "location": {"line": 174, "column": 7}, "meta": {}}], "startTime": 1752958012167, "endTime": 1752958012180.3062, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/unit/services/pocketbase.test.js"}, {"assertionResults": [{"ancestorTitles": ["SessionService", "createSession"], "fullName": "SessionService createSession should create a new session with valid data", "status": "passed", "title": "should create a new session with valid data", "duration": 9.271012999999584, "failureMessages": [], "location": {"line": 23, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "createSession"], "fullName": "SessionService createSession should generate unique session IDs", "status": "passed", "title": "should generate unique session IDs", "duration": 1.60515900000064, "failureMessages": [], "location": {"line": 42, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "getSession"], "fullName": "SessionService getSession should retrieve session by ID", "status": "passed", "title": "should retrieve session by ID", "duration": 0.5676330000005692, "failureMessages": [], "location": {"line": 51, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "getSession"], "fullName": "SessionService getSession should return null for non-existent session", "status": "passed", "title": "should return null for non-existent session", "duration": 0.38353300000017043, "failureMessages": [], "location": {"line": 60, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "getSessionByToken"], "fullName": "SessionService getSessionByToken should retrieve session by token", "status": "passed", "title": "should retrieve session by token", "duration": 0.3779840000006516, "failureMessages": [], "location": {"line": 67, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "getSessionByToken"], "fullName": "SessionService getSessionByToken should return null for non-existent token", "status": "passed", "title": "should return null for non-existent token", "duration": 0.24174799999855168, "failureMessages": [], "location": {"line": 77, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "getUserSessions"], "fullName": "SessionService getUserSessions should return all active sessions for a user", "status": "passed", "title": "should return all active sessions for a user", "duration": 1.3142509999997856, "failureMessages": [], "location": {"line": 84, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "getUserSessions"], "fullName": "SessionService getUserSessions should return empty array for user with no sessions", "status": "passed", "title": "should return empty array for user with no sessions", "duration": 0.2775039999996807, "failureMessages": [], "location": {"line": 99, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "updateSessionActivity"], "fullName": "SessionService updateSessionActivity should update last activity for valid session", "status": "passed", "title": "should update last activity for valid session", "duration": 11.773035000000164, "failureMessages": [], "location": {"line": 106, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "updateSessionActivity"], "fullName": "SessionService updateSessionActivity should return false for non-existent token", "status": "passed", "title": "should return false for non-existent token", "duration": 0.27458500000102504, "failureMessages": [], "location": {"line": 121, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "updateSessionActivity"], "fullName": "SessionService updateSessionActivity should return false for inactive session", "status": "passed", "title": "should return false for inactive session", "duration": 0.27108099999895785, "failureMessages": [], "location": {"line": 126, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "invalidateSession"], "fullName": "SessionService invalidateSession should invalidate session by ID", "status": "passed", "title": "should invalidate session by ID", "duration": 0.3011420000002545, "failureMessages": [], "location": {"line": 139, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "invalidateSession"], "fullName": "SessionService invalidateSession should add token to blacklist when invalidating", "status": "passed", "title": "should add token to blacklist when invalidating", "duration": 0.36027199999989534, "failureMessages": [], "location": {"line": 149, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "invalidateSession"], "fullName": "SessionService invalidateSession should return false for non-existent session", "status": "passed", "title": "should return false for non-existent session", "duration": 0.2769540000008419, "failureMessages": [], "location": {"line": 159, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "invalidateSessionByToken"], "fullName": "SessionService invalidateSessionByToken should invalidate session by token", "status": "passed", "title": "should invalidate session by token", "duration": 0.3191349999997328, "failureMessages": [], "location": {"line": 166, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "invalidateSessionByToken"], "fullName": "SessionService invalidateSessionByToken should return false for non-existent token", "status": "passed", "title": "should return false for non-existent token", "duration": 0.14782000000013795, "failureMessages": [], "location": {"line": 178, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "invalidateAllUserSessions"], "fullName": "SessionService invalidateAllUserSessions should invalidate all sessions for a user", "status": "passed", "title": "should invalidate all sessions for a user", "duration": 0.3269749999999476, "failureMessages": [], "location": {"line": 185, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "invalidateAllUserSessions"], "fullName": "SessionService invalidateAllUserSessions should exclude specified token from invalidation", "status": "passed", "title": "should exclude specified token from invalidation", "duration": 0.6854419999999664, "failureMessages": [], "location": {"line": 203, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "isTokenBlacklisted"], "fullName": "SessionService isTokenBlacklisted should return true for blacklisted tokens", "status": "passed", "title": "should return true for blacklisted tokens", "duration": 0.3749549999993178, "failureMessages": [], "location": {"line": 221, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "isTokenBlacklisted"], "fullName": "SessionService isTokenBlacklisted should return false for non-blacklisted tokens", "status": "passed", "title": "should return false for non-blacklisted tokens", "duration": 0.4915389999987383, "failureMessages": [], "location": {"line": 230, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "validateSession"], "fullName": "SessionService validateSession should validate active session and update activity", "status": "passed", "title": "should validate active session and update activity", "duration": 0.48391000000083295, "failureMessages": [], "location": {"line": 239, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "validateSession"], "fullName": "SessionService validateSession should reject blacklisted tokens", "status": "passed", "title": "should reject blacklisted tokens", "duration": 0.2646100000001752, "failureMessages": [], "location": {"line": 250, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "validateSession"], "fullName": "SessionService validateSession should reject non-existent sessions", "status": "passed", "title": "should reject non-existent sessions", "duration": 0.21935599999960687, "failureMessages": [], "location": {"line": 262, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "validateSession"], "fullName": "SessionService validateSession should reject inactive sessions", "status": "passed", "title": "should reject inactive sessions", "duration": 0.22522900000149093, "failureMessages": [], "location": {"line": 269, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "getSessionStats"], "fullName": "SessionService getSessionStats should return correct session statistics", "status": "passed", "title": "should return correct session statistics", "duration": 0.41936899999927846, "failureMessages": [], "location": {"line": 284, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "forceLogoutUser"], "fullName": "SessionService forceLogoutUser should force logout all sessions for a user", "status": "passed", "title": "should force logout all sessions for a user", "duration": 0.4275309999993624, "failureMessages": [], "location": {"line": 304, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "getUserSessionDetails"], "fullName": "SessionService getUserSessionDetails should return detailed session information", "status": "passed", "title": "should return detailed session information", "duration": 0.8582239999996091, "failureMessages": [], "location": {"line": 319, "column": 7}, "meta": {}}], "startTime": 1752958014834, "endTime": 1752958014867.8582, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/unit/services/sessionService.test.js"}, {"assertionResults": [{"ancestorTitles": ["Authentication Store (TEST-001)", "Initial State"], "fullName": "Authentication Store (TEST-001) Initial State should initialize with correct defaults", "status": "passed", "title": "should initialize with correct defaults", "duration": 5.559669000000213, "failureMessages": [], "location": {"line": 47, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Getters"], "fullName": "Authentication Store (TEST-001) Getters should return correct isLoggedIn status", "status": "passed", "title": "should return correct isLoggedIn status", "duration": 1.6333309999990888, "failureMessages": [], "location": {"line": 59, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Getters"], "fullName": "Authentication Store (TEST-001) Getters should return current user", "status": "passed", "title": "should return current user", "duration": 2.809449999998833, "failureMessages": [], "location": {"line": 68, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Login Action"], "fullName": "Authentication Store (TEST-001) Login Action should login successfully", "status": "passed", "title": "should login successfully", "duration": 2.4838029999991704, "failureMessages": [], "location": {"line": 77, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Login Action"], "fullName": "Authentication Store (TEST-001) Login Action should handle login failure", "status": "passed", "title": "should handle login failure", "duration": 0.7505049999999756, "failureMessages": [], "location": {"line": 99, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Login Action"], "fullName": "Authentication Store (TEST-001) Login Action should handle login exception", "status": "failed", "title": "should handle login exception", "duration": 5.5539260000005015, "failureMessages": ["AssertionError: expected null to be 'Network error' // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/auth.spec.js:127:27\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected null to be 'Network error' // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/auth.spec.js:127:27\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected null to be 'Network error' // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/auth.spec.js:127:27\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 118, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Register Action"], "fullName": "Authentication Store (TEST-001) Register Action should register successfully", "status": "passed", "title": "should register successfully", "duration": 8.355451999999786, "failureMessages": [], "location": {"line": 134, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Register Action"], "fullName": "Authentication Store (TEST-001) Register Action should handle registration failure", "status": "passed", "title": "should handle registration failure", "duration": 0.664080999998987, "failureMessages": [], "location": {"line": 155, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Logout Action"], "fullName": "Authentication Store (TEST-001) Logout Action should logout successfully", "status": "passed", "title": "should logout successfully", "duration": 1.0766700000003766, "failureMessages": [], "location": {"line": 173, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Token Refresh"], "fullName": "Authentication Store (TEST-001) Token Refresh should refresh token successfully", "status": "failed", "title": "should refresh token successfully", "duration": 2.1636859999998705, "failureMessages": ["AssertionError: expected null to be 'new-token' // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/auth.spec.js:206:27\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected null to be 'new-token' // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/auth.spec.js:206:27\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected null to be 'new-token' // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/auth.spec.js:206:27\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 194, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Token Refresh"], "fullName": "Authentication Store (TEST-001) Token Refresh should handle token refresh failure", "status": "failed", "title": "should handle token refresh failure", "duration": 2.3885769999997137, "failureMessages": ["AssertionError: expected null to be 'Token expired' // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/auth.spec.js:219:27\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected null to be 'Token expired' // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/auth.spec.js:219:27\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected null to be 'Token expired' // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/auth.spec.js:219:27\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 210, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Password Reset"], "fullName": "Authentication Store (TEST-001) Password Reset should request password reset successfully", "status": "passed", "title": "should request password reset successfully", "duration": 0.6355400000011286, "failureMessages": [], "location": {"line": 224, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Password Reset"], "fullName": "Authentication Store (TEST-001) Password Reset should reset password successfully", "status": "failed", "title": "should reset password successfully", "duration": 29.21137500000077, "failureMessages": ["AssertionError: expected \"spy\" to be called with arguments: [ { token: 'reset-token', …(1) } ]\u001b[90m\n\nNumber of calls: \u001b[1m0\u001b[22m\n\u001b[39m\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1355:10)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/auth.spec.js:246:47\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected \"spy\" to be called with arguments: [ { token: 'reset-token', …(1) } ]\u001b[90m\n\nNumber of calls: \u001b[1m0\u001b[22m\n\u001b[39m\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1355:10)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/auth.spec.js:246:47\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected \"spy\" to be called with arguments: [ { token: 'reset-token', …(1) } ]\u001b[90m\n\nNumber of calls: \u001b[1m0\u001b[22m\n\u001b[39m\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1355:10)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/auth.spec.js:246:47\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 237, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "State Persistence"], "fullName": "Authentication Store (TEST-001) State Persistence should persist auth state to localStorage", "status": "passed", "title": "should persist auth state to localStorage", "duration": 0.646258000000671, "failureMessages": [], "location": {"line": 252, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "State Persistence"], "fullName": "Authentication Store (TEST-001) State Persistence should restore auth state from localStorage", "status": "failed", "title": "should restore auth state from localStorage", "duration": 2.0196759999998903, "failureMessages": ["TypeError: store.restoreAuthState is not a function\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/auth.spec.js:276:13\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)", "TypeError: store.restoreAuthState is not a function\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/auth.spec.js:276:13\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)", "TypeError: store.restoreAuthState is not a function\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/auth.spec.js:276:13\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)"], "location": {"line": 266, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "State Persistence"], "fullName": "Authentication Store (TEST-001) State Persistence should clear auth state", "status": "failed", "title": "should clear auth state", "duration": 14.630651999999827, "failureMessages": ["AssertionError: expected \"spy\" to be called with arguments: [ 'auth_token' ]\u001b[90m\n\nNumber of calls: \u001b[1m0\u001b[22m\n\u001b[39m\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1355:10)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/auth.spec.js:294:43\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)", "AssertionError: expected \"spy\" to be called with arguments: [ 'auth_token' ]\u001b[90m\n\nNumber of calls: \u001b[1m0\u001b[22m\n\u001b[39m\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1355:10)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/auth.spec.js:294:43\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)", "AssertionError: expected \"spy\" to be called with arguments: [ 'auth_token' ]\u001b[90m\n\nNumber of calls: \u001b[1m0\u001b[22m\n\u001b[39m\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1355:10)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/auth.spec.js:294:43\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)"], "location": {"line": 283, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Initialization"], "fullName": "Authentication Store (TEST-001) Initialization should initialize auth state", "status": "failed", "title": "should initialize auth state", "duration": 6.038054000000557, "failureMessages": ["AssertionError: expected false to be true // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/auth.spec.js:312:37\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected false to be true // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/auth.spec.js:312:37\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected false to be true // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/auth.spec.js:312:37\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 300, "column": 7}, "meta": {}}], "startTime": 1752958016862, "endTime": 1752958016950.038, "status": "failed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/auth.spec.js"}, {"assertionResults": [], "startTime": 1752958004680, "endTime": 1752958004680, "status": "failed", "message": "Failed to resolve import \"@/services/databaseService\" from \"ui/stores/__tests__/tasks.spec.js\". Does the file exist?", "name": "/Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js"}]}