{"numTotalTestSuites": 175, "numPassedTestSuites": 139, "numFailedTestSuites": 36, "numPendingTestSuites": 0, "numTotalTests": 318, "numPassedTests": 283, "numFailedTests": 35, "numPendingTests": 0, "numTodoTests": 0, "snapshot": {"added": 0, "failure": false, "filesAdded": 0, "filesRemoved": 0, "filesRemovedList": [], "filesUnmatched": 0, "filesUpdated": 0, "matched": 0, "total": 0, "unchecked": 0, "uncheckedKeysByFile": [], "unmatched": 0, "updated": 0, "didUpdate": false}, "startTime": 1752959400365, "success": false, "testResults": [{"assertionResults": [{"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "User Registration Journey"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) User Registration Journey should complete full user registration flow", "status": "passed", "title": "should complete full user registration flow", "duration": 7.957263999998759, "failureMessages": [], "location": {"line": 90, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "User Registration Journey"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) User Registration Journey should handle registration validation errors", "status": "passed", "title": "should handle registration validation errors", "duration": 0.5324529999998049, "failureMessages": [], "location": {"line": 117, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "User Registration Journey"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) User Registration Journey should handle existing email registration attempt", "status": "passed", "title": "should handle existing email registration attempt", "duration": 0.43764499999997497, "failureMessages": [], "location": {"line": 138, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "User Login <PERSON>"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) User Login Journey should complete successful login flow", "status": "passed", "title": "should complete successful login flow", "duration": 0.6493669999999838, "failureMessages": [], "location": {"line": 161, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "User Login <PERSON>"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) User Login Journey should handle invalid login credentials", "status": "passed", "title": "should handle invalid login credentials", "duration": 1.5815419999998994, "failureMessages": [], "location": {"line": 184, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "User Login <PERSON>"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) User Login Journey should redirect to intended page after login", "status": "passed", "title": "should redirect to intended page after login", "duration": 2.1375800000005256, "failureMessages": [], "location": {"line": 203, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "User Lo<PERSON>ut <PERSON>"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) User Logout Journey should complete logout flow", "status": "passed", "title": "should complete logout flow", "duration": 1.0675439999995433, "failureMessages": [], "location": {"line": 223, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "Password Reset Journey"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) Password Reset Journey should complete password reset request flow", "status": "passed", "title": "should complete password reset request flow", "duration": 0.5246820000011212, "failureMessages": [], "location": {"line": 246, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "Password Reset Journey"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) Password Reset Journey should complete password reset confirmation flow", "status": "passed", "title": "should complete password reset confirmation flow", "duration": 0.5149920000003476, "failureMessages": [], "location": {"line": 262, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "Profile Management Journey"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) Profile Management Journey should complete profile update flow", "status": "passed", "title": "should complete profile update flow", "duration": 0.4393839999993361, "failureMessages": [], "location": {"line": 285, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "Profile Management Journey"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) Profile Management Journey should complete password change flow", "status": "passed", "title": "should complete password change flow", "duration": 1.3767820000011852, "failureMessages": [], "location": {"line": 303, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "Session Management Journey"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) Session Management Journey should handle session timeout gracefully", "status": "passed", "title": "should handle session timeout gracefully", "duration": 0.336950999999317, "failureMessages": [], "location": {"line": 324, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "Session Management Journey"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) Session Management Journey should handle concurrent sessions", "status": "passed", "title": "should handle concurrent sessions", "duration": 0.25715300000047137, "failureMessages": [], "location": {"line": 347, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "Error <PERSON>"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) Error Handling Journey should handle network errors gracefully", "status": "passed", "title": "should handle network errors gracefully", "duration": 0.43180500000016764, "failureMessages": [], "location": {"line": 367, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "Error <PERSON>"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) Error Handling Journey should handle server errors gracefully", "status": "passed", "title": "should handle server errors gracefully", "duration": 0.256467999999586, "failureMessages": [], "location": {"line": 388, "column": 7}, "meta": {}}], "startTime": 1752959414774, "endTime": 1752959414793.432, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/e2e/auth-journeys.test.js"}, {"assertionResults": [{"ancestorTitles": ["Responsive Design E2E Tests (TEST-009)", "Authentication Forms Responsive Design"], "fullName": "Responsive Design E2E Tests (TEST-009) Authentication Forms Responsive Design should display login form correctly on mobile devices", "status": "failed", "title": "should display login form correctly on mobile devices", "duration": 12.312737000000197, "failureMessages": ["AssertionError: expected 100 to be greater than 300\n    at /Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:142:38\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected 100 to be greater than 300\n    at /Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:142:38\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected 100 to be greater than 300\n    at /Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:142:38\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 125, "column": 7}, "meta": {}}, {"ancestorTitles": ["Responsive Design E2E Tests (TEST-009)", "Authentication Forms Responsive Design"], "fullName": "Responsive Design E2E Tests (TEST-009) Authentication Forms Responsive Design should display registration form correctly on tablet devices", "status": "passed", "title": "should display registration form correctly on tablet devices", "duration": 4.254985999999917, "failureMessages": [], "location": {"line": 150, "column": 7}, "meta": {}}, {"ancestorTitles": ["Responsive Design E2E Tests (TEST-009)", "Authentication Forms Responsive Design"], "fullName": "Responsive Design E2E Tests (TEST-009) Authentication Forms Responsive Design should display forms correctly on desktop", "status": "failed", "title": "should display forms correctly on desktop", "duration": 2.9811489999992773, "failureMessages": ["AssertionError: expected 0 to be greater than 500\n    at /Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:180:28\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected 0 to be greater than 500\n    at /Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:180:28\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected 0 to be greater than 500\n    at /Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:180:28\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 168, "column": 7}, "meta": {}}, {"ancestorTitles": ["Responsive Design E2E Tests (TEST-009)", "Authentication Forms Responsive Design"], "fullName": "Responsive Design E2E Tests (TEST-009) Authentication Forms Responsive Design should handle very small screens gracefully", "status": "failed", "title": "should handle very small screens gracefully", "duration": 3.850286999999298, "failureMessages": ["AssertionError: expected 100 to be greater than 300\n    at /Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:198:38\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected 100 to be greater than 300\n    at /Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:198:38\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected 100 to be greater than 300\n    at /Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:198:38\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 183, "column": 7}, "meta": {}}, {"ancestorTitles": ["Responsive Design E2E Tests (TEST-009)", "Navigation Responsive Design"], "fullName": "Responsive Design E2E Tests (TEST-009) Navigation Responsive Design should show mobile navigation drawer on small screens", "status": "failed", "title": "should show mobile navigation drawer on small screens", "duration": 9.137828999999329, "failureMessages": ["AssertionError: expected true to be false // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:223:33\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected true to be false // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:223:33\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected true to be false // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:223:33\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 203, "column": 7}, "meta": {}}, {"ancestorTitles": ["Responsive Design E2E Tests (TEST-009)", "Navigation Responsive Design"], "fullName": "Responsive Design E2E Tests (TEST-009) Navigation Responsive Design should show desktop navigation on large screens", "status": "failed", "title": "should show desktop navigation on large screens", "duration": 3.9468010000000504, "failureMessages": ["AssertionError: expected true to be false // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:256:33\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected true to be false // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:256:33\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected true to be false // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:256:33\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 237, "column": 7}, "meta": {}}, {"ancestorTitles": ["Responsive Design E2E Tests (TEST-009)", "Navigation Responsive Design"], "fullName": "Responsive Design E2E Tests (TEST-009) Navigation Responsive Design should adapt navigation for tablet screens", "status": "failed", "title": "should adapt navigation for tablet screens", "duration": 6.381874999999127, "failureMessages": ["AssertionError: expected true to be false // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:273:46\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected true to be false // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:273:46\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected true to be false // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:273:46\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 259, "column": 7}, "meta": {}}, {"ancestorTitles": ["Responsive Design E2E Tests (TEST-009)", "Touch Interactions"], "fullName": "Responsive Design E2E Tests (TEST-009) Touch Interactions should handle touch interactions on mobile devices", "status": "failed", "title": "should handle touch interactions on mobile devices", "duration": 6.531272000000172, "failureMessages": ["AssertionError: expected false to be true // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:293:31\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected false to be true // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:293:31\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected false to be true // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:293:31\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 278, "column": 7}, "meta": {}}, {"ancestorTitles": ["Responsive Design E2E Tests (TEST-009)", "Touch Interactions"], "fullName": "Responsive Design E2E Tests (TEST-009) Touch Interactions should provide adequate spacing between touch targets", "status": "failed", "title": "should provide adequate spacing between touch targets", "duration": 4.038984999999229, "failureMessages": ["AssertionError: expected -100 to be greater than or equal to 8\n    at /Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:313:23\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected -100 to be greater than or equal to 8\n    at /Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:313:23\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected -100 to be greater than or equal to 8\n    at /Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:313:23\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 296, "column": 7}, "meta": {}}, {"ancestorTitles": ["Responsive Design E2E Tests (TEST-009)", "Content Layout Responsive Design"], "fullName": "Responsive Design E2E Tests (TEST-009) Content Layout Responsive Design should stack content vertically on mobile", "status": "passed", "title": "should stack content vertically on mobile", "duration": 0.7844880000011472, "failureMessages": [], "location": {"line": 318, "column": 7}, "meta": {}}, {"ancestorTitles": ["Responsive Design E2E Tests (TEST-009)", "Content Layout Responsive Design"], "fullName": "Responsive Design E2E Tests (TEST-009) Content Layout Responsive Design should display content in rows on desktop", "status": "passed", "title": "should display content in rows on desktop", "duration": 5.832615999999689, "failureMessages": [], "location": {"line": 336, "column": 7}, "meta": {}}, {"ancestorTitles": ["Responsive Design E2E Tests (TEST-009)", "Content Layout Responsive Design"], "fullName": "Responsive Design E2E Tests (TEST-009) Content Layout Responsive Design should adjust font sizes for different screen sizes", "status": "passed", "title": "should adjust font sizes for different screen sizes", "duration": 0.7918980000013107, "failureMessages": [], "location": {"line": 354, "column": 7}, "meta": {}}, {"ancestorTitles": ["Responsive Design E2E Tests (TEST-009)", "Form Input Responsive Design"], "fullName": "Responsive Design E2E Tests (TEST-009) Form Input Responsive Design should adjust input field sizes for mobile", "status": "failed", "title": "should adjust input field sizes for mobile", "duration": 3.8858609999988403, "failureMessages": ["AssertionError: expected 100 to be greater than 300\n    at /Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:394:33\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected 100 to be greater than 300\n    at /Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:394:33\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected 100 to be greater than 300\n    at /Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:394:33\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 382, "column": 7}, "meta": {}}, {"ancestorTitles": ["Responsive Design E2E Tests (TEST-009)", "Form Input Responsive Design"], "fullName": "Responsive Design E2E Tests (TEST-009) Form Input Responsive Design should handle keyboard appearance on mobile", "status": "passed", "title": "should handle keyboard appearance on mobile", "duration": 1.350350000000617, "failureMessages": [], "location": {"line": 398, "column": 7}, "meta": {}}, {"ancestorTitles": ["Responsive Design E2E Tests (TEST-009)", "Accessibility on Different Devices"], "fullName": "Responsive Design E2E Tests (TEST-009) Accessibility on Different Devices should maintain accessibility on mobile devices", "status": "failed", "title": "should maintain accessibility on mobile devices", "duration": 3.5176029999984166, "failureMessages": ["AssertionError: expected false to be true // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:429:31\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected false to be true // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:429:31\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected false to be true // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js:429:31\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 419, "column": 7}, "meta": {}}, {"ancestorTitles": ["Responsive Design E2E Tests (TEST-009)", "Accessibility on Different Devices"], "fullName": "Responsive Design E2E Tests (TEST-009) Accessibility on Different Devices should support keyboard navigation on all devices", "status": "passed", "title": "should support keyboard navigation on all devices", "duration": 0.5693580000006477, "failureMessages": [], "location": {"line": 441, "column": 7}, "meta": {}}], "startTime": 1752959408231, "endTime": 1752959408302.5693, "status": "failed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/e2e/responsive-design.test.js"}, {"assertionResults": [{"ancestorTitles": ["Authentication Flows Integration Tests (TEST-005)", "Complete Login Flow"], "fullName": "Authentication Flows Integration Tests (TEST-005) Complete Login Flow should complete full login flow successfully", "status": "passed", "title": "should complete full login flow successfully", "duration": 10.73193800000081, "failureMessages": [], "location": {"line": 66, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Flows Integration Tests (TEST-005)", "Complete Login Flow"], "fullName": "Authentication Flows Integration Tests (TEST-005) Complete Login Flow should handle login failure gracefully", "status": "passed", "title": "should handle login failure gracefully", "duration": 1.214550999999119, "failureMessages": [], "location": {"line": 101, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Flows Integration Tests (TEST-005)", "Complete Login Flow"], "fullName": "Authentication Flows Integration Tests (TEST-005) Complete Login Flow should handle network errors during login", "status": "passed", "title": "should handle network errors during login", "duration": 1.170791000000463, "failureMessages": [], "location": {"line": 126, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Flows Integration Tests (TEST-005)", "Complete Registration Flow"], "fullName": "Authentication Flows Integration Tests (TEST-005) Complete Registration Flow should complete full registration flow successfully", "status": "passed", "title": "should complete full registration flow successfully", "duration": 2.0335269999995944, "failureMessages": [], "location": {"line": 142, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Flows Integration Tests (TEST-005)", "Complete Registration Flow"], "fullName": "Authentication Flows Integration Tests (TEST-005) Complete Registration Flow should handle registration validation errors", "status": "passed", "title": "should handle registration validation errors", "duration": 1.435645999999906, "failureMessages": [], "location": {"line": 165, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Flows Integration Tests (TEST-005)", "Complete Logout Flow"], "fullName": "Authentication Flows Integration Tests (TEST-005) Complete Logout Flow should complete full logout flow successfully", "status": "passed", "title": "should complete full logout flow successfully", "duration": 1.100644000000102, "failureMessages": [], "location": {"line": 187, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Flows Integration Tests (TEST-005)", "Complete Logout Flow"], "fullName": "Authentication Flows Integration Tests (TEST-005) Complete Logout Flow should handle logout errors gracefully", "status": "passed", "title": "should handle logout errors gracefully", "duration": 0.6980840000014723, "failureMessages": [], "location": {"line": 212, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Flows Integration Tests (TEST-005)", "Password Reset Flow"], "fullName": "Authentication Flows Integration Tests (TEST-005) Password Reset Flow should complete password reset request flow", "status": "passed", "title": "should complete password reset request flow", "duration": 0.692774000001009, "failureMessages": [], "location": {"line": 232, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Flows Integration Tests (TEST-005)", "Password Reset Flow"], "fullName": "Authentication Flows Integration Tests (TEST-005) Password Reset Flow should complete password reset confirmation flow", "status": "passed", "title": "should complete password reset confirmation flow", "duration": 0.7907290000002831, "failureMessages": [], "location": {"line": 245, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Flows Integration Tests (TEST-005)", "Session Restoration Flow"], "fullName": "Authentication Flows Integration Tests (TEST-005) Session Restoration Flow should restore session from localStorage on initialization", "status": "passed", "title": "should restore session from localStorage on initialization", "duration": 0.9398799999999028, "failureMessages": [], "location": {"line": 260, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Flows Integration Tests (TEST-005)", "Session Restoration Flow"], "fullName": "Authentication Flows Integration Tests (TEST-005) Session Restoration Flow should handle invalid stored session gracefully", "status": "passed", "title": "should handle invalid stored session gracefully", "duration": 1.5899950000002718, "failureMessages": [], "location": {"line": 288, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Flows Integration Tests (TEST-005)", "Profile Management Flow"], "fullName": "Authentication Flows Integration Tests (TEST-005) Profile Management Flow should complete profile update flow", "status": "passed", "title": "should complete profile update flow", "duration": 1.0356840000004013, "failureMessages": [], "location": {"line": 310, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Flows Integration Tests (TEST-005)", "Profile Management Flow"], "fullName": "Authentication Flows Integration Tests (TEST-005) Profile Management Flow should complete password change flow", "status": "passed", "title": "should complete password change flow", "duration": 0.8989149999997608, "failureMessages": [], "location": {"line": 331, "column": 7}, "meta": {}}], "startTime": 1752959413490, "endTime": 1752959413514.899, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/integration/auth-flows.test.js"}, {"assertionResults": [{"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Network Error Handling"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Network Error Handling should handle network errors during login", "status": "passed", "title": "should handle network errors during login", "duration": 7.419866000000184, "failureMessages": [], "location": {"line": 101, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Network Error Handling"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Network Error Handling should handle timeout errors", "status": "passed", "title": "should handle timeout errors", "duration": 52.73655000000144, "failureMessages": [], "location": {"line": 118, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Network Error Handling"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Network Error Handling should handle server errors gracefully", "status": "passed", "title": "should handle server errors gracefully", "duration": 1.164187000000311, "failureMessages": [], "location": {"line": 137, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "LocalStorage Error Handling"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) LocalStorage Error Handling should handle localStorage unavailability", "status": "failed", "title": "should handle localStorage unavailability", "duration": 9.868317000000388, "failureMessages": ["AssertionError: expected [Function] to not throw an error but 'Error: localStorage quota exceeded' was thrown\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1420:16)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/test/integration/error-handling.test.js:168:54\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)", "AssertionError: expected [Function] to not throw an error but 'Error: localStorage quota exceeded' was thrown\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1420:16)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/test/integration/error-handling.test.js:168:54\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)", "AssertionError: expected [Function] to not throw an error but 'Error: localStorage quota exceeded' was thrown\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1420:16)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/test/integration/error-handling.test.js:168:54\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)"], "location": {"line": 158, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "LocalStorage Error Handling"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) LocalStorage Error Handling should handle localStorage quota exceeded", "status": "failed", "title": "should handle localStorage quota exceeded", "duration": 4.4491760000000795, "failureMessages": ["AssertionError: expected [Function] to not throw an error but 'Error: QuotaExceededError' was thrown\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1420:16)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/test/integration/error-handling.test.js:188:54\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)", "AssertionError: expected [Function] to not throw an error but 'Error: QuotaExceededError' was thrown\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1420:16)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/test/integration/error-handling.test.js:188:54\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)", "AssertionError: expected [Function] to not throw an error but 'Error: QuotaExceededError' was thrown\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1420:16)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/test/integration/error-handling.test.js:188:54\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)"], "location": {"line": 175, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "LocalStorage Error Handling"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) LocalStorage Error Handling should handle corrupted localStorage data", "status": "failed", "title": "should handle corrupted localStorage data", "duration": 4.90409499999987, "failureMessages": ["AssertionError: expected [Function] to not throw an error but 'SyntaxError: Unexpected token \\'i\\', …' was thrown\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1420:16)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/test/integration/error-handling.test.js:202:54\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)", "AssertionError: expected [Function] to not throw an error but 'SyntaxError: Unexpected token \\'i\\', …' was thrown\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1420:16)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/test/integration/error-handling.test.js:202:54\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)", "AssertionError: expected [Function] to not throw an error but 'SyntaxError: Unexpected token \\'i\\', …' was thrown\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1420:16)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/test/integration/error-handling.test.js:202:54\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)"], "location": {"line": 191, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Authentication Edge Cases"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Authentication Edge Cases should handle simultaneous login attempts", "status": "passed", "title": "should handle simultaneous login attempts", "duration": 101.58448199999839, "failureMessages": [], "location": {"line": 212, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Authentication Edge Cases"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Authentication Edge Cases should handle login with empty credentials", "status": "failed", "title": "should handle login with empty credentials", "duration": 310.46820500000103, "failureMessages": ["AssertionError: expected true to be false // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/integration/error-handling.test.js:248:30\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected true to be false // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/integration/error-handling.test.js:248:30\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected true to be false // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/integration/error-handling.test.js:248:30\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 243, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Authentication Edge Cases"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Authentication Edge Cases should handle login with null/undefined credentials", "status": "failed", "title": "should handle login with null/undefined credentials", "duration": 617.600018000001, "failureMessages": ["AssertionError: expected true to be false // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/integration/error-handling.test.js:258:31\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected true to be false // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/integration/error-handling.test.js:258:31\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected true to be false // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/integration/error-handling.test.js:258:31\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 252, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Authentication Edge Cases"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Authentication Edge Cases should handle extremely long input values", "status": "passed", "title": "should handle extremely long input values", "duration": 101.49043999999958, "failureMessages": [], "location": {"line": 263, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Token Refresh Edge Cases"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Token Refresh Edge Cases should handle token refresh when already refreshing", "status": "passed", "title": "should handle token refresh when already refreshing", "duration": 201.78187799999978, "failureMessages": [], "location": {"line": 280, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Token Refresh Edge Cases"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Token Refresh Edge Cases should handle token refresh with invalid response", "status": "failed", "title": "should handle token refresh with invalid response", "duration": 2.5467490000010002, "failureMessages": ["AssertionError: expected true to be false // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/integration/error-handling.test.js:327:30\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected true to be false // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/integration/error-handling.test.js:327:30\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected true to be false // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/integration/error-handling.test.js:327:30\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 315, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Session Management Edge Cases"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Session Management Edge Cases should handle session restoration with partial data", "status": "failed", "title": "should handle session restoration with partial data", "duration": 4.016031000001021, "failureMessages": ["AssertionError: expected null to be 'stored-token' // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/integration/error-handling.test.js:347:31\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)", "AssertionError: expected null to be 'stored-token' // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/integration/error-handling.test.js:347:31\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)", "AssertionError: expected null to be 'stored-token' // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/integration/error-handling.test.js:347:31\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)"], "location": {"line": 333, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Session Management Edge Cases"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Session Management Edge Cases should handle concurrent session modifications", "status": "passed", "title": "should handle concurrent session modifications", "duration": 0.779184000000896, "failureMessages": [], "location": {"line": 352, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Memory and Performance Edge Cases"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Memory and Performance Edge Cases should handle memory pressure gracefully", "status": "passed", "title": "should handle memory pressure gracefully", "duration": 293.2379889999993, "failureMessages": [], "location": {"line": 379, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Memory and Performance Edge Cases"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Memory and Performance Edge Cases should handle rapid successive operations", "status": "passed", "title": "should handle rapid successive operations", "duration": 118.73507999999856, "failureMessages": [], "location": {"line": 394, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Browser Compatibility Edge Cases"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Browser Compatibility Edge Cases should handle missing browser APIs gracefully", "status": "passed", "title": "should handle missing browser APIs gracefully", "duration": 1.4259210000000166, "failureMessages": [], "location": {"line": 415, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Browser Compatibility Edge Cases"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Browser Compatibility Edge Cases should handle disabled cookies", "status": "passed", "title": "should handle disabled cookies", "duration": 1.4443080000000919, "failureMessages": [], "location": {"line": 428, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Data Validation Edge Cases"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Data Validation Edge Cases should handle malformed server responses", "status": "passed", "title": "should handle malformed server responses", "duration": 0.9709810000003927, "failureMessages": [], "location": {"line": 445, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Data Validation Edge Cases"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Data Validation Edge Cases should handle unexpected data types", "status": "passed", "title": "should handle unexpected data types", "duration": 0.7715339999995194, "failureMessages": [], "location": {"line": 465, "column": 7}, "meta": {}}], "startTime": 1752959408453, "endTime": 1752959410291.7715, "status": "failed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/integration/error-handling.test.js"}, {"assertionResults": [{"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Role Checking"], "fullName": "Role-Based Access Control Tests (TEST-007) Role Checking should correctly identify admin users", "status": "passed", "title": "should correctly identify admin users", "duration": 20.889800999999352, "failureMessages": [], "location": {"line": 107, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Role Checking"], "fullName": "Role-Based Access Control Tests (TEST-007) Role Checking should correctly identify regular users", "status": "passed", "title": "should correctly identify regular users", "duration": 14.742256999999881, "failureMessages": [], "location": {"line": 120, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Role Checking"], "fullName": "Role-Based Access Control Tests (TEST-007) Role Checking should handle multiple roles", "status": "passed", "title": "should handle multiple roles", "duration": 0.4438270000009652, "failureMessages": [], "location": {"line": 135, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Permission Checking"], "fullName": "Role-Based Access Control Tests (TEST-007) Permission Checking should check individual permissions correctly", "status": "passed", "title": "should check individual permissions correctly", "duration": 0.5237509999988106, "failureMessages": [], "location": {"line": 152, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Permission Checking"], "fullName": "Role-Based Access Control Tests (TEST-007) Permission Checking should handle admin users with all permissions", "status": "passed", "title": "should handle admin users with all permissions", "duration": 0.5974969999988389, "failureMessages": [], "location": {"line": 167, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Permission Checking"], "fullName": "Role-Based Access Control Tests (TEST-007) Permission Checking should handle users with no permissions", "status": "passed", "title": "should handle users with no permissions", "duration": 1.097288999999364, "failureMessages": [], "location": {"line": 182, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Route Access Control"], "fullName": "Role-Based Access Control Tests (TEST-007) Route Access Control should allow admin access to admin routes", "status": "passed", "title": "should allow admin access to admin routes", "duration": 2.145510000000286, "failureMessages": [], "location": {"line": 197, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Route Access Control"], "fullName": "Role-Based Access Control Tests (TEST-007) Route Access Control should deny regular user access to admin routes", "status": "passed", "title": "should deny regular user access to admin routes", "duration": 1.53697800000009, "failureMessages": [], "location": {"line": 211, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Route Access Control"], "fullName": "Role-Based Access Control Tests (TEST-007) Route Access Control should enforce role-based route access", "status": "passed", "title": "should enforce role-based route access", "duration": 5.750420999998823, "failureMessages": [], "location": {"line": 225, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Route Access Control"], "fullName": "Role-Based Access Control Tests (TEST-007) Route Access Control should deny access when user lacks required role", "status": "passed", "title": "should deny access when user lacks required role", "duration": 0.7198040000002948, "failureMessages": [], "location": {"line": 239, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Route Access Control"], "fullName": "Role-Based Access Control Tests (TEST-007) Route Access Control should enforce permission-based route access", "status": "passed", "title": "should enforce permission-based route access", "duration": 0.6007559999998193, "failureMessages": [], "location": {"line": 253, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Route Access Control"], "fullName": "Role-Based Access Control Tests (TEST-007) Route Access Control should deny access when user lacks required permissions", "status": "passed", "title": "should deny access when user lacks required permissions", "duration": 0.5120329999990645, "failureMessages": [], "location": {"line": 271, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Route Access Control"], "fullName": "Role-Based Access Control Tests (TEST-007) Route Access Control should redirect unauthenticated users to login", "status": "passed", "title": "should redirect unauthenticated users to login", "duration": 0.4322709999996732, "failureMessages": [], "location": {"line": 289, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Complex Access Control Scenarios"], "fullName": "Role-Based Access Control Tests (TEST-007) Complex Access Control Scenarios should handle routes with multiple requirements", "status": "passed", "title": "should handle routes with multiple requirements", "duration": 0.46472499999981665, "failureMessages": [], "location": {"line": 302, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Complex Access Control Scenarios"], "fullName": "Role-Based Access Control Tests (TEST-007) Complex Access Control Scenarios should deny access if any requirement is not met", "status": "passed", "title": "should deny access if any requirement is not met", "duration": 0.5124789999990753, "failureMessages": [], "location": {"line": 324, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Dynamic Permission Checking"], "fullName": "Role-Based Access Control Tests (TEST-007) Dynamic Permission Checking should check resource-specific permissions", "status": "passed", "title": "should check resource-specific permissions", "duration": 0.40437999999994645, "failureMessages": [], "location": {"line": 348, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Dynamic Permission Checking"], "fullName": "Role-Based Access Control Tests (TEST-007) Dynamic Permission Checking should handle hierarchical permissions", "status": "passed", "title": "should handle hierarchical permissions", "duration": 0.43674300000020594, "failureMessages": [], "location": {"line": 363, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Role Inheritance"], "fullName": "Role-Based Access Control Tests (TEST-007) Role Inheritance should handle role inheritance correctly", "status": "passed", "title": "should handle role inheritance correctly", "duration": 0.4062080000003334, "failureMessages": [], "location": {"line": 382, "column": 7}, "meta": {}}], "startTime": 1752959414615, "endTime": 1752959414668.4368, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/integration/rbac.test.js"}, {"assertionResults": [{"ancestorTitles": ["Token Refresh Mechanism Tests (TEST-006)", "Automatic Token Refresh"], "fullName": "Token Refresh Mechanism Tests (TEST-006) Automatic Token Refresh should refresh token automatically before expiration", "status": "passed", "title": "should refresh token automatically before expiration", "duration": 28.788183000000572, "failureMessages": [], "location": {"line": 55, "column": 7}, "meta": {}}, {"ancestorTitles": ["Token Refresh Mechanism Tests (TEST-006)", "Automatic Token Refresh"], "fullName": "Token Refresh Mechanism Tests (TEST-006) Automatic Token Refresh should handle token refresh failure by logging out", "status": "passed", "title": "should handle token refresh failure by logging out", "duration": 2.021124000000782, "failureMessages": [], "location": {"line": 85, "column": 7}, "meta": {}}, {"ancestorTitles": ["Token Refresh Mechanism Tests (TEST-006)", "Automatic Token Refresh"], "fullName": "Token Refresh Mechanism Tests (TEST-006) Automatic Token Refresh should handle network errors during token refresh", "status": "passed", "title": "should handle network errors during token refresh", "duration": 2.3287710000004154, "failureMessages": [], "location": {"line": 112, "column": 7}, "meta": {}}, {"ancestorTitles": ["Token Refresh Mechanism Tests (TEST-006)", "Token Refresh Timing"], "fullName": "Token Refresh Mechanism Tests (TEST-006) Token Refresh Timing should schedule token refresh based on token expiration", "status": "passed", "title": "should schedule token refresh based on token expiration", "duration": 1.1317250000010972, "failureMessages": [], "location": {"line": 131, "column": 7}, "meta": {}}, {"ancestorTitles": ["Token Refresh Mechanism Tests (TEST-006)", "Token Refresh Timing"], "fullName": "Token Refresh Mechanism Tests (TEST-006) Token Refresh Timing should refresh immediately if token is already expired", "status": "passed", "title": "should refresh immediately if token is already expired", "duration": 0.6855489999998099, "failureMessages": [], "location": {"line": 158, "column": 7}, "meta": {}}, {"ancestorTitles": ["Token Refresh Mechanism Tests (TEST-006)", "Token Refresh During API Calls"], "fullName": "Token Refresh Mechanism Tests (TEST-006) Token Refresh During API Calls should refresh token when API returns 401 Unauthorized", "status": "passed", "title": "should refresh token when API returns 401 Unauthorized", "duration": 1.2324489999991783, "failureMessages": [], "location": {"line": 184, "column": 7}, "meta": {}}, {"ancestorTitles": ["Token Refresh Mechanism Tests (TEST-006)", "Token Refresh During API Calls"], "fullName": "Token Refresh Mechanism Tests (TEST-006) Token Refresh During API Calls should logout user if token refresh fails during API call", "status": "passed", "title": "should logout user if token refresh fails during API call", "duration": 8.142628999999943, "failureMessages": [], "location": {"line": 251, "column": 7}, "meta": {}}, {"ancestorTitles": ["Token Refresh Mechanism Tests (TEST-006)", "Concurrent Token Refresh"], "fullName": "Token Refresh Mechanism Tests (TEST-006) Concurrent Token Refresh should handle multiple simultaneous refresh requests", "status": "passed", "title": "should handle multiple simultaneous refresh requests", "duration": 1.0200019999992946, "failureMessages": [], "location": {"line": 303, "column": 7}, "meta": {}}, {"ancestorTitles": ["Token Refresh Mechanism Tests (TEST-006)", "Token Refresh State Management"], "fullName": "Token Refresh Mechanism Tests (TEST-006) Token Refresh State Management should set loading state during token refresh", "status": "passed", "title": "should set loading state during token refresh", "duration": 1.0057660000002215, "failureMessages": [], "location": {"line": 339, "column": 7}, "meta": {}}, {"ancestorTitles": ["Token Refresh Mechanism Tests (TEST-006)", "Token Refresh State Management"], "fullName": "Token Refresh Mechanism Tests (TEST-006) Token Refresh State Management should clear error state on successful token refresh", "status": "passed", "title": "should clear error state on successful token refresh", "duration": 0.9450880000003963, "failureMessages": [], "location": {"line": 364, "column": 7}, "meta": {}}], "startTime": 1752959414556, "endTime": 1752959414604.945, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/integration/token-refresh.test.js"}, {"assertionResults": [{"ancestorTitles": ["Authentication Composable (TEST-002)"], "fullName": "Authentication Composable (TEST-002) should be properly configured", "status": "passed", "title": "should be properly configured", "duration": 2.361856000001353, "failureMessages": [], "location": {"line": 149, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "State Access"], "fullName": "Authentication Composable (TEST-002) State Access should provide access to authentication state", "status": "passed", "title": "should provide access to authentication state", "duration": 0.8704150000012305, "failureMessages": [], "location": {"line": 154, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "State Access"], "fullName": "Authentication Composable (TEST-002) State Access should reflect changes in authentication state", "status": "passed", "title": "should reflect changes in authentication state", "duration": 2.3524710000001505, "failureMessages": [], "location": {"line": 164, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Login Function"], "fullName": "Authentication Composable (TEST-002) Login Function should login and redirect on success", "status": "passed", "title": "should login and redirect on success", "duration": 2.349670999999944, "failureMessages": [], "location": {"line": 178, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Login Function"], "fullName": "Authentication Composable (TEST-002) Login Function should not redirect on login failure", "status": "passed", "title": "should not redirect on login failure", "duration": 0.7482280000003811, "failureMessages": [], "location": {"line": 191, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Register Function"], "fullName": "Authentication Composable (TEST-002) Register Function should register and redirect to login on success", "status": "passed", "title": "should register and redirect to login on success", "duration": 17.381750000000466, "failureMessages": [], "location": {"line": 206, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Register Function"], "fullName": "Authentication Composable (TEST-002) Register Function should not redirect on registration failure", "status": "passed", "title": "should not redirect on registration failure", "duration": 0.44200199999977485, "failureMessages": [], "location": {"line": 219, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Logout Function"], "fullName": "Authentication Composable (TEST-002) Logout Function should logout and redirect to login", "status": "passed", "title": "should logout and redirect to login", "duration": 1.2326040000007197, "failureMessages": [], "location": {"line": 233, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Token Refresh Function"], "fullName": "Authentication Composable (TEST-002) Token Refresh Function should refresh token", "status": "passed", "title": "should refresh token", "duration": 0.5561820000002626, "failureMessages": [], "location": {"line": 246, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Password Reset Functions"], "fullName": "Authentication Composable (TEST-002) Password Reset Functions should request password reset", "status": "passed", "title": "should request password reset", "duration": 0.4588960000000952, "failureMessages": [], "location": {"line": 259, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Password Reset Functions"], "fullName": "Authentication Composable (TEST-002) Password Reset Functions should reset password and redirect to login on success", "status": "passed", "title": "should reset password and redirect to login on success", "duration": 1.7136119999995572, "failureMessages": [], "location": {"line": 271, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Profile Management Functions"], "fullName": "Authentication Composable (TEST-002) Profile Management Functions should update profile", "status": "passed", "title": "should update profile", "duration": 0.48499399999855086, "failureMessages": [], "location": {"line": 286, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Profile Management Functions"], "fullName": "Authentication Composable (TEST-002) Profile Management Functions should change password", "status": "passed", "title": "should change password", "duration": 3.5526730000001407, "failureMessages": [], "location": {"line": 298, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Route Guards"], "fullName": "Authentication Composable (TEST-002) Route Guards should allow access when authenticated (requireAuth)", "status": "passed", "title": "should allow access when authenticated (requireAuth)", "duration": 0.31365999999979977, "failureMessages": [], "location": {"line": 312, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Route Guards"], "fullName": "Authentication Composable (TEST-002) Route Guards should redirect to login when not authenticated (requireAuth)", "status": "passed", "title": "should redirect to login when not authenticated (requireAuth)", "duration": 0.37805499999922176, "failureMessages": [], "location": {"line": 322, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Route Guards"], "fullName": "Authentication Composable (TEST-002) Route Guards should allow access when not authenticated (requireGuest)", "status": "passed", "title": "should allow access when not authenticated (requireGuest)", "duration": 0.26105899999856774, "failureMessages": [], "location": {"line": 332, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Route Guards"], "fullName": "Authentication Composable (TEST-002) Route Guards should redirect to home when authenticated (requireGuest)", "status": "passed", "title": "should redirect to home when authenticated (requireGuest)", "duration": 0.24708999999893422, "failureMessages": [], "location": {"line": 342, "column": 7}, "meta": {}}], "startTime": 1752959414368, "endTime": 1752959414406.3782, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/unit/composables/useAuth.test.js"}, {"assertionResults": [{"ancestorTitles": ["useSessionManagement", "initialization"], "fullName": "useSessionManagement initialization should initialize with empty sessions", "status": "passed", "title": "should initialize with empty sessions", "duration": 11.870789999999033, "failureMessages": [], "location": {"line": 63, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "fetchSessions"], "fullName": "useSessionManagement fetchSessions should fetch sessions successfully", "status": "passed", "title": "should fetch sessions successfully", "duration": 3.7747959999996965, "failureMessages": [], "location": {"line": 81, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "fetchSessions"], "fullName": "useSessionManagement fetchSessions should handle fetch errors", "status": "passed", "title": "should handle fetch errors", "duration": 1.1042359999992186, "failureMessages": [], "location": {"line": 142, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "fetchSessions"], "fullName": "useSessionManagement fetchSessions should handle API errors", "status": "passed", "title": "should handle API errors", "duration": 0.9390569999995932, "failureMessages": [], "location": {"line": 163, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "fetchSessions"], "fullName": "useSessionManagement fetchSessions should handle API response errors", "status": "passed", "title": "should handle API response errors", "duration": 1.8237669999998616, "failureMessages": [], "location": {"line": 186, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "fetchSessions"], "fullName": "useSessionManagement fetchSessions should not fetch when unauthenticated", "status": "passed", "title": "should not fetch when unauthenticated", "duration": 1.265503999999055, "failureMessages": [], "location": {"line": 211, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "terminateSession"], "fullName": "useSessionManagement terminateSession should terminate session successfully", "status": "passed", "title": "should terminate session successfully", "duration": 2.6820299999999406, "failureMessages": [], "location": {"line": 225, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "terminateSession"], "fullName": "useSessionManagement terminateSession should handle termination errors", "status": "passed", "title": "should handle termination errors", "duration": 3.8535640000009153, "failureMessages": [], "location": {"line": 289, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "terminateSession"], "fullName": "useSessionManagement terminateSession should handle API termination errors", "status": "passed", "title": "should handle API termination errors", "duration": 1.3681660000002012, "failureMessages": [], "location": {"line": 306, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "terminateAllOtherSessions"], "fullName": "useSessionManagement terminateAllOtherSessions should terminate all other sessions successfully", "status": "passed", "title": "should terminate all other sessions successfully", "duration": 1.165710000001127, "failureMessages": [], "location": {"line": 329, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "terminateAllOtherSessions"], "fullName": "useSessionManagement terminateAllOtherSessions should handle termination errors", "status": "passed", "title": "should handle termination errors", "duration": 0.6825040000003355, "failureMessages": [], "location": {"line": 381, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "refreshCurrentSession"], "fullName": "useSessionManagement refreshCurrentSession should refresh current session successfully", "status": "passed", "title": "should refresh current session successfully", "duration": 1.1650229999995645, "failureMessages": [], "location": {"line": 400, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "refreshCurrentSession"], "fullName": "useSessionManagement refreshCurrentSession should handle refresh errors", "status": "passed", "title": "should handle refresh errors", "duration": 0.6473659999992378, "failureMessages": [], "location": {"line": 431, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "computed properties"], "fullName": "useSessionManagement computed properties should compute active sessions correctly", "status": "passed", "title": "should compute active sessions correctly", "duration": 0.7503729999989446, "failureMessages": [], "location": {"line": 450, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "computed properties"], "fullName": "useSessionManagement computed properties should identify current session correctly", "status": "passed", "title": "should identify current session correctly", "duration": 0.8963309999999183, "failureMessages": [], "location": {"line": 482, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "computed properties"], "fullName": "useSessionManagement computed properties should compute other sessions correctly", "status": "passed", "title": "should compute other sessions correctly", "duration": 1.0769450000007055, "failureMessages": [], "location": {"line": 515, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "computed properties"], "fullName": "useSessionManagement computed properties should compute session statistics correctly", "status": "passed", "title": "should compute session statistics correctly", "duration": 1.1375970000008238, "failureMessages": [], "location": {"line": 548, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "utility functions"], "fullName": "useSessionManagement utility functions should detect device type correctly", "status": "passed", "title": "should detect device type correctly", "duration": 1.5040890000000218, "failureMessages": [], "location": {"line": 587, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "utility functions"], "fullName": "useSessionManagement utility functions should detect browser name correctly", "status": "passed", "title": "should detect browser name correctly", "duration": 4.212271999998848, "failureMessages": [], "location": {"line": 604, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "CSRF token handling"], "fullName": "useSessionManagement CSRF token handling should get CSRF token successfully", "status": "passed", "title": "should get CSRF token successfully", "duration": 1.4597150000008696, "failureMessages": [], "location": {"line": 623, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "CSRF token handling"], "fullName": "useSessionManagement CSRF token handling should handle CSRF token errors gracefully", "status": "passed", "title": "should handle CSRF token errors gracefully", "duration": 0.7337769999994634, "failureMessages": [], "location": {"line": 652, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "auto refresh"], "fullName": "useSessionManagement auto refresh should start auto refresh", "status": "passed", "title": "should start auto refresh", "duration": 1.057451000000583, "failureMessages": [], "location": {"line": 673, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "auto refresh"], "fullName": "useSessionManagement auto refresh should stop auto refresh", "status": "passed", "title": "should stop auto refresh", "duration": 0.7649799999999232, "failureMessages": [], "location": {"line": 682, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "auto refresh"], "fullName": "useSessionManagement auto refresh should refresh sessions automatically", "status": "passed", "title": "should refresh sessions automatically", "duration": 1.2888370000000577, "failureMessages": [], "location": {"line": 693, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "error handling"], "fullName": "useSessionManagement error handling should clear errors", "status": "passed", "title": "should clear errors", "duration": 0.9661580000010872, "failureMessages": [], "location": {"line": 714, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "new session detection"], "fullName": "useSessionManagement new session detection should detect new sessions", "status": "passed", "title": "should detect new sessions", "duration": 0.8243129999991652, "failureMessages": [], "location": {"line": 737, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "new session detection"], "fullName": "useSessionManagement new session detection should handle no new sessions", "status": "passed", "title": "should handle no new sessions", "duration": 0.6897030000000086, "failureMessages": [], "location": {"line": 789, "column": 7}, "meta": {}}], "startTime": 1752959410302, "endTime": 1752959410352.6897, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionManagement.test.js"}, {"assertionResults": [{"ancestorTitles": ["useSessionTimeout", "initialization"], "fullName": "useSessionTimeout initialization should initialize with default configuration", "status": "passed", "title": "should initialize with default configuration", "duration": 8.46884399999908, "failureMessages": [], "location": {"line": 93, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "initialization"], "fullName": "useSessionTimeout initialization should accept custom configuration", "status": "failed", "title": "should accept custom configuration", "duration": 9.446642999999312, "failureMessages": ["AssertionError: expected 3000000 to be greater than 3000000\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionTimeout.test.js:119:38\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)", "AssertionError: expected 3000000 to be greater than 3000000\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionTimeout.test.js:119:38\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)", "AssertionError: expected 3000000 to be greater than 3000000\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionTimeout.test.js:119:38\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)"], "location": {"line": 109, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "activity tracking"], "fullName": "useSessionTimeout activity tracking should register activity event listeners", "status": "failed", "title": "should register activity event listeners", "duration": 14.121276000001671, "failureMessages": ["AssertionError: expected false to be true // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionTimeout.test.js:132:43\n    at Array.forEach (<anonymous>)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionTimeout.test.js:131:22\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)", "AssertionError: expected false to be true // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionTimeout.test.js:132:43\n    at Array.forEach (<anonymous>)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionTimeout.test.js:131:22\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)", "AssertionError: expected false to be true // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionTimeout.test.js:132:43\n    at Array.forEach (<anonymous>)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionTimeout.test.js:131:22\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)"], "location": {"line": 124, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "activity tracking"], "fullName": "useSessionTimeout activity tracking should update activity timestamp on user activity", "status": "passed", "title": "should update activity timestamp on user activity", "duration": 3.2906179999990854, "failureMessages": [], "location": {"line": 137, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "activity tracking"], "fullName": "useSessionTimeout activity tracking should handle localStorage errors gracefully", "status": "passed", "title": "should handle localStorage errors gracefully", "duration": 3.190705999999409, "failureMessages": [], "location": {"line": 154, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "warning system"], "fullName": "useSessionTimeout warning system should show warning when approaching timeout", "status": "passed", "title": "should show warning when approaching timeout", "duration": 1.9209129999999277, "failureMessages": [], "location": {"line": 166, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "warning system"], "fullName": "useSessionTimeout warning system should hide warning when activity is detected", "status": "passed", "title": "should hide warning when activity is detected", "duration": 1.299924999999348, "failureMessages": [], "location": {"line": 182, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "warning system"], "fullName": "useSessionTimeout warning system should format time remaining correctly", "status": "passed", "title": "should format time remaining correctly", "duration": 0.8919189999996888, "failureMessages": [], "location": {"line": 202, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "session extension"], "fullName": "useSessionTimeout session extension should extend session successfully", "status": "passed", "title": "should extend session successfully", "duration": 1.8494620000001305, "failureMessages": [], "location": {"line": 222, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "session extension"], "fullName": "useSessionTimeout session extension should handle session extension failure", "status": "passed", "title": "should handle session extension failure", "duration": 1.6613109999998414, "failureMessages": [], "location": {"line": 242, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "session extension"], "fullName": "useSessionTimeout session extension should handle network errors during extension", "status": "passed", "title": "should handle network errors during extension", "duration": 1.2426080000004731, "failureMessages": [], "location": {"line": 257, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "session timeout handling"], "fullName": "useSessionTimeout session timeout handling should logout user when session times out", "status": "failed", "title": "should logout user when session times out", "duration": 5.368641999999454, "failureMessages": ["AssertionError: expected \"spy\" to be called at least once\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1335:10)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionTimeout.test.js:295:36\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected \"spy\" to be called at least once\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1335:10)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionTimeout.test.js:295:36\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected \"spy\" to be called at least once\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1335:10)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionTimeout.test.js:295:36\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 270, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "session timeout handling"], "fullName": "useSessionTimeout session timeout handling should redirect to login even if logout fails", "status": "failed", "title": "should redirect to login even if logout fails", "duration": 4.7549829999989015, "failureMessages": ["AssertionError: expected \"spy\" to be called with arguments: [ { name: 'login' } ]\u001b[90m\n\nNumber of calls: \u001b[1m0\u001b[22m\n\u001b[39m\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1355:10)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionTimeout.test.js:326:31\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected \"spy\" to be called with arguments: [ { name: 'login' } ]\u001b[90m\n\nNumber of calls: \u001b[1m0\u001b[22m\n\u001b[39m\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1355:10)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionTimeout.test.js:326:31\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected \"spy\" to be called with arguments: [ { name: 'login' } ]\u001b[90m\n\nNumber of calls: \u001b[1m0\u001b[22m\n\u001b[39m\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1355:10)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionTimeout.test.js:326:31\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 302, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "cross-tab communication"], "fullName": "useSessionTimeout cross-tab communication should check for activity in other tabs", "status": "passed", "title": "should check for activity in other tabs", "duration": 1.093949000000066, "failureMessages": [], "location": {"line": 331, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "cross-tab communication"], "fullName": "useSessionTimeout cross-tab communication should handle localStorage errors during cross-tab check", "status": "passed", "title": "should handle localStorage errors during cross-tab check", "duration": 0.8967670000001817, "failureMessages": [], "location": {"line": 345, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "cleanup"], "fullName": "useSessionTimeout cleanup should remove event listeners when stopped", "status": "failed", "title": "should remove event listeners when stopped", "duration": 4.989810999999463, "failureMessages": ["TypeError: [Function removeEventListener] is not a spy or a call to a spy!\n    at assertIsMock (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1299:10)\n    at getSpy (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1303:3)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1307:15)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionTimeout.test.js:365:44\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)", "TypeError: [Function removeEventListener] is not a spy or a call to a spy!\n    at assertIsMock (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1299:10)\n    at getSpy (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1303:3)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1307:15)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionTimeout.test.js:365:44\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)", "TypeError: [Function removeEventListener] is not a spy or a call to a spy!\n    at assertIsMock (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1299:10)\n    at getSpy (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1303:3)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1307:15)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionTimeout.test.js:365:44\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)"], "location": {"line": 357, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "cleanup"], "fullName": "useSessionTimeout cleanup should clear timers when stopped", "status": "passed", "title": "should clear timers when stopped", "duration": 0.9205149999997957, "failureMessages": [], "location": {"line": 368, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "session status"], "fullName": "useSessionTimeout session status should provide comprehensive session status", "status": "passed", "title": "should provide comprehensive session status", "duration": 1.5023030000011204, "failureMessages": [], "location": {"line": 380, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "edge cases"], "fullName": "useSessionTimeout edge cases should handle unauthenticated state gracefully", "status": "failed", "title": "should handle unauthenticated state gracefully", "duration": 5.500383000000511, "failureMessages": ["AssertionError: expected true to be false // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionTimeout.test.js:422:30\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)", "AssertionError: expected true to be false // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionTimeout.test.js:422:30\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)", "AssertionError: expected true to be false // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionTimeout.test.js:422:30\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)"], "location": {"line": 405, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "edge cases"], "fullName": "useSessionTimeout edge cases should handle multiple start/stop cycles", "status": "passed", "title": "should handle multiple start/stop cycles", "duration": 1.2352799999989656, "failureMessages": [], "location": {"line": 425, "column": 7}, "meta": {}}], "startTime": 1752959408458, "endTime": 1752959408533.2354, "status": "failed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionTimeout.test.js"}, {"assertionResults": [{"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Login API"], "fullName": "Authentication API Middleware (TEST-004) Login API should call PocketBase login with correct parameters", "status": "passed", "title": "should call PocketBase login with correct parameters", "duration": 5.584082999999737, "failureMessages": [], "location": {"line": 73, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Login API"], "fullName": "Authentication API Middleware (TEST-004) Login API should handle login API errors", "status": "passed", "title": "should handle login API errors", "duration": 2.0262370000000374, "failureMessages": [], "location": {"line": 88, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Login API"], "fullName": "Authentication API Middleware (TEST-004) Login API should validate input parameters", "status": "passed", "title": "should validate input parameters", "duration": 0.6098000000001775, "failureMessages": [], "location": {"line": 95, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Registration API"], "fullName": "Authentication API Middleware (TEST-004) Registration API should call PocketBase register with correct parameters", "status": "passed", "title": "should call PocketBase register with correct parameters", "duration": 1.2154269999991811, "failureMessages": [], "location": {"line": 105, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Registration API"], "fullName": "Authentication API Middleware (TEST-004) Registration API should handle registration API errors", "status": "passed", "title": "should handle registration API errors", "duration": 0.5727119999992283, "failureMessages": [], "location": {"line": 124, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Registration API"], "fullName": "Authentication API Middleware (TEST-004) Registration API should validate registration data", "status": "passed", "title": "should validate registration data", "duration": 0.5707760000004782, "failureMessages": [], "location": {"line": 132, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Token Refresh API"], "fullName": "Authentication API Middleware (TEST-004) Token Refresh API should call PocketBase refreshToken", "status": "passed", "title": "should call PocketBase refreshToken", "duration": 3.492687999998452, "failureMessages": [], "location": {"line": 140, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Token Refresh API"], "fullName": "Authentication API Middleware (TEST-004) Token Refresh API should handle token refresh API errors", "status": "passed", "title": "should handle token refresh API errors", "duration": 0.42744300000049407, "failureMessages": [], "location": {"line": 155, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Token Refresh API"], "fullName": "Authentication API Middleware (TEST-004) Token Refresh API should handle expired refresh tokens", "status": "passed", "title": "should handle expired refresh tokens", "duration": 0.4568950000011682, "failureMessages": [], "location": {"line": 162, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Password Reset API"], "fullName": "Authentication API Middleware (TEST-004) Password Reset API should call PocketBase requestPasswordReset", "status": "passed", "title": "should call PocketBase requestPasswordReset", "duration": 0.44020199999977194, "failureMessages": [], "location": {"line": 174, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Password Reset API"], "fullName": "Authentication API Middleware (TEST-004) Password Reset API should call PocketBase resetPassword", "status": "passed", "title": "should call PocketBase resetPassword", "duration": 0.47133299999950395, "failureMessages": [], "location": {"line": 184, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Password Reset API"], "fullName": "Authentication API Middleware (TEST-004) Password Reset API should validate email format for password reset", "status": "failed", "title": "should validate email format for password reset", "duration": 10.00253100000009, "failureMessages": ["Error: promise resolved \"{ success: true }\" instead of rejecting\n    at Assertion.__VITEST_REJECTS__ (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1610:17)\n    at Assertion.propertyGetter (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1486:27)\n    at Reflect.get (<anonymous>)\n    at Object.proxyGetter [as get] (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1577:22)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/auth-middleware.test.js:196:70\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)", "Error: promise resolved \"{ success: true }\" instead of rejecting\n    at Assertion.__VITEST_REJECTS__ (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1610:17)\n    at Assertion.propertyGetter (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1486:27)\n    at Reflect.get (<anonymous>)\n    at Object.proxyGetter [as get] (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1577:22)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/auth-middleware.test.js:196:70\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)", "Error: promise resolved \"{ success: true }\" instead of rejecting\n    at Assertion.__VITEST_REJECTS__ (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1610:17)\n    at Assertion.propertyGetter (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1486:27)\n    at Reflect.get (<anonymous>)\n    at Object.proxyGetter [as get] (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1577:22)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/auth-middleware.test.js:196:70\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)"], "location": {"line": 195, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Password Reset API"], "fullName": "Authentication API Middleware (TEST-004) Password Reset API should validate reset token and password", "status": "failed", "title": "should validate reset token and password", "duration": 2.446728000000803, "failureMessages": ["Error: promise resolved \"{ success: true }\" instead of rejecting\n    at Assertion.__VITEST_REJECTS__ (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1610:17)\n    at Assertion.propertyGetter (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1486:27)\n    at Reflect.get (<anonymous>)\n    at Object.proxyGetter [as get] (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1577:22)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/auth-middleware.test.js:201:59\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)", "Error: promise resolved \"{ success: true }\" instead of rejecting\n    at Assertion.__VITEST_REJECTS__ (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1610:17)\n    at Assertion.propertyGetter (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1486:27)\n    at Reflect.get (<anonymous>)\n    at Object.proxyGetter [as get] (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1577:22)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/auth-middleware.test.js:201:59\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)", "Error: promise resolved \"{ success: true }\" instead of rejecting\n    at Assertion.__VITEST_REJECTS__ (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1610:17)\n    at Assertion.propertyGetter (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1486:27)\n    at Reflect.get (<anonymous>)\n    at Object.proxyGetter [as get] (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1577:22)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/auth-middleware.test.js:201:59\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)"], "location": {"line": 199, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Profile Management API"], "fullName": "Authentication API Middleware (TEST-004) Profile Management API should call PocketBase updateProfile", "status": "passed", "title": "should call PocketBase updateProfile", "duration": 0.6749820000004547, "failureMessages": [], "location": {"line": 206, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Profile Management API"], "fullName": "Authentication API Middleware (TEST-004) Profile Management API should call PocketBase changePassword", "status": "passed", "title": "should call PocketBase changePassword", "duration": 1.487725999999384, "failureMessages": [], "location": {"line": 220, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Profile Management API"], "fullName": "Authentication API Middleware (TEST-004) Profile Management API should validate profile update data", "status": "failed", "title": "should validate profile update data", "duration": 3.0308139999997366, "failureMessages": ["Error: promise resolved \"{ success: true, …(1) }\" instead of rejecting\n    at Assertion.__VITEST_REJECTS__ (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1610:17)\n    at Assertion.propertyGetter (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1486:27)\n    at Reflect.get (<anonymous>)\n    at Object.proxyGetter [as get] (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1577:22)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/auth-middleware.test.js:233:59\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)", "Error: promise resolved \"{ success: true, …(1) }\" instead of rejecting\n    at Assertion.__VITEST_REJECTS__ (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1610:17)\n    at Assertion.propertyGetter (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1486:27)\n    at Reflect.get (<anonymous>)\n    at Object.proxyGetter [as get] (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1577:22)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/auth-middleware.test.js:233:59\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)", "Error: promise resolved \"{ success: true, …(1) }\" instead of rejecting\n    at Assertion.__VITEST_REJECTS__ (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1610:17)\n    at Assertion.propertyGetter (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1486:27)\n    at Reflect.get (<anonymous>)\n    at Object.proxyGetter [as get] (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1577:22)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/auth-middleware.test.js:233:59\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)"], "location": {"line": 231, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Profile Management API"], "fullName": "Authentication API Middleware (TEST-004) Profile Management API should validate password change data", "status": "failed", "title": "should validate password change data", "duration": 2.1327459999993152, "failureMessages": ["Error: promise resolved \"{ success: true }\" instead of rejecting\n    at Assertion.__VITEST_REJECTS__ (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1610:17)\n    at Assertion.propertyGetter (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1486:27)\n    at Reflect.get (<anonymous>)\n    at Object.proxyGetter [as get] (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1577:22)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/auth-middleware.test.js:238:60\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)", "Error: promise resolved \"{ success: true }\" instead of rejecting\n    at Assertion.__VITEST_REJECTS__ (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1610:17)\n    at Assertion.propertyGetter (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1486:27)\n    at Reflect.get (<anonymous>)\n    at Object.proxyGetter [as get] (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1577:22)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/auth-middleware.test.js:238:60\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)", "Error: promise resolved \"{ success: true }\" instead of rejecting\n    at Assertion.__VITEST_REJECTS__ (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1610:17)\n    at Assertion.propertyGetter (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1486:27)\n    at Reflect.get (<anonymous>)\n    at Object.proxyGetter [as get] (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1577:22)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/auth-middleware.test.js:238:60\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)"], "location": {"line": 236, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Authentication Status API"], "fullName": "Authentication API Middleware (TEST-004) Authentication Status API should call PocketBase isAuthenticated", "status": "passed", "title": "should call PocketBase isAuthenticated", "duration": 1.1502720000007685, "failureMessages": [], "location": {"line": 243, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Authentication Status API"], "fullName": "Authentication API Middleware (TEST-004) Authentication Status API should call PocketBase getCurrentUser", "status": "passed", "title": "should call PocketBase getCurrentUser", "duration": 0.4974180000008346, "failureMessages": [], "location": {"line": 252, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Authentication Status API"], "fullName": "Authentication API Middleware (TEST-004) Authentication Status API should call PocketBase getToken", "status": "passed", "title": "should call PocketBase getToken", "duration": 0.2923579999987851, "failureMessages": [], "location": {"line": 265, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Authentication Status API"], "fullName": "Authentication API Middleware (TEST-004) Authentication Status API should handle authentication check errors", "status": "passed", "title": "should handle authentication check errors", "duration": 0.7104849999996077, "failureMessages": [], "location": {"line": 275, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Logout API"], "fullName": "Authentication API Middleware (TEST-004) Logout API should call PocketBase logout", "status": "passed", "title": "should call PocketBase logout", "duration": 0.5365199999996548, "failureMessages": [], "location": {"line": 283, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Logout API"], "fullName": "Authentication API Middleware (TEST-004) Logout API should handle logout API errors", "status": "passed", "title": "should handle logout API errors", "duration": 0.36317199999939476, "failureMessages": [], "location": {"line": 293, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Logout API"], "fullName": "Authentication API Middleware (TEST-004) Logout API should handle logout when not authenticated", "status": "failed", "title": "should handle logout when not authenticated", "duration": 0.7463069999994332, "failureMessages": ["Error: <PERSON><PERSON><PERSON> failed\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/auth-middleware.test.js:294:25\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)", "Error: <PERSON><PERSON><PERSON> failed\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/auth-middleware.test.js:294:25\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)", "Error: <PERSON><PERSON><PERSON> failed\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/auth-middleware.test.js:294:25\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)"], "location": {"line": 300, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "API Response Handling"], "fullName": "Authentication API Middleware (TEST-004) API Response Handling should handle malformed API responses", "status": "failed", "title": "should handle malformed API responses", "duration": 1.5047020000001794, "failureMessages": ["Error: promise resolved \"null\" instead of rejecting\n    at Assertion.__VITEST_REJECTS__ (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1610:17)\n    at Assertion.propertyGetter (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1486:27)\n    at Reflect.get (<anonymous>)\n    at Object.proxyGetter [as get] (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1577:22)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/auth-middleware.test.js:313:73\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)", "Error: promise resolved \"null\" instead of rejecting\n    at Assertion.__VITEST_REJECTS__ (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1610:17)\n    at Assertion.propertyGetter (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1486:27)\n    at Reflect.get (<anonymous>)\n    at Object.proxyGetter [as get] (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1577:22)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/auth-middleware.test.js:313:73\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)", "Error: promise resolved \"null\" instead of rejecting\n    at Assertion.__VITEST_REJECTS__ (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1610:17)\n    at Assertion.propertyGetter (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1486:27)\n    at Reflect.get (<anonymous>)\n    at Object.proxyGetter [as get] (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1577:22)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/auth-middleware.test.js:313:73\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)"], "location": {"line": 310, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "API Response Handling"], "fullName": "Authentication API Middleware (TEST-004) API Response Handling should handle network timeouts", "status": "passed", "title": "should handle network timeouts", "duration": 101.29301999999916, "failureMessages": [], "location": {"line": 316, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "API Response Handling"], "fullName": "Authentication API Middleware (TEST-004) API Response Handling should handle rate limiting", "status": "passed", "title": "should handle rate limiting", "duration": 0.5299439999998867, "failureMessages": [], "location": {"line": 326, "column": 7}, "meta": {}}], "startTime": 1752959408233, "endTime": 1752959408376.53, "status": "failed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/unit/middleware/auth-middleware.test.js"}, {"assertionResults": [{"ancestorTitles": ["CSRF Middleware", "generateCSRFToken"], "fullName": "CSRF Middleware generateCSRFToken should generate and set CSRF token in cookie and header", "status": "passed", "title": "should generate and set CSRF token in cookie and header", "duration": 85.23253100000147, "failureMessages": [], "location": {"line": 38, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "generateCSRFToken"], "fullName": "CSRF Middleware generateCSRFToken should set secure cookie in production", "status": "passed", "title": "should set secure cookie in production", "duration": 37.9714440000007, "failureMessages": [], "location": {"line": 53, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "validateCSRFToken"], "fullName": "CSRF Middleware validateCSRFToken should skip validation for GET requests", "status": "passed", "title": "should skip validation for GET requests", "duration": 7.4082500000004075, "failureMessages": [], "location": {"line": 70, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "validateCSRFToken"], "fullName": "CSRF Middleware validateCSRFToken should skip validation for HEAD requests", "status": "passed", "title": "should skip validation for HEAD requests", "duration": 5.117459000000963, "failureMessages": [], "location": {"line": 79, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "validateCSRFToken"], "fullName": "CSRF Middleware validateCSRFToken should skip validation for OPTIONS requests", "status": "passed", "title": "should skip validation for OPTIONS requests", "duration": 4.914290000000619, "failureMessages": [], "location": {"line": 88, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "validateCSRFToken"], "fullName": "CSRF Middleware validateCSRFToken should require CSRF token for POST requests", "status": "failed", "title": "should require CSRF token for POST requests", "duration": 79.38292899999942, "failureMessages": ["AssertionError: expected undefined to be false // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/csrf.test.js:104:37\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected undefined to be false // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/csrf.test.js:104:37\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected undefined to be false // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/csrf.test.js:104:37\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 97, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "validateCSRFToken"], "fullName": "CSRF Middleware validateCSRFToken should validate CSRF token from header", "status": "failed", "title": "should validate CSRF token from header", "duration": 56.83737800000017, "failureMessages": ["AssertionError: expected 403 to be 200 // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/csrf.test.js:128:31\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected 403 to be 200 // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/csrf.test.js:128:31\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected 403 to be 200 // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/csrf.test.js:128:31\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 108, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "validateCSRFToken"], "fullName": "CSRF Middleware validateCSRFToken should reject invalid CSRF token", "status": "failed", "title": "should reject invalid CSRF token", "duration": 10.72657000000072, "failureMessages": ["TypeError: Cannot read properties of undefined (reading 'message')\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/csrf.test.js:142:34\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "TypeError: Cannot read properties of undefined (reading 'message')\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/csrf.test.js:142:34\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "TypeError: Cannot read properties of undefined (reading 'message')\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/csrf.test.js:142:34\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 132, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "csrfProtection"], "fullName": "CSRF Middleware csrfProtection should protect authentication endpoints", "status": "passed", "title": "should protect authentication endpoints", "duration": 2.845586999999796, "failureMessages": [], "location": {"line": 147, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "csrfProtection"], "fullName": "CSRF Middleware csrfProtection should protect registration endpoints", "status": "passed", "title": "should protect registration endpoints", "duration": 2.637550999999803, "failureMessages": [], "location": {"line": 156, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "csrfProtection"], "fullName": "CSRF Middleware csrfProtection should not protect non-authentication endpoints", "status": "passed", "title": "should not protect non-authentication endpoints", "duration": 2.556902000000264, "failureMessages": [], "location": {"line": 165, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "csrfProtection"], "fullName": "CSRF Middleware csrfProtection should not protect GET requests to auth endpoints", "status": "passed", "title": "should not protect GET requests to auth endpoints", "duration": 2.2857569999996485, "failureMessages": [], "location": {"line": 174, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "getCSRFToken"], "fullName": "CSRF Middleware getCSRFToken should return null for non-existent session", "status": "passed", "title": "should return null for non-existent session", "duration": 0.4671220000000176, "failureMessages": [], "location": {"line": 185, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "cleanupExpiredTokens"], "fullName": "CSRF Middleware cleanupExpiredTokens should clean up expired tokens", "status": "passed", "title": "should clean up expired tokens", "duration": 66.28948200000013, "failureMessages": [], "location": {"line": 193, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "CSRF token endpoint"], "fullName": "CSRF Middleware CSRF token endpoint should provide CSRF token via API endpoint", "status": "passed", "title": "should provide CSRF token via API endpoint", "duration": 6.30235999999968, "failureMessages": [], "location": {"line": 201, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "Integration with authentication flow"], "fullName": "CSRF Middleware Integration with authentication flow should work with complete authentication flow", "status": "failed", "title": "should work with complete authentication flow", "duration": 68.2894469999992, "failureMessages": ["AssertionError: expected 403 to be 200 // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/csrf.test.js:247:36\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected 403 to be 200 // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/csrf.test.js:247:36\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected 403 to be 200 // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/csrf.test.js:247:36\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 216, "column": 5}, "meta": {}}], "startTime": 1752959411092, "endTime": 1752959411534.2896, "status": "failed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/unit/middleware/csrf.test.js"}, {"assertionResults": [{"ancestorTitles": ["Password Reset Middleware", "storeResetToken"], "fullName": "Password Reset Middleware storeResetToken should store reset token with expiration", "status": "passed", "title": "should store reset token with expiration", "duration": 5.4452650000002905, "failureMessages": [], "location": {"line": 39, "column": 7}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "storeResetToken"], "fullName": "Password Reset Middleware storeResetToken should use default expiration time", "status": "passed", "title": "should use default expiration time", "duration": 0.6607139999996434, "failureMessages": [], "location": {"line": 55, "column": 7}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "validateResetToken"], "fullName": "Password Reset Middleware validateResetToken should validate valid token", "status": "passed", "title": "should validate valid token", "duration": 0.7027849999994942, "failureMessages": [], "location": {"line": 66, "column": 7}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "validateResetToken"], "fullName": "Password Reset Middleware validateResetToken should reject non-existent token", "status": "passed", "title": "should reject non-existent token", "duration": 0.7932960000016465, "failureMessages": [], "location": {"line": 78, "column": 7}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "validateResetToken"], "fullName": "Password Reset Middleware validateResetToken should reject expired token", "status": "passed", "title": "should reject expired token", "duration": 0.4295459999993909, "failureMessages": [], "location": {"line": 85, "column": 7}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "validateResetToken"], "fullName": "Password Reset Middleware validateResetToken should reject used token", "status": "passed", "title": "should reject used token", "duration": 0.697519999999713, "failureMessages": [], "location": {"line": 100, "column": 7}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "validateResetToken"], "fullName": "Password Reset Middleware validateResetToken should increment attempt counter", "status": "passed", "title": "should increment attempt counter", "duration": 0.6504059999988385, "failureMessages": [], "location": {"line": 113, "column": 7}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "validateResetToken"], "fullName": "Password Reset Middleware validateResetToken should reject token after too many attempts", "status": "passed", "title": "should reject token after too many attempts", "duration": 0.688216000000466, "failureMessages": [], "location": {"line": 126, "column": 7}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "markTokenAsUsed"], "fullName": "Password Reset Middleware markTokenAsUsed should mark token as used", "status": "passed", "title": "should mark token as used", "duration": 0.5334100000000035, "failureMessages": [], "location": {"line": 146, "column": 7}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "markTokenAsUsed"], "fullName": "Password Reset Middleware markTokenAsUsed should handle non-existent token gracefully", "status": "passed", "title": "should handle non-existent token gracefully", "duration": 1.5247030000009545, "failureMessages": [], "location": {"line": 157, "column": 7}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "invalidateEmailResetTokens"], "fullName": "Password Reset Middleware invalidateEmailResetTokens should invalidate existing tokens for email", "status": "passed", "title": "should invalidate existing tokens for email", "duration": 0.542222000000038, "failureMessages": [], "location": {"line": 163, "column": 7}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "passwordResetRateLimit"], "fullName": "Password Reset Middleware passwordResetRateLimit should allow requests within rate limit", "status": "passed", "title": "should allow requests within rate limit", "duration": 184.922681, "failureMessages": [], "location": {"line": 185, "column": 7}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "passwordResetRateLimit"], "fullName": "Password Reset Middleware passwordResetRateLimit should block requests exceeding rate limit", "status": "failed", "title": "should block requests exceeding rate limit", "duration": 94.46571199999926, "failureMessages": ["AssertionError: expected 200 to be 429 // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/passwordReset.test.js:215:31\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected 200 to be 429 // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/passwordReset.test.js:215:31\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected 200 to be 429 // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/passwordReset.test.js:215:31\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 198, "column": 7}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "emailResetRateLimit"], "fullName": "Password Reset Middleware emailResetRateLimit should allow requests for different emails", "status": "passed", "title": "should allow requests for different emails", "duration": 4.631250000000364, "failureMessages": [], "location": {"line": 222, "column": 7}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "emailResetRateLimit"], "fullName": "Password Reset Middleware emailResetRateLimit should block excessive requests for same email", "status": "failed", "title": "should block excessive requests for same email", "duration": 75.30401299999903, "failureMessages": ["AssertionError: expected 200 to be 429 // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/passwordReset.test.js:261:31\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected 200 to be 429 // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/passwordReset.test.js:261:31\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected 200 to be 429 // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/passwordReset.test.js:261:31\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 240, "column": 7}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "validateResetTokenMiddleware"], "fullName": "Password Reset Middleware validateResetTokenMiddleware should validate token and attach data to request", "status": "passed", "title": "should validate token and attach data to request", "duration": 3.805691999999908, "failureMessages": [], "location": {"line": 266, "column": 7}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "validateResetTokenMiddleware"], "fullName": "Password Reset Middleware validateResetTokenMiddleware should reject request with invalid token", "status": "passed", "title": "should reject request with invalid token", "duration": 8.925971000000573, "failureMessages": [], "location": {"line": 290, "column": 7}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "validateResetTokenMiddleware"], "fullName": "Password Reset Middleware validateResetTokenMiddleware should reject request without token", "status": "passed", "title": "should reject request without token", "duration": 3.247976999999082, "failureMessages": [], "location": {"line": 302, "column": 7}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "progressiveDelayMiddleware"], "fullName": "Password Reset Middleware progressiveDelayMiddleware should not delay first request", "status": "passed", "title": "should not delay first request", "duration": 3.459262000000308, "failureMessages": [], "location": {"line": 316, "column": 7}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "progressiveDelayMiddleware"], "fullName": "Password Reset Middleware progressiveDelayMiddleware should handle missing email gracefully", "status": "passed", "title": "should handle missing email gracefully", "duration": 18.8661919999995, "failureMessages": [], "location": {"line": 331, "column": 7}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "cleanupExpiredData"], "fullName": "Password Reset Middleware cleanupExpiredData should clean up expired tokens", "status": "passed", "title": "should clean up expired tokens", "duration": 0.41852300000027753, "failureMessages": [], "location": {"line": 345, "column": 7}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "cleanupExpiredData"], "fullName": "Password Reset Middleware cleanupExpiredData should not throw errors during cleanup", "status": "passed", "title": "should not throw errors during cleanup", "duration": 8.383735999999772, "failureMessages": [], "location": {"line": 362, "column": 7}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "getResetTokenStats"], "fullName": "Password Reset Middleware getResetTokenStats should return correct statistics", "status": "passed", "title": "should return correct statistics", "duration": 1.7776969999995345, "failureMessages": [], "location": {"line": 368, "column": 7}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "Integration tests"], "fullName": "Password Reset Middleware Integration tests should handle complete password reset flow", "status": "passed", "title": "should handle complete password reset flow", "duration": 1.1417330000003858, "failureMessages": [], "location": {"line": 393, "column": 7}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "Integration tests"], "fullName": "Password Reset Middleware Integration tests should handle token expiration correctly", "status": "passed", "title": "should handle token expiration correctly", "duration": 0.4264979999989009, "failureMessages": [], "location": {"line": 413, "column": 7}, "meta": {}}], "startTime": 1752959411054, "endTime": 1752959411477.4265, "status": "failed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/unit/middleware/passwordReset.test.js"}, {"assertionResults": [{"ancestorTitles": ["Route Guards (TEST-003)", "Authentication Guard"], "fullName": "Route Guards (TEST-003) Authentication Guard should initialize auth store if not initialized", "status": "passed", "title": "should initialize auth store if not initialized", "duration": 3.53924299999926, "failureMessages": [], "location": {"line": 106, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Authentication Guard"], "fullName": "Route Guards (TEST-003) Authentication Guard should not initialize auth store if already initialized", "status": "passed", "title": "should not initialize auth store if already initialized", "duration": 0.9117139999998471, "failureMessages": [], "location": {"line": 120, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Protected Routes"], "fullName": "Route Guards (TEST-003) Protected Routes should allow access to protected routes when authenticated", "status": "passed", "title": "should allow access to protected routes when authenticated", "duration": 0.9443410000003496, "failureMessages": [], "location": {"line": 135, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Protected Routes"], "fullName": "Route Guards (TEST-003) Protected Routes should redirect to login when accessing protected routes while unauthenticated", "status": "passed", "title": "should redirect to login when accessing protected routes while unauthenticated", "duration": 2.7279600000001665, "failureMessages": [], "location": {"line": 148, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Protected Routes"], "fullName": "Route Guards (TEST-003) Protected Routes should preserve query parameters in redirect", "status": "passed", "title": "should preserve query parameters in redirect", "duration": 0.6189479999993637, "failureMessages": [], "location": {"line": 164, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Guest-Only Routes"], "fullName": "Route Guards (TEST-003) Guest-Only Routes should allow access to guest-only routes when unauthenticated", "status": "passed", "title": "should allow access to guest-only routes when unauthenticated", "duration": 0.6181390000001556, "failureMessages": [], "location": {"line": 183, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Guest-Only Routes"], "fullName": "Route Guards (TEST-003) Guest-Only Routes should redirect to home when accessing guest-only routes while authenticated", "status": "passed", "title": "should redirect to home when accessing guest-only routes while authenticated", "duration": 0.697969999999259, "failureMessages": [], "location": {"line": 196, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Guest-Only Routes"], "fullName": "Route Guards (TEST-003) Guest-Only Routes should redirect authenticated users from register page", "status": "passed", "title": "should redirect authenticated users from register page", "duration": 1.1807850000004692, "failureMessages": [], "location": {"line": 209, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Guest-Only Routes"], "fullName": "Route Guards (TEST-003) Guest-Only Routes should redirect authenticated users from forgot-password page", "status": "passed", "title": "should redirect authenticated users from forgot-password page", "duration": 0.5287200000002485, "failureMessages": [], "location": {"line": 222, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Admin Routes"], "fullName": "Route Guards (TEST-003) Admin Routes should allow access to admin routes when user is admin", "status": "passed", "title": "should allow access to admin routes when user is admin", "duration": 0.4426609999991342, "failureMessages": [], "location": {"line": 237, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Admin Routes"], "fullName": "Route Guards (TEST-003) Admin Routes should redirect to unauthorized when non-admin user accesses admin routes", "status": "passed", "title": "should redirect to unauthorized when non-admin user accesses admin routes", "duration": 0.549062000000049, "failureMessages": [], "location": {"line": 251, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Admin Routes"], "fullName": "Route Guards (TEST-003) Admin Routes should redirect to login when unauthenticated user accesses admin routes", "status": "passed", "title": "should redirect to login when unauthenticated user accesses admin routes", "duration": 0.4644800000005489, "failureMessages": [], "location": {"line": 265, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Public Routes"], "fullName": "Route Guards (TEST-003) Public Routes should allow access to public routes regardless of authentication status", "status": "passed", "title": "should allow access to public routes regardless of authentication status", "duration": 0.89392399999997, "failureMessages": [], "location": {"line": 284, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Public Routes"], "fullName": "Route Guards (TEST-003) Public Routes should allow authenticated users to access public routes", "status": "passed", "title": "should allow authenticated users to access public routes", "duration": 0.40552500000012515, "failureMessages": [], "location": {"line": 297, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Erro<PERSON>"], "fullName": "Route Guards (TEST-003) Error Handling should handle auth initialization errors gracefully", "status": "passed", "title": "should handle auth initialization errors gracefully", "duration": 0.5282529999985854, "failureMessages": [], "location": {"line": 312, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Route Meta Combinations"], "fullName": "Route Guards (TEST-003) Route Meta Combinations should handle routes with multiple meta requirements", "status": "passed", "title": "should handle routes with multiple meta requirements", "duration": 0.3002710000000661, "failureMessages": [], "location": {"line": 329, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Route Meta Combinations"], "fullName": "Route Guards (TEST-003) Route Meta Combinations should prioritize authentication check over admin check", "status": "passed", "title": "should prioritize authentication check over admin check", "duration": 0.4280239999989135, "failureMessages": [], "location": {"line": 346, "column": 7}, "meta": {}}], "startTime": 1752959415748, "endTime": 1752959415764.428, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/unit/router/guards.test.js"}, {"assertionResults": [{"ancestorTitles": ["Authentication Routes", "POST /api/auth/login"], "fullName": "Authentication Routes POST /api/auth/login should login user with valid credentials", "status": "passed", "title": "should login user with valid credentials", "duration": 181.0377180000014, "failureMessages": [], "location": {"line": 201, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/login"], "fullName": "Authentication Routes POST /api/auth/login should return validation error for invalid email", "status": "passed", "title": "should return validation error for invalid email", "duration": 5.402697999999873, "failureMessages": [], "location": {"line": 232, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/login"], "fullName": "Authentication Routes POST /api/auth/login should return validation error for missing password", "status": "passed", "title": "should return validation error for missing password", "duration": 7.670543999998699, "failureMessages": [], "location": {"line": 245, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/register"], "fullName": "Authentication Routes POST /api/auth/register should register user with valid data", "status": "passed", "title": "should register user with valid data", "duration": 5.25888599999962, "failureMessages": [], "location": {"line": 259, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/register"], "fullName": "Authentication Routes POST /api/auth/register should return validation error for weak password", "status": "passed", "title": "should return validation error for weak password", "duration": 3.8775000000005093, "failureMessages": [], "location": {"line": 293, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/register"], "fullName": "Authentication Routes POST /api/auth/register should return validation error for password mismatch", "status": "passed", "title": "should return validation error for password mismatch", "duration": 3.984650000000329, "failureMessages": [], "location": {"line": 308, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/logout"], "fullName": "Authentication Routes POST /api/auth/logout should logout user successfully", "status": "passed", "title": "should logout user successfully", "duration": 4.826919999999518, "failureMessages": [], "location": {"line": 325, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/refresh"], "fullName": "Authentication Routes POST /api/auth/refresh should refresh token successfully", "status": "passed", "title": "should refresh token successfully", "duration": 4.176346000000194, "failureMessages": [], "location": {"line": 343, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/refresh"], "fullName": "Authentication Routes POST /api/auth/refresh should return validation error for missing refresh token", "status": "passed", "title": "should return validation error for missing refresh token", "duration": 3.170785000000251, "failureMessages": [], "location": {"line": 363, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/forgot-password"], "fullName": "Authentication Routes POST /api/auth/forgot-password should send password reset email", "status": "passed", "title": "should send password reset email", "duration": 4.791114999999991, "failureMessages": [], "location": {"line": 375, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/forgot-password"], "fullName": "Authentication Routes POST /api/auth/forgot-password should return validation error for invalid email", "status": "passed", "title": "should return validation error for invalid email", "duration": 2.60122599999886, "failureMessages": [], "location": {"line": 393, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/reset-password"], "fullName": "Authentication Routes POST /api/auth/reset-password should reset password successfully", "status": "passed", "title": "should reset password successfully", "duration": 4.555446000000302, "failureMessages": [], "location": {"line": 407, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/reset-password"], "fullName": "Authentication Routes POST /api/auth/reset-password should return validation error for missing token", "status": "passed", "title": "should return validation error for missing token", "duration": 2.63574300000073, "failureMessages": [], "location": {"line": 427, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "GET /api/auth/me"], "fullName": "Authentication Routes GET /api/auth/me should get current user profile", "status": "passed", "title": "should get current user profile", "duration": 4.161759000000529, "failureMessages": [], "location": {"line": 442, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "PUT /api/auth/profile"], "fullName": "Authentication Routes PUT /api/auth/profile should update user profile", "status": "passed", "title": "should update user profile", "duration": 2.9308209999999235, "failureMessages": [], "location": {"line": 467, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "PUT /api/auth/profile"], "fullName": "Authentication Routes PUT /api/auth/profile should return validation error for invalid email", "status": "passed", "title": "should return validation error for invalid email", "duration": 2.7012200000008306, "failureMessages": [], "location": {"line": 499, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "PUT /api/auth/password"], "fullName": "Authentication Routes PUT /api/auth/password should change password successfully", "status": "passed", "title": "should change password successfully", "duration": 75.80133200000091, "failureMessages": [], "location": {"line": 514, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "PUT /api/auth/password"], "fullName": "Authentication Routes PUT /api/auth/password should return validation error for password mismatch", "status": "passed", "title": "should return validation error for password mismatch", "duration": 4.8050120000007155, "failureMessages": [], "location": {"line": 540, "column": 5}, "meta": {}}], "startTime": 1752959411074, "endTime": 1752959411399.805, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/unit/routes/auth.test.js"}, {"assertionResults": [], "startTime": 1752959400365, "endTime": 1752959400365, "status": "failed", "message": "Cannot access 'mockPocketBase' before initialization", "name": "/Volumes/External Drive/Development/track-tasks/test/unit/services/authService.test.js"}, {"assertionResults": [{"ancestorTitles": ["PocketBase Service", "Initialization"], "fullName": "PocketBase Service Initialization should initialize with default URL when no environment variable is set", "status": "passed", "title": "should initialize with default URL when no environment variable is set", "duration": 2.0859849999997095, "failureMessages": [], "location": {"line": 40, "column": 7}, "meta": {}}, {"ancestorTitles": ["PocketBase Service", "Initialization"], "fullName": "PocketBase Service Initialization should load auth state from cookies on initialization", "status": "passed", "title": "should load auth state from cookies on initialization", "duration": 0.44841099999939615, "failureMessages": [], "location": {"line": 45, "column": 7}, "meta": {}}, {"ancestorTitles": ["PocketBase Service", "Authentication Check"], "fullName": "PocketBase Service Authentication Check should return false when user is not authenticated", "status": "passed", "title": "should return false when user is not authenticated", "duration": 0.22304499999881955, "failureMessages": [], "location": {"line": 63, "column": 7}, "meta": {}}, {"ancestorTitles": ["PocketBase Service", "Authentication Check"], "fullName": "PocketBase Service Authentication Check should return true when user is authenticated", "status": "passed", "title": "should return true when user is authenticated", "duration": 0.20041200000014214, "failureMessages": [], "location": {"line": 68, "column": 7}, "meta": {}}, {"ancestorTitles": ["PocketBase Service", "Current User"], "fullName": "PocketBase Service Current User should return null when no user is authenticated", "status": "passed", "title": "should return null when no user is authenticated", "duration": 0.29335099999843806, "failureMessages": [], "location": {"line": 75, "column": 7}, "meta": {}}, {"ancestorTitles": ["PocketBase Service", "Current User"], "fullName": "PocketBase Service Current User should return user data when authenticated", "status": "passed", "title": "should return user data when authenticated", "duration": 1.3798119999992196, "failureMessages": [], "location": {"line": 80, "column": 7}, "meta": {}}, {"ancestorTitles": ["PocketBase Service", "<PERSON><PERSON>"], "fullName": "PocketBase Service Login should successfully authenticate user with valid credentials", "status": "passed", "title": "should successfully authenticate user with valid credentials", "duration": 2.8294630000000325, "failureMessages": [], "location": {"line": 88, "column": 7}, "meta": {}}, {"ancestorTitles": ["PocketBase Service", "<PERSON><PERSON>"], "fullName": "PocketBase Service Login should return error when login fails", "status": "passed", "title": "should return error when login fails", "duration": 0.5731159999995725, "failureMessages": [], "location": {"line": 102, "column": 7}, "meta": {}}, {"ancestorTitles": ["PocketBase Service", "Registration"], "fullName": "PocketBase Service Registration should successfully register a new user", "status": "passed", "title": "should successfully register a new user", "duration": 1.0253339999999298, "failureMessages": [], "location": {"line": 114, "column": 7}, "meta": {}}, {"ancestorTitles": ["PocketBase Service", "Registration"], "fullName": "PocketBase Service Registration should return error when registration fails", "status": "passed", "title": "should return error when registration fails", "duration": 0.4135900000001129, "failureMessages": [], "location": {"line": 128, "column": 7}, "meta": {}}, {"ancestorTitles": ["PocketBase Service", "Logout"], "fullName": "PocketBase Service Logout should clear auth store on logout", "status": "passed", "title": "should clear auth store on logout", "duration": 0.3181700000004639, "failureMessages": [], "location": {"line": 140, "column": 7}, "meta": {}}, {"ancestorTitles": ["PocketBase Service", "Collection Access"], "fullName": "PocketBase Service Collection Access should return collection reference", "status": "passed", "title": "should return collection reference", "duration": 0.27979600000071514, "failureMessages": [], "location": {"line": 147, "column": 7}, "meta": {}}, {"ancestorTitles": ["PocketBase Service", "Real-time Subscriptions"], "fullName": "PocketBase Service Real-time Subscriptions should subscribe to real-time updates", "status": "passed", "title": "should subscribe to real-time updates", "duration": 0.8011100000003353, "failureMessages": [], "location": {"line": 155, "column": 7}, "meta": {}}, {"ancestorTitles": ["PocketBase Service", "Real-time Subscriptions"], "fullName": "PocketBase Service Real-time Subscriptions should unsubscribe from real-time updates", "status": "passed", "title": "should unsubscribe from real-time updates", "duration": 0.3318310000013298, "failureMessages": [], "location": {"line": 164, "column": 7}, "meta": {}}, {"ancestorTitles": ["PocketBase Service", "Client Access"], "fullName": "PocketBase Service Client Access should return PocketBase client instance", "status": "passed", "title": "should return PocketBase client instance", "duration": 0.16509899999982736, "failureMessages": [], "location": {"line": 174, "column": 7}, "meta": {}}], "startTime": 1752959415717, "endTime": 1752959415729.3318, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/unit/services/pocketbase.test.js"}, {"assertionResults": [{"ancestorTitles": ["SessionService", "createSession"], "fullName": "SessionService createSession should create a new session with valid data", "status": "passed", "title": "should create a new session with valid data", "duration": 4.13981799999965, "failureMessages": [], "location": {"line": 23, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "createSession"], "fullName": "SessionService createSession should generate unique session IDs", "status": "passed", "title": "should generate unique session IDs", "duration": 1.7572000000000116, "failureMessages": [], "location": {"line": 42, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "getSession"], "fullName": "SessionService getSession should retrieve session by ID", "status": "passed", "title": "should retrieve session by ID", "duration": 0.40781499999866355, "failureMessages": [], "location": {"line": 51, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "getSession"], "fullName": "SessionService getSession should return null for non-existent session", "status": "passed", "title": "should return null for non-existent session", "duration": 0.2741249999999127, "failureMessages": [], "location": {"line": 60, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "getSessionByToken"], "fullName": "SessionService getSessionByToken should retrieve session by token", "status": "passed", "title": "should retrieve session by token", "duration": 0.37204100000053586, "failureMessages": [], "location": {"line": 67, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "getSessionByToken"], "fullName": "SessionService getSessionByToken should return null for non-existent token", "status": "passed", "title": "should return null for non-existent token", "duration": 0.25296200000047975, "failureMessages": [], "location": {"line": 77, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "getUserSessions"], "fullName": "SessionService getUserSessions should return all active sessions for a user", "status": "passed", "title": "should return all active sessions for a user", "duration": 2.818401000000449, "failureMessages": [], "location": {"line": 84, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "getUserSessions"], "fullName": "SessionService getUserSessions should return empty array for user with no sessions", "status": "passed", "title": "should return empty array for user with no sessions", "duration": 0.46813099999963015, "failureMessages": [], "location": {"line": 99, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "updateSessionActivity"], "fullName": "SessionService updateSessionActivity should update last activity for valid session", "status": "passed", "title": "should update last activity for valid session", "duration": 13.947557000001325, "failureMessages": [], "location": {"line": 106, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "updateSessionActivity"], "fullName": "SessionService updateSessionActivity should return false for non-existent token", "status": "passed", "title": "should return false for non-existent token", "duration": 0.28968800000075134, "failureMessages": [], "location": {"line": 121, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "updateSessionActivity"], "fullName": "SessionService updateSessionActivity should return false for inactive session", "status": "passed", "title": "should return false for inactive session", "duration": 0.2613739999997051, "failureMessages": [], "location": {"line": 126, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "invalidateSession"], "fullName": "SessionService invalidateSession should invalidate session by ID", "status": "passed", "title": "should invalidate session by ID", "duration": 0.27230799999961164, "failureMessages": [], "location": {"line": 139, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "invalidateSession"], "fullName": "SessionService invalidateSession should add token to blacklist when invalidating", "status": "passed", "title": "should add token to blacklist when invalidating", "duration": 0.19124100000044564, "failureMessages": [], "location": {"line": 149, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "invalidateSession"], "fullName": "SessionService invalidateSession should return false for non-existent session", "status": "passed", "title": "should return false for non-existent session", "duration": 0.5494479999997566, "failureMessages": [], "location": {"line": 159, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "invalidateSessionByToken"], "fullName": "SessionService invalidateSessionByToken should invalidate session by token", "status": "passed", "title": "should invalidate session by token", "duration": 0.5965249999990192, "failureMessages": [], "location": {"line": 166, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "invalidateSessionByToken"], "fullName": "SessionService invalidateSessionByToken should return false for non-existent token", "status": "passed", "title": "should return false for non-existent token", "duration": 0.16354300000057265, "failureMessages": [], "location": {"line": 178, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "invalidateAllUserSessions"], "fullName": "SessionService invalidateAllUserSessions should invalidate all sessions for a user", "status": "passed", "title": "should invalidate all sessions for a user", "duration": 0.47608000000036554, "failureMessages": [], "location": {"line": 185, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "invalidateAllUserSessions"], "fullName": "SessionService invalidateAllUserSessions should exclude specified token from invalidation", "status": "passed", "title": "should exclude specified token from invalidation", "duration": 0.42425400000138325, "failureMessages": [], "location": {"line": 203, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "isTokenBlacklisted"], "fullName": "SessionService isTokenBlacklisted should return true for blacklisted tokens", "status": "passed", "title": "should return true for blacklisted tokens", "duration": 0.29110099999888916, "failureMessages": [], "location": {"line": 221, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "isTokenBlacklisted"], "fullName": "SessionService isTokenBlacklisted should return false for non-blacklisted tokens", "status": "passed", "title": "should return false for non-blacklisted tokens", "duration": 0.18873600000006263, "failureMessages": [], "location": {"line": 230, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "validateSession"], "fullName": "SessionService validateSession should validate active session and update activity", "status": "passed", "title": "should validate active session and update activity", "duration": 0.30558299999938754, "failureMessages": [], "location": {"line": 239, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "validateSession"], "fullName": "SessionService validateSession should reject blacklisted tokens", "status": "passed", "title": "should reject blacklisted tokens", "duration": 0.20434000000022934, "failureMessages": [], "location": {"line": 250, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "validateSession"], "fullName": "SessionService validateSession should reject non-existent sessions", "status": "passed", "title": "should reject non-existent sessions", "duration": 0.18106099999931757, "failureMessages": [], "location": {"line": 262, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "validateSession"], "fullName": "SessionService validateSession should reject inactive sessions", "status": "passed", "title": "should reject inactive sessions", "duration": 0.19151899999997113, "failureMessages": [], "location": {"line": 269, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "getSessionStats"], "fullName": "SessionService getSessionStats should return correct session statistics", "status": "passed", "title": "should return correct session statistics", "duration": 0.3867020000016055, "failureMessages": [], "location": {"line": 284, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "forceLogoutUser"], "fullName": "SessionService forceLogoutUser should force logout all sessions for a user", "status": "passed", "title": "should force logout all sessions for a user", "duration": 0.39877900000101363, "failureMessages": [], "location": {"line": 304, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "getUserSessionDetails"], "fullName": "SessionService getUserSessionDetails should return detailed session information", "status": "passed", "title": "should return detailed session information", "duration": 0.798903999999311, "failureMessages": [], "location": {"line": 319, "column": 7}, "meta": {}}], "startTime": 1752959413252, "endTime": 1752959413283.7988, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/unit/services/sessionService.test.js"}, {"assertionResults": [{"ancestorTitles": ["Authentication Store (TEST-001)", "Initial State"], "fullName": "Authentication Store (TEST-001) Initial State should initialize with correct defaults", "status": "passed", "title": "should initialize with correct defaults", "duration": 7.059248999999909, "failureMessages": [], "location": {"line": 47, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Getters"], "fullName": "Authentication Store (TEST-001) Getters should return correct isLoggedIn status", "status": "passed", "title": "should return correct isLoggedIn status", "duration": 1.7360069999995176, "failureMessages": [], "location": {"line": 59, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Getters"], "fullName": "Authentication Store (TEST-001) Getters should return current user", "status": "passed", "title": "should return current user", "duration": 1.911627999999837, "failureMessages": [], "location": {"line": 68, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Login Action"], "fullName": "Authentication Store (TEST-001) Login Action should login successfully", "status": "passed", "title": "should login successfully", "duration": 3.5385189999997237, "failureMessages": [], "location": {"line": 77, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Login Action"], "fullName": "Authentication Store (TEST-001) Login Action should handle login failure", "status": "passed", "title": "should handle login failure", "duration": 0.8430330000010144, "failureMessages": [], "location": {"line": 99, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Login Action"], "fullName": "Authentication Store (TEST-001) Login Action should handle login exception", "status": "passed", "title": "should handle login exception", "duration": 0.93251600000076, "failureMessages": [], "location": {"line": 118, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Register Action"], "fullName": "Authentication Store (TEST-001) Register Action should register successfully", "status": "passed", "title": "should register successfully", "duration": 1.2729239999989659, "failureMessages": [], "location": {"line": 134, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Register Action"], "fullName": "Authentication Store (TEST-001) Register Action should handle registration failure", "status": "passed", "title": "should handle registration failure", "duration": 0.6428049999994982, "failureMessages": [], "location": {"line": 155, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Logout Action"], "fullName": "Authentication Store (TEST-001) Logout Action should logout successfully", "status": "passed", "title": "should logout successfully", "duration": 19.05612800000017, "failureMessages": [], "location": {"line": 173, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Token Refresh"], "fullName": "Authentication Store (TEST-001) Token Refresh should refresh token successfully", "status": "passed", "title": "should refresh token successfully", "duration": 9.497303000000102, "failureMessages": [], "location": {"line": 194, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Token Refresh"], "fullName": "Authentication Store (TEST-001) Token Refresh should handle token refresh failure", "status": "passed", "title": "should handle token refresh failure", "duration": 1.457416999999623, "failureMessages": [], "location": {"line": 210, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Password Reset"], "fullName": "Authentication Store (TEST-001) Password Reset should request password reset successfully", "status": "passed", "title": "should request password reset successfully", "duration": 0.716749000001073, "failureMessages": [], "location": {"line": 224, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Password Reset"], "fullName": "Authentication Store (TEST-001) Password Reset should reset password successfully", "status": "passed", "title": "should reset password successfully", "duration": 0.7190710000013496, "failureMessages": [], "location": {"line": 237, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "State Persistence"], "fullName": "Authentication Store (TEST-001) State Persistence should persist auth state to localStorage", "status": "passed", "title": "should persist auth state to localStorage", "duration": 8.56498400000055, "failureMessages": [], "location": {"line": 252, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "State Persistence"], "fullName": "Authentication Store (TEST-001) State Persistence should restore auth state from localStorage", "status": "passed", "title": "should restore auth state from localStorage", "duration": 1.6455239999995683, "failureMessages": [], "location": {"line": 266, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "State Persistence"], "fullName": "Authentication Store (TEST-001) State Persistence should clear auth state", "status": "passed", "title": "should clear auth state", "duration": 1.7253770000006625, "failureMessages": [], "location": {"line": 283, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Initialization"], "fullName": "Authentication Store (TEST-001) Initialization should initialize auth state", "status": "passed", "title": "should initialize auth state", "duration": 0.9377839999997377, "failureMessages": [], "location": {"line": 300, "column": 7}, "meta": {}}], "startTime": 1752959413468, "endTime": 1752959413530.9377, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/auth.spec.js"}, {"assertionResults": [], "startTime": 1752959400365, "endTime": 1752959400365, "status": "failed", "message": "Failed to resolve import \"@/services/databaseService\" from \"ui/stores/__tests__/tasks.spec.js\". Does the file exist?", "name": "/Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js"}]}