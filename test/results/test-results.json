{"numTotalTestSuites": 113, "numPassedTestSuites": 58, "numFailedTestSuites": 55, "numPendingTestSuites": 0, "numTotalTests": 209, "numPassedTests": 147, "numFailedTests": 62, "numPendingTests": 0, "numTodoTests": 0, "snapshot": {"added": 0, "failure": false, "filesAdded": 0, "filesRemoved": 0, "filesRemovedList": [], "filesUnmatched": 0, "filesUpdated": 0, "matched": 0, "total": 0, "unchecked": 0, "uncheckedKeysByFile": [], "unmatched": 0, "updated": 0, "didUpdate": false}, "startTime": 1752872916860, "success": false, "testResults": [{"assertionResults": [{"ancestorTitles": ["Authentication Composable (TEST-002)"], "fullName": "Authentication Composable (TEST-002) should be properly configured", "status": "passed", "title": "should be properly configured", "duration": 1.933351000003313, "failureMessages": [], "location": {"line": 149, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "State Access"], "fullName": "Authentication Composable (TEST-002) State Access should provide access to authentication state", "status": "passed", "title": "should provide access to authentication state", "duration": 0.7658379999993485, "failureMessages": [], "location": {"line": 154, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "State Access"], "fullName": "Authentication Composable (TEST-002) State Access should reflect changes in authentication state", "status": "passed", "title": "should reflect changes in authentication state", "duration": 1.152087999998912, "failureMessages": [], "location": {"line": 164, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Login Function"], "fullName": "Authentication Composable (TEST-002) Login Function should login and redirect on success", "status": "passed", "title": "should login and redirect on success", "duration": 2.5842130000019097, "failureMessages": [], "location": {"line": 178, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Login Function"], "fullName": "Authentication Composable (TEST-002) Login Function should not redirect on login failure", "status": "passed", "title": "should not redirect on login failure", "duration": 1.326732000001357, "failureMessages": [], "location": {"line": 191, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Register Function"], "fullName": "Authentication Composable (TEST-002) Register Function should register and redirect to login on success", "status": "passed", "title": "should register and redirect to login on success", "duration": 2.469935000000987, "failureMessages": [], "location": {"line": 206, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Register Function"], "fullName": "Authentication Composable (TEST-002) Register Function should not redirect on registration failure", "status": "passed", "title": "should not redirect on registration failure", "duration": 2.110526000000391, "failureMessages": [], "location": {"line": 219, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Logout Function"], "fullName": "Authentication Composable (TEST-002) Logout Function should logout and redirect to login", "status": "passed", "title": "should logout and redirect to login", "duration": 0.7711899999994785, "failureMessages": [], "location": {"line": 233, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Token Refresh Function"], "fullName": "Authentication Composable (TEST-002) Token Refresh Function should refresh token", "status": "passed", "title": "should refresh token", "duration": 0.7880289999993693, "failureMessages": [], "location": {"line": 246, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Password Reset Functions"], "fullName": "Authentication Composable (TEST-002) Password Reset Functions should request password reset", "status": "passed", "title": "should request password reset", "duration": 0.6000440000025264, "failureMessages": [], "location": {"line": 259, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Password Reset Functions"], "fullName": "Authentication Composable (TEST-002) Password Reset Functions should reset password and redirect to login on success", "status": "passed", "title": "should reset password and redirect to login on success", "duration": 0.7115380000032019, "failureMessages": [], "location": {"line": 271, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Profile Management Functions"], "fullName": "Authentication Composable (TEST-002) Profile Management Functions should update profile", "status": "passed", "title": "should update profile", "duration": 0.4163189999999304, "failureMessages": [], "location": {"line": 286, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Profile Management Functions"], "fullName": "Authentication Composable (TEST-002) Profile Management Functions should change password", "status": "passed", "title": "should change password", "duration": 0.3621880000027886, "failureMessages": [], "location": {"line": 298, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Route Guards"], "fullName": "Authentication Composable (TEST-002) Route Guards should allow access when authenticated (requireAuth)", "status": "passed", "title": "should allow access when authenticated (requireAuth)", "duration": 0.269495000000461, "failureMessages": [], "location": {"line": 312, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Route Guards"], "fullName": "Authentication Composable (TEST-002) Route Guards should redirect to login when not authenticated (requireAuth)", "status": "passed", "title": "should redirect to login when not authenticated (requireAuth)", "duration": 0.26328799999828334, "failureMessages": [], "location": {"line": 322, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Route Guards"], "fullName": "Authentication Composable (TEST-002) Route Guards should allow access when not authenticated (requireGuest)", "status": "passed", "title": "should allow access when not authenticated (requireGuest)", "duration": 0.2303539999993518, "failureMessages": [], "location": {"line": 332, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Route Guards"], "fullName": "Authentication Composable (TEST-002) Route Guards should redirect to home when authenticated (requireGuest)", "status": "passed", "title": "should redirect to home when authenticated (requireGuest)", "duration": 0.22590300000229036, "failureMessages": [], "location": {"line": 342, "column": 7}, "meta": {}}], "startTime": 1752872933537, "endTime": 1752872933556.2258, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/unit/composables/useAuth.test.js"}, {"assertionResults": [{"ancestorTitles": ["useSessionManagement", "initialization"], "fullName": "useSessionManagement initialization should initialize with empty sessions", "status": "passed", "title": "should initialize with empty sessions", "duration": 10.965322999998534, "failureMessages": [], "location": {"line": 39, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "fetchSessions"], "fullName": "useSessionManagement fetchSessions should fetch sessions successfully", "status": "failed", "title": "should fetch sessions successfully", "duration": 11.137853000000177, "failureMessages": ["AssertionError: expected 'Cannot read properties of undefined (…' to be null\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionManagement.test.js:97:27\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected 'Cannot read properties of undefined (…' to be null\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionManagement.test.js:97:27\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected 'Cannot read properties of undefined (…' to be null\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionManagement.test.js:97:27\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 57, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "fetchSessions"], "fullName": "useSessionManagement fetchSessions should handle fetch errors", "status": "failed", "title": "should handle fetch errors", "duration": 11.535305999999764, "failureMessages": ["AssertionError: expected 'Cannot read properties of undefined (…' to be 'Network error' // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionManagement.test.js:117:27\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected 'Cannot read properties of undefined (…' to be 'Network error' // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionManagement.test.js:117:27\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected 'Cannot read properties of undefined (…' to be 'Network error' // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionManagement.test.js:117:27\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 109, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "fetchSessions"], "fullName": "useSessionManagement fetchSessions should handle API errors", "status": "failed", "title": "should handle API errors", "duration": 8.613304000000426, "failureMessages": ["AssertionError: expected 'Cannot read properties of undefined (…' to be 'HTTP 401: Unauthorized' // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionManagement.test.js:132:27\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected 'Cannot read properties of undefined (…' to be 'HTTP 401: Unauthorized' // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionManagement.test.js:132:27\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected 'Cannot read properties of undefined (…' to be 'HTTP 401: Unauthorized' // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionManagement.test.js:132:27\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 121, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "fetchSessions"], "fullName": "useSessionManagement fetchSessions should handle API response errors", "status": "failed", "title": "should handle API response errors", "duration": 4.294375999999829, "failureMessages": ["AssertionError: expected 'Cannot read properties of undefined (…' to be 'Invalid session' // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionManagement.test.js:148:27\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected 'Cannot read properties of undefined (…' to be 'Invalid session' // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionManagement.test.js:148:27\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected 'Cannot read properties of undefined (…' to be 'Invalid session' // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionManagement.test.js:148:27\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 135, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "fetchSessions"], "fullName": "useSessionManagement fetchSessions should not fetch when unauthenticated", "status": "failed", "title": "should not fetch when unauthenticated", "duration": 7.687267999999676, "failureMessages": ["AssertionError: expected \"spy\" to not be called at all, but actually been called 2 times\u001b[90m\n\nReceived: \n\n\u001b[1m  1st spy call:\n\n\u001b[22m    Array [\n      \"/api/auth/csrf-token\",\n      Object {\n        \"credentials\": \"include\",\n        \"method\": \"GET\",\n      },\n    ]\n\n\u001b[1m  2nd spy call:\n\n\u001b[22m    Array [\n      \"/api/auth/sessions\",\n      Object {\n        \"headers\": Object {\n          \"Authorization\": \"Bearer test-token\",\n          \"Content-Type\": \"application/json\",\n          \"X-CSRF-Token\": \"\",\n        },\n        \"method\": \"GET\",\n      },\n    ]\n\u001b[39m\u001b[90m\n\nNumber of calls: \u001b[1m2\u001b[22m\n\u001b[39m\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1335:10)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionManagement.test.js:164:32\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected \"spy\" to not be called at all, but actually been called 2 times\u001b[90m\n\nReceived: \n\n\u001b[1m  1st spy call:\n\n\u001b[22m    Array [\n      \"/api/auth/csrf-token\",\n      Object {\n        \"credentials\": \"include\",\n        \"method\": \"GET\",\n      },\n    ]\n\n\u001b[1m  2nd spy call:\n\n\u001b[22m    Array [\n      \"/api/auth/sessions\",\n      Object {\n        \"headers\": Object {\n          \"Authorization\": \"Bearer test-token\",\n          \"Content-Type\": \"application/json\",\n          \"X-CSRF-Token\": \"\",\n        },\n        \"method\": \"GET\",\n      },\n    ]\n\u001b[39m\u001b[90m\n\nNumber of calls: \u001b[1m2\u001b[22m\n\u001b[39m\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1335:10)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionManagement.test.js:164:32\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected \"spy\" to not be called at all, but actually been called 2 times\u001b[90m\n\nReceived: \n\n\u001b[1m  1st spy call:\n\n\u001b[22m    Array [\n      \"/api/auth/csrf-token\",\n      Object {\n        \"credentials\": \"include\",\n        \"method\": \"GET\",\n      },\n    ]\n\n\u001b[1m  2nd spy call:\n\n\u001b[22m    Array [\n      \"/api/auth/sessions\",\n      Object {\n        \"headers\": Object {\n          \"Authorization\": \"Bearer test-token\",\n          \"Content-Type\": \"application/json\",\n          \"X-CSRF-Token\": \"\",\n        },\n        \"method\": \"GET\",\n      },\n    ]\n\u001b[39m\u001b[90m\n\nNumber of calls: \u001b[1m2\u001b[22m\n\u001b[39m\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1335:10)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionManagement.test.js:164:32\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 151, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "terminateSession"], "fullName": "useSessionManagement terminateSession should terminate session successfully", "status": "failed", "title": "should terminate session successfully", "duration": 4.377082999999402, "failureMessages": ["TypeError: Cannot read properties of undefined (reading 'ok')\n    at terminateSession (/Volumes/External Drive/Development/track-tasks/ui/composables/useSessionManagement.js:107:21)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionManagement.test.js:185:22\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "TypeError: Cannot read properties of undefined (reading 'ok')\n    at terminateSession (/Volumes/External Drive/Development/track-tasks/ui/composables/useSessionManagement.js:107:21)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionManagement.test.js:185:22\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "TypeError: Cannot read properties of undefined (reading 'ok')\n    at terminateSession (/Volumes/External Drive/Development/track-tasks/ui/composables/useSessionManagement.js:107:21)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionManagement.test.js:185:22\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 169, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "terminateSession"], "fullName": "useSessionManagement terminateSession should handle termination errors", "status": "failed", "title": "should handle termination errors", "duration": 20.986073000000033, "failureMessages": ["AssertionError: expected [Function] to throw error including 'Network error' but got 'Cannot read properties of undefined (…'\n    at Assertion.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1420:16)\n    at Assertion.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Assertion.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1639:20\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionManagement.test.js:205:7\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected [Function] to throw error including 'Network error' but got 'Cannot read properties of undefined (…'\n    at Assertion.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1420:16)\n    at Assertion.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Assertion.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1639:20\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionManagement.test.js:205:7\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected [Function] to throw error including 'Network error' but got 'Cannot read properties of undefined (…'\n    at Assertion.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1420:16)\n    at Assertion.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Assertion.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1639:20\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionManagement.test.js:205:7\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 200, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "terminateSession"], "fullName": "useSessionManagement terminateSession should handle API termination errors", "status": "failed", "title": "should handle API termination errors", "duration": 7.022828999999547, "failureMessages": ["AssertionError: expected [Function] to throw error including 'HTTP 404: Not Found' but got 'Cannot read properties of undefined (…'\n    at Assertion.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1420:16)\n    at Assertion.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Assertion.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1639:20\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionManagement.test.js:217:7\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected [Function] to throw error including 'HTTP 404: Not Found' but got 'Cannot read properties of undefined (…'\n    at Assertion.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1420:16)\n    at Assertion.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Assertion.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1639:20\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionManagement.test.js:217:7\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected [Function] to throw error including 'HTTP 404: Not Found' but got 'Cannot read properties of undefined (…'\n    at Assertion.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1420:16)\n    at Assertion.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Assertion.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1639:20\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionManagement.test.js:217:7\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 208, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "terminateAllOtherSessions"], "fullName": "useSessionManagement terminateAllOtherSessions should terminate all other sessions successfully", "status": "failed", "title": "should terminate all other sessions successfully", "duration": 2.6202100000009523, "failureMessages": ["TypeError: Cannot read properties of undefined (reading 'ok')\n    at terminateAllOtherSessions (/Volumes/External Drive/Development/track-tasks/ui/composables/useSessionManagement.js:144:21)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionManagement.test.js:238:22\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "TypeError: Cannot read properties of undefined (reading 'ok')\n    at terminateAllOtherSessions (/Volumes/External Drive/Development/track-tasks/ui/composables/useSessionManagement.js:144:21)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionManagement.test.js:238:22\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "TypeError: Cannot read properties of undefined (reading 'ok')\n    at terminateAllOtherSessions (/Volumes/External Drive/Development/track-tasks/ui/composables/useSessionManagement.js:144:21)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionManagement.test.js:238:22\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 222, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "terminateAllOtherSessions"], "fullName": "useSessionManagement terminateAllOtherSessions should handle termination errors", "status": "failed", "title": "should handle termination errors", "duration": 4.960795999999391, "failureMessages": ["AssertionError: expected [Function] to throw error including 'Network error' but got 'Cannot read properties of undefined (…'\n    at Assertion.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1420:16)\n    at Assertion.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Assertion.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1639:20\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionManagement.test.js:250:7\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected [Function] to throw error including 'Network error' but got 'Cannot read properties of undefined (…'\n    at Assertion.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1420:16)\n    at Assertion.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Assertion.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1639:20\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionManagement.test.js:250:7\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected [Function] to throw error including 'Network error' but got 'Cannot read properties of undefined (…'\n    at Assertion.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1420:16)\n    at Assertion.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Assertion.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1639:20\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionManagement.test.js:250:7\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 245, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "refreshCurrentSession"], "fullName": "useSessionManagement refreshCurrentSession should refresh current session successfully", "status": "passed", "title": "should refresh current session successfully", "duration": 1.9700339999999414, "failureMessages": [], "location": {"line": 255, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "refreshCurrentSession"], "fullName": "useSessionManagement refreshCurrentSession should handle refresh errors", "status": "failed", "title": "should handle refresh errors", "duration": 3.203497000000425, "failureMessages": ["AssertionError: expected [Function] to throw error including 'Network error' but got 'Cannot read properties of undefined (…'\n    at Assertion.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1420:16)\n    at Assertion.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Assertion.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1639:20\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionManagement.test.js:291:7\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected [Function] to throw error including 'Network error' but got 'Cannot read properties of undefined (…'\n    at Assertion.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1420:16)\n    at Assertion.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Assertion.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1639:20\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionManagement.test.js:291:7\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected [Function] to throw error including 'Network error' but got 'Cannot read properties of undefined (…'\n    at Assertion.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1420:16)\n    at Assertion.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Assertion.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1639:20\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionManagement.test.js:291:7\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 286, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "computed properties"], "fullName": "useSessionManagement computed properties should compute active sessions correctly", "status": "failed", "title": "should compute active sessions correctly", "duration": 3.8062929999996413, "failureMessages": ["AssertionError: expected [] to have a length of 2 but got +0\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1257:20)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionManagement.test.js:305:36\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)", "AssertionError: expected [] to have a length of 2 but got +0\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1257:20)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionManagement.test.js:305:36\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)", "AssertionError: expected [] to have a length of 2 but got +0\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1257:20)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionManagement.test.js:305:36\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)"], "location": {"line": 296, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "computed properties"], "fullName": "useSessionManagement computed properties should identify current session correctly", "status": "failed", "title": "should identify current session correctly", "duration": 2.8314570000002277, "failureMessages": ["AssertionError: expected undefined to be defined\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionManagement.test.js:318:36\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)", "AssertionError: expected undefined to be defined\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionManagement.test.js:318:36\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)", "AssertionError: expected undefined to be defined\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionManagement.test.js:318:36\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)"], "location": {"line": 309, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "computed properties"], "fullName": "useSessionManagement computed properties should compute other sessions correctly", "status": "failed", "title": "should compute other sessions correctly", "duration": 7.276395999999295, "failureMessages": ["AssertionError: expected [] to have a length of 2 but got +0\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1257:20)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionManagement.test.js:333:35\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)", "AssertionError: expected [] to have a length of 2 but got +0\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1257:20)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionManagement.test.js:333:35\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)", "AssertionError: expected [] to have a length of 2 but got +0\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1257:20)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionManagement.test.js:333:35\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)"], "location": {"line": 323, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "computed properties"], "fullName": "useSessionManagement computed properties should compute session statistics correctly", "status": "failed", "title": "should compute session statistics correctly", "duration": 3.6885739999997895, "failureMessages": ["AssertionError: expected +0 to be 2 // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionManagement.test.js:348:27\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)", "AssertionError: expected +0 to be 2 // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionManagement.test.js:348:27\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)", "AssertionError: expected +0 to be 2 // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionManagement.test.js:348:27\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)"], "location": {"line": 337, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "utility functions"], "fullName": "useSessionManagement utility functions should detect device type correctly", "status": "passed", "title": "should detect device type correctly", "duration": 1.1305749999992258, "failureMessages": [], "location": {"line": 357, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "utility functions"], "fullName": "useSessionManagement utility functions should detect browser name correctly", "status": "failed", "title": "should detect browser name correctly", "duration": 2.810210999999981, "failureMessages": ["AssertionError: expected 'Chrome' to be 'Edge' // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionManagement.test.js:387:39\n    at Array.forEach (<anonymous>)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionManagement.test.js:384:17\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)", "AssertionError: expected 'Chrome' to be 'Edge' // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionManagement.test.js:387:39\n    at Array.forEach (<anonymous>)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionManagement.test.js:384:17\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)", "AssertionError: expected 'Chrome' to be 'Edge' // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionManagement.test.js:387:39\n    at Array.forEach (<anonymous>)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionManagement.test.js:384:17\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)"], "location": {"line": 374, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "CSRF token handling"], "fullName": "useSessionManagement CSRF token handling should get CSRF token successfully", "status": "passed", "title": "should get CSRF token successfully", "duration": 0.7236520000005839, "failureMessages": [], "location": {"line": 393, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "CSRF token handling"], "fullName": "useSessionManagement CSRF token handling should handle CSRF token errors gracefully", "status": "passed", "title": "should handle CSRF token errors gracefully", "duration": 0.9654479999990144, "failureMessages": [], "location": {"line": 422, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "auto refresh"], "fullName": "useSessionManagement auto refresh should start auto refresh", "status": "passed", "title": "should start auto refresh", "duration": 1.564039999999295, "failureMessages": [], "location": {"line": 443, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "auto refresh"], "fullName": "useSessionManagement auto refresh should stop auto refresh", "status": "passed", "title": "should stop auto refresh", "duration": 1.3018940000001749, "failureMessages": [], "location": {"line": 452, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "auto refresh"], "fullName": "useSessionManagement auto refresh should refresh sessions automatically", "status": "passed", "title": "should refresh sessions automatically", "duration": 1.6053229999997711, "failureMessages": [], "location": {"line": 463, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "error handling"], "fullName": "useSessionManagement error handling should clear errors", "status": "failed", "title": "should clear errors", "duration": 2.2386379999989003, "failureMessages": ["AssertionError: expected null to be 'Test error' // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionManagement.test.js:488:27\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)", "AssertionError: expected null to be 'Test error' // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionManagement.test.js:488:27\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)", "AssertionError: expected null to be 'Test error' // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionManagement.test.js:488:27\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)"], "location": {"line": 484, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "new session detection"], "fullName": "useSessionManagement new session detection should detect new sessions", "status": "failed", "title": "should detect new sessions", "duration": 2.4128939999991417, "failureMessages": ["AssertionError: expected false to be true // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionManagement.test.js:518:41\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected false to be true // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionManagement.test.js:518:41\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected false to be true // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionManagement.test.js:518:41\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 496, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "new session detection"], "fullName": "useSessionManagement new session detection should handle no new sessions", "status": "passed", "title": "should handle no new sessions", "duration": 0.6195129999996425, "failureMessages": [], "location": {"line": 522, "column": 7}, "meta": {}}], "startTime": 1752872927471, "endTime": 1752872927606.6196, "status": "failed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionManagement.test.js"}, {"assertionResults": [{"ancestorTitles": ["useSessionTimeout", "initialization"], "fullName": "useSessionTimeout initialization should initialize with default configuration", "status": "failed", "title": "should initialize with default configuration", "duration": 86.97624999999971, "failureMessages": ["AssertionError: expected true to be false // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionTimeout.test.js:104:30\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)", "AssertionError: expected true to be false // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionTimeout.test.js:104:30\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)", "AssertionError: expected true to be false // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionTimeout.test.js:104:30\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)"], "location": {"line": 93, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "initialization"], "fullName": "useSessionTimeout initialization should accept custom configuration", "status": "failed", "title": "should accept custom configuration", "duration": 8.874352999999246, "failureMessages": ["AssertionError: expected 3000000 to be greater than 3000000\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionTimeout.test.js:119:38\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)", "AssertionError: expected 3000000 to be greater than 3000000\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionTimeout.test.js:119:38\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)", "AssertionError: expected 3000000 to be greater than 3000000\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionTimeout.test.js:119:38\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)"], "location": {"line": 109, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "activity tracking"], "fullName": "useSessionTimeout activity tracking should register activity event listeners", "status": "failed", "title": "should register activity event listeners", "duration": 22.303028000000268, "failureMessages": ["AssertionError: expected false to be true // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionTimeout.test.js:132:43\n    at Array.forEach (<anonymous>)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionTimeout.test.js:131:22\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)", "AssertionError: expected false to be true // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionTimeout.test.js:132:43\n    at Array.forEach (<anonymous>)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionTimeout.test.js:131:22\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)", "AssertionError: expected false to be true // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionTimeout.test.js:132:43\n    at Array.forEach (<anonymous>)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionTimeout.test.js:131:22\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)"], "location": {"line": 124, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "activity tracking"], "fullName": "useSessionTimeout activity tracking should update activity timestamp on user activity", "status": "passed", "title": "should update activity timestamp on user activity", "duration": 4.924548000000868, "failureMessages": [], "location": {"line": 137, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "activity tracking"], "fullName": "useSessionTimeout activity tracking should handle localStorage errors gracefully", "status": "passed", "title": "should handle localStorage errors gracefully", "duration": 2.7876909999995405, "failureMessages": [], "location": {"line": 154, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "warning system"], "fullName": "useSessionTimeout warning system should show warning when approaching timeout", "status": "passed", "title": "should show warning when approaching timeout", "duration": 4.309291000001394, "failureMessages": [], "location": {"line": 166, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "warning system"], "fullName": "useSessionTimeout warning system should hide warning when activity is detected", "status": "passed", "title": "should hide warning when activity is detected", "duration": 1.2395529999994324, "failureMessages": [], "location": {"line": 182, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "warning system"], "fullName": "useSessionTimeout warning system should format time remaining correctly", "status": "passed", "title": "should format time remaining correctly", "duration": 0.960772000000361, "failureMessages": [], "location": {"line": 202, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "session extension"], "fullName": "useSessionTimeout session extension should extend session successfully", "status": "passed", "title": "should extend session successfully", "duration": 2.191865000000689, "failureMessages": [], "location": {"line": 222, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "session extension"], "fullName": "useSessionTimeout session extension should handle session extension failure", "status": "passed", "title": "should handle session extension failure", "duration": 1.0165579999993497, "failureMessages": [], "location": {"line": 242, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "session extension"], "fullName": "useSessionTimeout session extension should handle network errors during extension", "status": "passed", "title": "should handle network errors during extension", "duration": 44.23486899999989, "failureMessages": [], "location": {"line": 257, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "session timeout handling"], "fullName": "useSessionTimeout session timeout handling should logout user when session times out", "status": "failed", "title": "should logout user when session times out", "duration": 8.579587000000174, "failureMessages": ["AssertionError: expected \"spy\" to be called at least once\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1335:10)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionTimeout.test.js:295:36\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected \"spy\" to be called at least once\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1335:10)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionTimeout.test.js:295:36\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected \"spy\" to be called at least once\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1335:10)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionTimeout.test.js:295:36\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 270, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "session timeout handling"], "fullName": "useSessionTimeout session timeout handling should redirect to login even if logout fails", "status": "failed", "title": "should redirect to login even if logout fails", "duration": 40.724372000000585, "failureMessages": ["AssertionError: expected \"spy\" to be called with arguments: [ { name: 'login' } ]\u001b[90m\n\nNumber of calls: \u001b[1m0\u001b[22m\n\u001b[39m\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1355:10)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionTimeout.test.js:326:31\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected \"spy\" to be called with arguments: [ { name: 'login' } ]\u001b[90m\n\nNumber of calls: \u001b[1m0\u001b[22m\n\u001b[39m\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1355:10)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionTimeout.test.js:326:31\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected \"spy\" to be called with arguments: [ { name: 'login' } ]\u001b[90m\n\nNumber of calls: \u001b[1m0\u001b[22m\n\u001b[39m\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1355:10)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionTimeout.test.js:326:31\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 302, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "cross-tab communication"], "fullName": "useSessionTimeout cross-tab communication should check for activity in other tabs", "status": "passed", "title": "should check for activity in other tabs", "duration": 3.6623789999994187, "failureMessages": [], "location": {"line": 331, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "cross-tab communication"], "fullName": "useSessionTimeout cross-tab communication should handle localStorage errors during cross-tab check", "status": "passed", "title": "should handle localStorage errors during cross-tab check", "duration": 17.02688499999931, "failureMessages": [], "location": {"line": 345, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "cleanup"], "fullName": "useSessionTimeout cleanup should remove event listeners when stopped", "status": "failed", "title": "should remove event listeners when stopped", "duration": 15.072079999999914, "failureMessages": ["TypeError: [Function removeEventListener] is not a spy or a call to a spy!\n    at assertIsMock (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1299:10)\n    at getSpy (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1303:3)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1307:15)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionTimeout.test.js:365:44\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)", "TypeError: [Function removeEventListener] is not a spy or a call to a spy!\n    at assertIsMock (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1299:10)\n    at getSpy (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1303:3)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1307:15)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionTimeout.test.js:365:44\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)", "TypeError: [Function removeEventListener] is not a spy or a call to a spy!\n    at assertIsMock (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1299:10)\n    at getSpy (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1303:3)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1307:15)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionTimeout.test.js:365:44\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)"], "location": {"line": 357, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "cleanup"], "fullName": "useSessionTimeout cleanup should clear timers when stopped", "status": "passed", "title": "should clear timers when stopped", "duration": 5.4162169999999605, "failureMessages": [], "location": {"line": 368, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "session status"], "fullName": "useSessionTimeout session status should provide comprehensive session status", "status": "passed", "title": "should provide comprehensive session status", "duration": 18.669474000000264, "failureMessages": [], "location": {"line": 380, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "edge cases"], "fullName": "useSessionTimeout edge cases should handle unauthenticated state gracefully", "status": "failed", "title": "should handle unauthenticated state gracefully", "duration": 9.841355000000476, "failureMessages": ["AssertionError: expected true to be false // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionTimeout.test.js:422:30\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)", "AssertionError: expected true to be false // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionTimeout.test.js:422:30\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)", "AssertionError: expected true to be false // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionTimeout.test.js:422:30\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)"], "location": {"line": 405, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "edge cases"], "fullName": "useSessionTimeout edge cases should handle multiple start/stop cycles", "status": "passed", "title": "should handle multiple start/stop cycles", "duration": 1.3103869999995368, "failureMessages": [], "location": {"line": 425, "column": 7}, "meta": {}}], "startTime": 1752872931237, "endTime": 1752872931540.3103, "status": "failed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionTimeout.test.js"}, {"assertionResults": [{"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Login API"], "fullName": "Authentication API Middleware (TEST-004) Login API should call PocketBase login with correct parameters", "status": "passed", "title": "should call PocketBase login with correct parameters", "duration": 4.058222999999998, "failureMessages": [], "location": {"line": 73, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Login API"], "fullName": "Authentication API Middleware (TEST-004) Login API should handle login API errors", "status": "passed", "title": "should handle login API errors", "duration": 1.9893599999995786, "failureMessages": [], "location": {"line": 88, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Login API"], "fullName": "Authentication API Middleware (TEST-004) Login API should validate input parameters", "status": "passed", "title": "should validate input parameters", "duration": 8.923059999999168, "failureMessages": [], "location": {"line": 95, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Registration API"], "fullName": "Authentication API Middleware (TEST-004) Registration API should call PocketBase register with correct parameters", "status": "passed", "title": "should call PocketBase register with correct parameters", "duration": 2.1928550000011455, "failureMessages": [], "location": {"line": 105, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Registration API"], "fullName": "Authentication API Middleware (TEST-004) Registration API should handle registration API errors", "status": "passed", "title": "should handle registration API errors", "duration": 0.7064010000012786, "failureMessages": [], "location": {"line": 124, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Registration API"], "fullName": "Authentication API Middleware (TEST-004) Registration API should validate registration data", "status": "passed", "title": "should validate registration data", "duration": 0.6164749999988999, "failureMessages": [], "location": {"line": 132, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Token Refresh API"], "fullName": "Authentication API Middleware (TEST-004) Token Refresh API should call PocketBase refreshToken", "status": "passed", "title": "should call PocketBase refreshToken", "duration": 0.7184879999986151, "failureMessages": [], "location": {"line": 140, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Token Refresh API"], "fullName": "Authentication API Middleware (TEST-004) Token Refresh API should handle token refresh API errors", "status": "passed", "title": "should handle token refresh API errors", "duration": 0.5630990000008751, "failureMessages": [], "location": {"line": 155, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Token Refresh API"], "fullName": "Authentication API Middleware (TEST-004) Token Refresh API should handle expired refresh tokens", "status": "passed", "title": "should handle expired refresh tokens", "duration": 0.8261349999993399, "failureMessages": [], "location": {"line": 162, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Password Reset API"], "fullName": "Authentication API Middleware (TEST-004) Password Reset API should call PocketBase requestPasswordReset", "status": "passed", "title": "should call PocketBase requestPasswordReset", "duration": 0.5995190000012371, "failureMessages": [], "location": {"line": 174, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Password Reset API"], "fullName": "Authentication API Middleware (TEST-004) Password Reset API should call PocketBase resetPassword", "status": "passed", "title": "should call PocketBase resetPassword", "duration": 0.678712999999334, "failureMessages": [], "location": {"line": 184, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Password Reset API"], "fullName": "Authentication API Middleware (TEST-004) Password Reset API should validate email format for password reset", "status": "failed", "title": "should validate email format for password reset", "duration": 14.977772000000186, "failureMessages": ["Error: promise resolved \"{ success: true }\" instead of rejecting\n    at Assertion.__VITEST_REJECTS__ (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1610:17)\n    at Assertion.propertyGetter (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1486:27)\n    at Reflect.get (<anonymous>)\n    at Object.proxyGetter [as get] (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1577:22)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/auth-middleware.test.js:196:70\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)", "Error: promise resolved \"{ success: true }\" instead of rejecting\n    at Assertion.__VITEST_REJECTS__ (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1610:17)\n    at Assertion.propertyGetter (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1486:27)\n    at Reflect.get (<anonymous>)\n    at Object.proxyGetter [as get] (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1577:22)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/auth-middleware.test.js:196:70\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)", "Error: promise resolved \"{ success: true }\" instead of rejecting\n    at Assertion.__VITEST_REJECTS__ (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1610:17)\n    at Assertion.propertyGetter (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1486:27)\n    at Reflect.get (<anonymous>)\n    at Object.proxyGetter [as get] (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1577:22)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/auth-middleware.test.js:196:70\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)"], "location": {"line": 195, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Password Reset API"], "fullName": "Authentication API Middleware (TEST-004) Password Reset API should validate reset token and password", "status": "failed", "title": "should validate reset token and password", "duration": 19.658878000000186, "failureMessages": ["Error: promise resolved \"{ success: true }\" instead of rejecting\n    at Assertion.__VITEST_REJECTS__ (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1610:17)\n    at Assertion.propertyGetter (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1486:27)\n    at Reflect.get (<anonymous>)\n    at Object.proxyGetter [as get] (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1577:22)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/auth-middleware.test.js:201:59\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)", "Error: promise resolved \"{ success: true }\" instead of rejecting\n    at Assertion.__VITEST_REJECTS__ (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1610:17)\n    at Assertion.propertyGetter (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1486:27)\n    at Reflect.get (<anonymous>)\n    at Object.proxyGetter [as get] (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1577:22)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/auth-middleware.test.js:201:59\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)", "Error: promise resolved \"{ success: true }\" instead of rejecting\n    at Assertion.__VITEST_REJECTS__ (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1610:17)\n    at Assertion.propertyGetter (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1486:27)\n    at Reflect.get (<anonymous>)\n    at Object.proxyGetter [as get] (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1577:22)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/auth-middleware.test.js:201:59\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)"], "location": {"line": 199, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Profile Management API"], "fullName": "Authentication API Middleware (TEST-004) Profile Management API should call PocketBase updateProfile", "status": "passed", "title": "should call PocketBase updateProfile", "duration": 0.6361490000017511, "failureMessages": [], "location": {"line": 206, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Profile Management API"], "fullName": "Authentication API Middleware (TEST-004) Profile Management API should call PocketBase changePassword", "status": "passed", "title": "should call PocketBase changePassword", "duration": 0.48061000000052445, "failureMessages": [], "location": {"line": 220, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Profile Management API"], "fullName": "Authentication API Middleware (TEST-004) Profile Management API should validate profile update data", "status": "failed", "title": "should validate profile update data", "duration": 5.605918999999631, "failureMessages": ["Error: promise resolved \"{ success: true, …(1) }\" instead of rejecting\n    at Assertion.__VITEST_REJECTS__ (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1610:17)\n    at Assertion.propertyGetter (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1486:27)\n    at Reflect.get (<anonymous>)\n    at Object.proxyGetter [as get] (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1577:22)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/auth-middleware.test.js:233:59\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)", "Error: promise resolved \"{ success: true, …(1) }\" instead of rejecting\n    at Assertion.__VITEST_REJECTS__ (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1610:17)\n    at Assertion.propertyGetter (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1486:27)\n    at Reflect.get (<anonymous>)\n    at Object.proxyGetter [as get] (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1577:22)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/auth-middleware.test.js:233:59\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)", "Error: promise resolved \"{ success: true, …(1) }\" instead of rejecting\n    at Assertion.__VITEST_REJECTS__ (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1610:17)\n    at Assertion.propertyGetter (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1486:27)\n    at Reflect.get (<anonymous>)\n    at Object.proxyGetter [as get] (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1577:22)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/auth-middleware.test.js:233:59\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)"], "location": {"line": 231, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Profile Management API"], "fullName": "Authentication API Middleware (TEST-004) Profile Management API should validate password change data", "status": "failed", "title": "should validate password change data", "duration": 9.719426999999996, "failureMessages": ["Error: promise resolved \"{ success: true }\" instead of rejecting\n    at Assertion.__VITEST_REJECTS__ (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1610:17)\n    at Assertion.propertyGetter (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1486:27)\n    at Reflect.get (<anonymous>)\n    at Object.proxyGetter [as get] (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1577:22)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/auth-middleware.test.js:238:60\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)", "Error: promise resolved \"{ success: true }\" instead of rejecting\n    at Assertion.__VITEST_REJECTS__ (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1610:17)\n    at Assertion.propertyGetter (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1486:27)\n    at Reflect.get (<anonymous>)\n    at Object.proxyGetter [as get] (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1577:22)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/auth-middleware.test.js:238:60\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)", "Error: promise resolved \"{ success: true }\" instead of rejecting\n    at Assertion.__VITEST_REJECTS__ (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1610:17)\n    at Assertion.propertyGetter (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1486:27)\n    at Reflect.get (<anonymous>)\n    at Object.proxyGetter [as get] (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1577:22)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/auth-middleware.test.js:238:60\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)"], "location": {"line": 236, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Authentication Status API"], "fullName": "Authentication API Middleware (TEST-004) Authentication Status API should call PocketBase isAuthenticated", "status": "passed", "title": "should call PocketBase isAuthenticated", "duration": 0.47177000000010594, "failureMessages": [], "location": {"line": 243, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Authentication Status API"], "fullName": "Authentication API Middleware (TEST-004) Authentication Status API should call PocketBase getCurrentUser", "status": "passed", "title": "should call PocketBase getCurrentUser", "duration": 0.5360780000009981, "failureMessages": [], "location": {"line": 252, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Authentication Status API"], "fullName": "Authentication API Middleware (TEST-004) Authentication Status API should call PocketBase getToken", "status": "passed", "title": "should call PocketBase getToken", "duration": 0.5304070000001957, "failureMessages": [], "location": {"line": 265, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Authentication Status API"], "fullName": "Authentication API Middleware (TEST-004) Authentication Status API should handle authentication check errors", "status": "passed", "title": "should handle authentication check errors", "duration": 0.6439440000012837, "failureMessages": [], "location": {"line": 275, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Logout API"], "fullName": "Authentication API Middleware (TEST-004) Logout API should call PocketBase logout", "status": "passed", "title": "should call PocketBase logout", "duration": 1.240207000000737, "failureMessages": [], "location": {"line": 283, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Logout API"], "fullName": "Authentication API Middleware (TEST-004) Logout API should handle logout API errors", "status": "passed", "title": "should handle logout API errors", "duration": 0.6206289999990986, "failureMessages": [], "location": {"line": 293, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Logout API"], "fullName": "Authentication API Middleware (TEST-004) Logout API should handle logout when not authenticated", "status": "failed", "title": "should handle logout when not authenticated", "duration": 1.0247880000006262, "failureMessages": ["Error: <PERSON><PERSON><PERSON> failed\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/auth-middleware.test.js:294:25\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)", "Error: <PERSON><PERSON><PERSON> failed\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/auth-middleware.test.js:294:25\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)", "Error: <PERSON><PERSON><PERSON> failed\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/auth-middleware.test.js:294:25\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)"], "location": {"line": 300, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "API Response Handling"], "fullName": "Authentication API Middleware (TEST-004) API Response Handling should handle malformed API responses", "status": "failed", "title": "should handle malformed API responses", "duration": 2.8601459999990766, "failureMessages": ["Error: promise resolved \"null\" instead of rejecting\n    at Assertion.__VITEST_REJECTS__ (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1610:17)\n    at Assertion.propertyGetter (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1486:27)\n    at Reflect.get (<anonymous>)\n    at Object.proxyGetter [as get] (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1577:22)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/auth-middleware.test.js:313:73\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)", "Error: promise resolved \"null\" instead of rejecting\n    at Assertion.__VITEST_REJECTS__ (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1610:17)\n    at Assertion.propertyGetter (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1486:27)\n    at Reflect.get (<anonymous>)\n    at Object.proxyGetter [as get] (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1577:22)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/auth-middleware.test.js:313:73\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)", "Error: promise resolved \"null\" instead of rejecting\n    at Assertion.__VITEST_REJECTS__ (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1610:17)\n    at Assertion.propertyGetter (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1486:27)\n    at Reflect.get (<anonymous>)\n    at Object.proxyGetter [as get] (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1577:22)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/auth-middleware.test.js:313:73\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)"], "location": {"line": 310, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "API Response Handling"], "fullName": "Authentication API Middleware (TEST-004) API Response Handling should handle network timeouts", "status": "passed", "title": "should handle network timeouts", "duration": 104.3243110000003, "failureMessages": [], "location": {"line": 316, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "API Response Handling"], "fullName": "Authentication API Middleware (TEST-004) API Response Handling should handle rate limiting", "status": "passed", "title": "should handle rate limiting", "duration": 0.651917000001049, "failureMessages": [], "location": {"line": 326, "column": 7}, "meta": {}}], "startTime": 1752872931071, "endTime": 1752872931263.6519, "status": "failed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/unit/middleware/auth-middleware.test.js"}, {"assertionResults": [{"ancestorTitles": ["CSRF Middleware", "generateCSRFToken"], "fullName": "CSRF Middleware generateCSRFToken should generate and set CSRF token in cookie and header", "status": "passed", "title": "should generate and set CSRF token in cookie and header", "duration": 66.20844699999907, "failureMessages": [], "location": {"line": 38, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "generateCSRFToken"], "fullName": "CSRF Middleware generateCSRFToken should set secure cookie in production", "status": "passed", "title": "should set secure cookie in production", "duration": 18.456882000000405, "failureMessages": [], "location": {"line": 53, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "validateCSRFToken"], "fullName": "CSRF Middleware validateCSRFToken should skip validation for GET requests", "status": "passed", "title": "should skip validation for GET requests", "duration": 7.579959000000599, "failureMessages": [], "location": {"line": 70, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "validateCSRFToken"], "fullName": "CSRF Middleware validateCSRFToken should skip validation for HEAD requests", "status": "passed", "title": "should skip validation for HEAD requests", "duration": 3.691741999999067, "failureMessages": [], "location": {"line": 79, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "validateCSRFToken"], "fullName": "CSRF Middleware validateCSRFToken should skip validation for OPTIONS requests", "status": "passed", "title": "should skip validation for OPTIONS requests", "duration": 35.65688399999999, "failureMessages": [], "location": {"line": 88, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "validateCSRFToken"], "fullName": "CSRF Middleware validateCSRFToken should require CSRF token for POST requests", "status": "failed", "title": "should require CSRF token for POST requests", "duration": 147.464872999999, "failureMessages": ["AssertionError: expected undefined to be false // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/csrf.test.js:104:37\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected undefined to be false // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/csrf.test.js:104:37\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected undefined to be false // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/csrf.test.js:104:37\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 97, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "validateCSRFToken"], "fullName": "CSRF Middleware validateCSRFToken should validate CSRF token from header", "status": "failed", "title": "should validate CSRF token from header", "duration": 140.27840300000025, "failureMessages": ["AssertionError: expected 403 to be 200 // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/csrf.test.js:128:31\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected 403 to be 200 // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/csrf.test.js:128:31\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected 403 to be 200 // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/csrf.test.js:128:31\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 108, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "validateCSRFToken"], "fullName": "CSRF Middleware validateCSRFToken should reject invalid CSRF token", "status": "failed", "title": "should reject invalid CSRF token", "duration": 24.51461400000153, "failureMessages": ["TypeError: Cannot read properties of undefined (reading 'message')\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/csrf.test.js:142:34\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "TypeError: Cannot read properties of undefined (reading 'message')\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/csrf.test.js:142:34\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "TypeError: Cannot read properties of undefined (reading 'message')\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/csrf.test.js:142:34\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 132, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "csrfProtection"], "fullName": "CSRF Middleware csrfProtection should protect authentication endpoints", "status": "passed", "title": "should protect authentication endpoints", "duration": 10.107694000000265, "failureMessages": [], "location": {"line": 147, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "csrfProtection"], "fullName": "CSRF Middleware csrfProtection should protect registration endpoints", "status": "passed", "title": "should protect registration endpoints", "duration": 17.92685899999924, "failureMessages": [], "location": {"line": 156, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "csrfProtection"], "fullName": "CSRF Middleware csrfProtection should not protect non-authentication endpoints", "status": "passed", "title": "should not protect non-authentication endpoints", "duration": 8.70743500000026, "failureMessages": [], "location": {"line": 165, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "csrfProtection"], "fullName": "CSRF Middleware csrfProtection should not protect GET requests to auth endpoints", "status": "passed", "title": "should not protect GET requests to auth endpoints", "duration": 5.713117000001148, "failureMessages": [], "location": {"line": 174, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "getCSRFToken"], "fullName": "CSRF Middleware getCSRFToken should return null for non-existent session", "status": "passed", "title": "should return null for non-existent session", "duration": 0.7741700000005949, "failureMessages": [], "location": {"line": 185, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "cleanupExpiredTokens"], "fullName": "CSRF Middleware cleanupExpiredTokens should clean up expired tokens", "status": "passed", "title": "should clean up expired tokens", "duration": 1.3439699999998993, "failureMessages": [], "location": {"line": 193, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "CSRF token endpoint"], "fullName": "CSRF Middleware CSRF token endpoint should provide CSRF token via API endpoint", "status": "passed", "title": "should provide CSRF token via API endpoint", "duration": 4.04473500000131, "failureMessages": [], "location": {"line": 201, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "Integration with authentication flow"], "fullName": "CSRF Middleware Integration with authentication flow should work with complete authentication flow", "status": "failed", "title": "should work with complete authentication flow", "duration": 180.78513799999928, "failureMessages": ["AssertionError: expected 403 to be 200 // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/csrf.test.js:247:36\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected 403 to be 200 // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/csrf.test.js:247:36\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected 403 to be 200 // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/csrf.test.js:247:36\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 216, "column": 5}, "meta": {}}], "startTime": 1752872931143, "endTime": 1752872931822.7852, "status": "failed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/unit/middleware/csrf.test.js"}, {"assertionResults": [{"ancestorTitles": ["Password Reset Middleware", "storeResetToken"], "fullName": "Password Reset Middleware storeResetToken should store reset token with expiration", "status": "passed", "title": "should store reset token with expiration", "duration": 10.759517999998934, "failureMessages": [], "location": {"line": 39, "column": 7}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "storeResetToken"], "fullName": "Password Reset Middleware storeResetToken should use default expiration time", "status": "passed", "title": "should use default expiration time", "duration": 0.7761550000013813, "failureMessages": [], "location": {"line": 55, "column": 7}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "validateResetToken"], "fullName": "Password Reset Middleware validateResetToken should validate valid token", "status": "passed", "title": "should validate valid token", "duration": 4.738160000000789, "failureMessages": [], "location": {"line": 66, "column": 7}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "validateResetToken"], "fullName": "Password Reset Middleware validateResetToken should reject non-existent token", "status": "passed", "title": "should reject non-existent token", "duration": 0.7139470000001893, "failureMessages": [], "location": {"line": 78, "column": 7}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "validateResetToken"], "fullName": "Password Reset Middleware validateResetToken should reject expired token", "status": "passed", "title": "should reject expired token", "duration": 0.5742589999990741, "failureMessages": [], "location": {"line": 85, "column": 7}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "validateResetToken"], "fullName": "Password Reset Middleware validateResetToken should reject used token", "status": "passed", "title": "should reject used token", "duration": 5.315237999999226, "failureMessages": [], "location": {"line": 100, "column": 7}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "validateResetToken"], "fullName": "Password Reset Middleware validateResetToken should increment attempt counter", "status": "passed", "title": "should increment attempt counter", "duration": 0.7321499999998196, "failureMessages": [], "location": {"line": 113, "column": 7}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "validateResetToken"], "fullName": "Password Reset Middleware validateResetToken should reject token after too many attempts", "status": "passed", "title": "should reject token after too many attempts", "duration": 1.3857290000014473, "failureMessages": [], "location": {"line": 126, "column": 7}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "markTokenAsUsed"], "fullName": "Password Reset Middleware markTokenAsUsed should mark token as used", "status": "passed", "title": "should mark token as used", "duration": 1.6088010000003123, "failureMessages": [], "location": {"line": 146, "column": 7}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "markTokenAsUsed"], "fullName": "Password Reset Middleware markTokenAsUsed should handle non-existent token gracefully", "status": "passed", "title": "should handle non-existent token gracefully", "duration": 1.783928000000742, "failureMessages": [], "location": {"line": 157, "column": 7}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "invalidateEmailResetTokens"], "fullName": "Password Reset Middleware invalidateEmailResetTokens should invalidate existing tokens for email", "status": "passed", "title": "should invalidate existing tokens for email", "duration": 0.925783999999112, "failureMessages": [], "location": {"line": 163, "column": 7}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "passwordResetRateLimit"], "fullName": "Password Reset Middleware passwordResetRateLimit should allow requests within rate limit", "status": "passed", "title": "should allow requests within rate limit", "duration": 302.3308470000011, "failureMessages": [], "location": {"line": 185, "column": 7}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "passwordResetRateLimit"], "fullName": "Password Reset Middleware passwordResetRateLimit should block requests exceeding rate limit", "status": "passed", "title": "should block requests exceeding rate limit", "duration": 140.8104970000004, "failureMessages": [], "location": {"line": 198, "column": 7}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "emailResetRateLimit"], "fullName": "Password Reset Middleware emailResetRateLimit should allow requests for different emails", "status": "passed", "title": "should allow requests for different emails", "duration": 24.409351000000242, "failureMessages": [], "location": {"line": 222, "column": 7}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "emailResetRateLimit"], "fullName": "Password Reset Middleware emailResetRateLimit should block excessive requests for same email", "status": "passed", "title": "should block excessive requests for same email", "duration": 119.69587799999863, "failureMessages": [], "location": {"line": 240, "column": 7}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "validateResetTokenMiddleware"], "fullName": "Password Reset Middleware validateResetTokenMiddleware should validate token and attach data to request", "status": "passed", "title": "should validate token and attach data to request", "duration": 6.1891840000007505, "failureMessages": [], "location": {"line": 266, "column": 7}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "validateResetTokenMiddleware"], "fullName": "Password Reset Middleware validateResetTokenMiddleware should reject request with invalid token", "status": "passed", "title": "should reject request with invalid token", "duration": 13.907264999999825, "failureMessages": [], "location": {"line": 290, "column": 7}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "validateResetTokenMiddleware"], "fullName": "Password Reset Middleware validateResetTokenMiddleware should reject request without token", "status": "passed", "title": "should reject request without token", "duration": 76.68945100000019, "failureMessages": [], "location": {"line": 302, "column": 7}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "progressiveDelayMiddleware"], "fullName": "Password Reset Middleware progressiveDelayMiddleware should not delay first request", "status": "failed", "title": "should not delay first request", "duration": 24036.617377000002, "failureMessages": ["AssertionError: expected 8014 to be less than 100\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/passwordReset.test.js:328:35\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected 8003 to be less than 100\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/passwordReset.test.js:328:35\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected 8005 to be less than 100\n    at /Volumes/External Drive/Development/track-tasks/test/unit/middleware/passwordReset.test.js:328:35\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 316, "column": 7}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "progressiveDelayMiddleware"], "fullName": "Password Reset Middleware progressiveDelayMiddleware should handle missing email gracefully", "status": "passed", "title": "should handle missing email gracefully", "duration": 6.372883999996702, "failureMessages": [], "location": {"line": 331, "column": 7}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "cleanupExpiredData"], "fullName": "Password Reset Middleware cleanupExpiredData should clean up expired tokens", "status": "passed", "title": "should clean up expired tokens", "duration": 0.4593330000061542, "failureMessages": [], "location": {"line": 345, "column": 7}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "cleanupExpiredData"], "fullName": "Password Reset Middleware cleanupExpiredData should not throw errors during cleanup", "status": "passed", "title": "should not throw errors during cleanup", "duration": 0.7035700000051293, "failureMessages": [], "location": {"line": 362, "column": 7}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "getResetTokenStats"], "fullName": "Password Reset Middleware getResetTokenStats should return correct statistics", "status": "passed", "title": "should return correct statistics", "duration": 1.0287759999991977, "failureMessages": [], "location": {"line": 368, "column": 7}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "Integration tests"], "fullName": "Password Reset Middleware Integration tests should handle complete password reset flow", "status": "passed", "title": "should handle complete password reset flow", "duration": 0.6397179999985383, "failureMessages": [], "location": {"line": 393, "column": 7}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "Integration tests"], "fullName": "Password Reset Middleware Integration tests should handle token expiration correctly", "status": "passed", "title": "should handle token expiration correctly", "duration": 0.5189019999961602, "failureMessages": [], "location": {"line": 413, "column": 7}, "meta": {}}], "startTime": 1752872931195, "endTime": 1752872955957.5188, "status": "failed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/unit/middleware/passwordReset.test.js"}, {"assertionResults": [{"ancestorTitles": ["Route Guards (TEST-003)", "Authentication Guard"], "fullName": "Route Guards (TEST-003) Authentication Guard should initialize auth store if not initialized", "status": "passed", "title": "should initialize auth store if not initialized", "duration": 9.12709900000118, "failureMessages": [], "location": {"line": 94, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Authentication Guard"], "fullName": "Route Guards (TEST-003) Authentication Guard should not initialize auth store if already initialized", "status": "passed", "title": "should not initialize auth store if already initialized", "duration": 1.4742389999992156, "failureMessages": [], "location": {"line": 108, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Protected Routes"], "fullName": "Route Guards (TEST-003) Protected Routes should allow access to protected routes when authenticated", "status": "passed", "title": "should allow access to protected routes when authenticated", "duration": 1.0386890000008862, "failureMessages": [], "location": {"line": 123, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Protected Routes"], "fullName": "Route Guards (TEST-003) Protected Routes should redirect to login when accessing protected routes while unauthenticated", "status": "passed", "title": "should redirect to login when accessing protected routes while unauthenticated", "duration": 2.37789900000098, "failureMessages": [], "location": {"line": 136, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Protected Routes"], "fullName": "Route Guards (TEST-003) Protected Routes should preserve query parameters in redirect", "status": "passed", "title": "should preserve query parameters in redirect", "duration": 0.8711640000001353, "failureMessages": [], "location": {"line": 152, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Guest-Only Routes"], "fullName": "Route Guards (TEST-003) Guest-Only Routes should allow access to guest-only routes when unauthenticated", "status": "passed", "title": "should allow access to guest-only routes when unauthenticated", "duration": 0.5449029999999766, "failureMessages": [], "location": {"line": 171, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Guest-Only Routes"], "fullName": "Route Guards (TEST-003) Guest-Only Routes should redirect to home when accessing guest-only routes while authenticated", "status": "passed", "title": "should redirect to home when accessing guest-only routes while authenticated", "duration": 0.5190670000010869, "failureMessages": [], "location": {"line": 184, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Guest-Only Routes"], "fullName": "Route Guards (TEST-003) Guest-Only Routes should redirect authenticated users from register page", "status": "passed", "title": "should redirect authenticated users from register page", "duration": 1.4228210000001127, "failureMessages": [], "location": {"line": 197, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Guest-Only Routes"], "fullName": "Route Guards (TEST-003) Guest-Only Routes should redirect authenticated users from forgot-password page", "status": "passed", "title": "should redirect authenticated users from forgot-password page", "duration": 1.8384040000000823, "failureMessages": [], "location": {"line": 210, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Admin Routes"], "fullName": "Route Guards (TEST-003) Admin Routes should allow access to admin routes when user is admin", "status": "passed", "title": "should allow access to admin routes when user is admin", "duration": 0.6638120000006893, "failureMessages": [], "location": {"line": 225, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Admin Routes"], "fullName": "Route Guards (TEST-003) Admin Routes should redirect to unauthorized when non-admin user accesses admin routes", "status": "failed", "title": "should redirect to unauthorized when non-admin user accesses admin routes", "duration": 10.600792000001093, "failureMessages": ["AssertionError: expected \"spy\" to be called with arguments: [ { path: '/unauthorized' } ]\u001b[90m\n\nReceived: \n\n\u001b[1m  1st spy call:\n\n\u001b[22m\u001b[32m- [\u001b[90m\n\u001b[32m-   {\u001b[90m\n\u001b[32m-     \"path\": \"/unauthorized\",\u001b[90m\n\u001b[32m-   },\u001b[90m\n\u001b[32m- ]\u001b[90m\n\u001b[31m+ []\u001b[90m\n\u001b[39m\u001b[90m\n\nNumber of calls: \u001b[1m1\u001b[22m\n\u001b[39m\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1355:10)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/router/guards.test.js:250:20\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected \"spy\" to be called with arguments: [ { path: '/unauthorized' } ]\u001b[90m\n\nReceived: \n\n\u001b[1m  1st spy call:\n\n\u001b[22m\u001b[32m- [\u001b[90m\n\u001b[32m-   {\u001b[90m\n\u001b[32m-     \"path\": \"/unauthorized\",\u001b[90m\n\u001b[32m-   },\u001b[90m\n\u001b[32m- ]\u001b[90m\n\u001b[31m+ []\u001b[90m\n\u001b[39m\u001b[90m\n\nNumber of calls: \u001b[1m1\u001b[22m\n\u001b[39m\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1355:10)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/router/guards.test.js:250:20\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected \"spy\" to be called with arguments: [ { path: '/unauthorized' } ]\u001b[90m\n\nReceived: \n\n\u001b[1m  1st spy call:\n\n\u001b[22m\u001b[32m- [\u001b[90m\n\u001b[32m-   {\u001b[90m\n\u001b[32m-     \"path\": \"/unauthorized\",\u001b[90m\n\u001b[32m-   },\u001b[90m\n\u001b[32m- ]\u001b[90m\n\u001b[31m+ []\u001b[90m\n\u001b[39m\u001b[90m\n\nNumber of calls: \u001b[1m1\u001b[22m\n\u001b[39m\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1355:10)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/router/guards.test.js:250:20\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 239, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Admin Routes"], "fullName": "Route Guards (TEST-003) Admin Routes should redirect to unauthorized when unauthenticated user accesses admin routes", "status": "failed", "title": "should redirect to unauthorized when unauthenticated user accesses admin routes", "duration": 7.275929999999789, "failureMessages": ["AssertionError: expected \"spy\" to be called with arguments: [ { path: '/unauthorized' } ]\u001b[90m\n\nReceived: \n\n\u001b[1m  1st spy call:\n\n\u001b[22m\u001b[2m  [\u001b[22m\n\u001b[2m    {\u001b[22m\n\u001b[32m-     \"path\": \"/unauthorized\",\u001b[90m\n\u001b[31m+     \"path\": \"/login\",\u001b[90m\n\u001b[31m+     \"query\": {\u001b[90m\n\u001b[31m+       \"redirect\": \"/admin\",\u001b[90m\n\u001b[31m+     },\u001b[90m\n\u001b[2m    },\u001b[22m\n\u001b[2m  ]\u001b[22m\n\u001b[39m\u001b[90m\n\nNumber of calls: \u001b[1m1\u001b[22m\n\u001b[39m\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1355:10)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/router/guards.test.js:264:20\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected \"spy\" to be called with arguments: [ { path: '/unauthorized' } ]\u001b[90m\n\nReceived: \n\n\u001b[1m  1st spy call:\n\n\u001b[22m\u001b[2m  [\u001b[22m\n\u001b[2m    {\u001b[22m\n\u001b[32m-     \"path\": \"/unauthorized\",\u001b[90m\n\u001b[31m+     \"path\": \"/login\",\u001b[90m\n\u001b[31m+     \"query\": {\u001b[90m\n\u001b[31m+       \"redirect\": \"/admin\",\u001b[90m\n\u001b[31m+     },\u001b[90m\n\u001b[2m    },\u001b[22m\n\u001b[2m  ]\u001b[22m\n\u001b[39m\u001b[90m\n\nNumber of calls: \u001b[1m1\u001b[22m\n\u001b[39m\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1355:10)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/router/guards.test.js:264:20\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected \"spy\" to be called with arguments: [ { path: '/unauthorized' } ]\u001b[90m\n\nReceived: \n\n\u001b[1m  1st spy call:\n\n\u001b[22m\u001b[2m  [\u001b[22m\n\u001b[2m    {\u001b[22m\n\u001b[32m-     \"path\": \"/unauthorized\",\u001b[90m\n\u001b[31m+     \"path\": \"/login\",\u001b[90m\n\u001b[31m+     \"query\": {\u001b[90m\n\u001b[31m+       \"redirect\": \"/admin\",\u001b[90m\n\u001b[31m+     },\u001b[90m\n\u001b[2m    },\u001b[22m\n\u001b[2m  ]\u001b[22m\n\u001b[39m\u001b[90m\n\nNumber of calls: \u001b[1m1\u001b[22m\n\u001b[39m\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1355:10)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/router/guards.test.js:264:20\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 253, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Public Routes"], "fullName": "Route Guards (TEST-003) Public Routes should allow access to public routes regardless of authentication status", "status": "passed", "title": "should allow access to public routes regardless of authentication status", "duration": 0.7562050000015006, "failureMessages": [], "location": {"line": 269, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Public Routes"], "fullName": "Route Guards (TEST-003) Public Routes should allow authenticated users to access public routes", "status": "passed", "title": "should allow authenticated users to access public routes", "duration": 0.8029729999998381, "failureMessages": [], "location": {"line": 282, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Erro<PERSON>"], "fullName": "Route Guards (TEST-003) Error Handling should handle auth initialization errors gracefully", "status": "failed", "title": "should handle auth initialization errors gracefully", "duration": 4.099147999999332, "failureMessages": ["Error: Auth initialization failed\n    at /Volumes/External Drive/Development/track-tasks/test/unit/router/guards.test.js:303:54\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)", "Error: Auth initialization failed\n    at /Volumes/External Drive/Development/track-tasks/test/unit/router/guards.test.js:303:54\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)", "Error: Auth initialization failed\n    at /Volumes/External Drive/Development/track-tasks/test/unit/router/guards.test.js:303:54\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)"], "location": {"line": 297, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Route Meta Combinations"], "fullName": "Route Guards (TEST-003) Route Meta Combinations should handle routes with multiple meta requirements", "status": "passed", "title": "should handle routes with multiple meta requirements", "duration": 5.576609000001554, "failureMessages": [], "location": {"line": 314, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Route Meta Combinations"], "fullName": "Route Guards (TEST-003) Route Meta Combinations should prioritize authentication check over admin check", "status": "passed", "title": "should prioritize authentication check over admin check", "duration": 1.585371999999552, "failureMessages": [], "location": {"line": 331, "column": 7}, "meta": {}}], "startTime": 1752872927378, "endTime": 1752872927432.5854, "status": "failed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/unit/router/guards.test.js"}, {"assertionResults": [{"ancestorTitles": ["Authentication Routes", "POST /api/auth/login"], "fullName": "Authentication Routes POST /api/auth/login should login user with valid credentials", "status": "failed", "title": "should login user with valid credentials", "duration": 218.33568899999955, "failureMessages": ["AssertionError: expected 403 to be 200 // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/routes/auth.test.js:77:31\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected 403 to be 200 // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/routes/auth.test.js:77:31\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected 403 to be 200 // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/routes/auth.test.js:77:31\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 54, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/login"], "fullName": "Authentication Routes POST /api/auth/login should return validation error for invalid email", "status": "failed", "title": "should return validation error for invalid email", "duration": 33.09993299999951, "failureMessages": ["AssertionError: expected 403 to be 400 // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/routes/auth.test.js:93:31\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected 403 to be 400 // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/routes/auth.test.js:93:31\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected 429 to be 400 // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/routes/auth.test.js:93:31\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 85, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/login"], "fullName": "Authentication Routes POST /api/auth/login should return validation error for missing password", "status": "failed", "title": "should return validation error for missing password", "duration": 51.69933800000035, "failureMessages": ["AssertionError: expected 429 to be 400 // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/routes/auth.test.js:105:31\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected 429 to be 400 // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/routes/auth.test.js:105:31\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected 429 to be 400 // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/routes/auth.test.js:105:31\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 98, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/register"], "fullName": "Authentication Routes POST /api/auth/register should register user with valid data", "status": "failed", "title": "should register user with valid data", "duration": 31.069352999998955, "failureMessages": ["AssertionError: expected 429 to be 201 // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/routes/auth.test.js:134:31\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected 429 to be 201 // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/routes/auth.test.js:134:31\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected 429 to be 201 // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/routes/auth.test.js:134:31\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 112, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/register"], "fullName": "Authentication Routes POST /api/auth/register should return validation error for weak password", "status": "failed", "title": "should return validation error for weak password", "duration": 17.30434199999945, "failureMessages": ["AssertionError: expected 429 to be 400 // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/routes/auth.test.js:156:31\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected 429 to be 400 // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/routes/auth.test.js:156:31\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected 429 to be 400 // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/routes/auth.test.js:156:31\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 146, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/register"], "fullName": "Authentication Routes POST /api/auth/register should return validation error for password mismatch", "status": "failed", "title": "should return validation error for password mismatch", "duration": 87.70684500000061, "failureMessages": ["AssertionError: expected 429 to be 400 // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/routes/auth.test.js:171:31\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected 429 to be 400 // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/routes/auth.test.js:171:31\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected 429 to be 400 // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/routes/auth.test.js:171:31\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 161, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/logout"], "fullName": "Authentication Routes POST /api/auth/logout should logout user successfully", "status": "failed", "title": "should logout user successfully", "duration": 42.809471000000485, "failureMessages": ["AssertionError: expected 403 to be 200 // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/routes/auth.test.js:188:31\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected 403 to be 200 // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/routes/auth.test.js:188:31\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected 403 to be 200 // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/routes/auth.test.js:188:31\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 178, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/refresh"], "fullName": "Authentication Routes POST /api/auth/refresh should refresh token successfully", "status": "passed", "title": "should refresh token successfully", "duration": 34.22961100000248, "failureMessages": [], "location": {"line": 196, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/refresh"], "fullName": "Authentication Routes POST /api/auth/refresh should return validation error for missing refresh token", "status": "failed", "title": "should return validation error for missing refresh token", "duration": 31.541565000003175, "failureMessages": ["AssertionError: expected undefined to be false // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/routes/auth.test.js:222:37\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected undefined to be false // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/routes/auth.test.js:222:37\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected undefined to be false // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/routes/auth.test.js:222:37\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 216, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/forgot-password"], "fullName": "Authentication Routes POST /api/auth/forgot-password should send password reset email", "status": "failed", "title": "should send password reset email", "duration": 16.39395300000251, "failureMessages": ["AssertionError: expected 429 to be 200 // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/routes/auth.test.js:240:31\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected 429 to be 200 // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/routes/auth.test.js:240:31\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected 429 to be 200 // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/routes/auth.test.js:240:31\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 228, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/forgot-password"], "fullName": "Authentication Routes POST /api/auth/forgot-password should return validation error for invalid email", "status": "failed", "title": "should return validation error for invalid email", "duration": 13.433547000000544, "failureMessages": ["AssertionError: expected 429 to be 400 // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/routes/auth.test.js:253:31\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected 429 to be 400 // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/routes/auth.test.js:253:31\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected 429 to be 400 // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/routes/auth.test.js:253:31\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 246, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/reset-password"], "fullName": "Authentication Routes POST /api/auth/reset-password should reset password successfully", "status": "failed", "title": "should reset password successfully", "duration": 14.118623999998817, "failureMessages": ["AssertionError: expected 429 to be 200 // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/routes/auth.test.js:274:31\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected 429 to be 200 // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/routes/auth.test.js:274:31\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected 429 to be 200 // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/routes/auth.test.js:274:31\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 260, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/reset-password"], "fullName": "Authentication Routes POST /api/auth/reset-password should return validation error for missing token", "status": "failed", "title": "should return validation error for missing token", "duration": 16.21290500000032, "failureMessages": ["AssertionError: expected 429 to be 400 // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/routes/auth.test.js:288:31\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected 429 to be 400 // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/routes/auth.test.js:288:31\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected 429 to be 400 // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/routes/auth.test.js:288:31\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 280, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "GET /api/auth/me"], "fullName": "Authentication Routes GET /api/auth/me should get current user profile", "status": "failed", "title": "should get current user profile", "duration": 13.310207999998966, "failureMessages": ["AssertionError: expected 429 to be 200 // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/routes/auth.test.js:312:31\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected 429 to be 200 // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/routes/auth.test.js:312:31\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected 429 to be 200 // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/routes/auth.test.js:312:31\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 295, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "PUT /api/auth/profile"], "fullName": "Authentication Routes PUT /api/auth/profile should update user profile", "status": "failed", "title": "should update user profile", "duration": 22.98641399999906, "failureMessages": ["AssertionError: expected 429 to be 200 // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/routes/auth.test.js:341:31\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected 429 to be 200 // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/routes/auth.test.js:341:31\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected 429 to be 200 // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/routes/auth.test.js:341:31\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 320, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "PUT /api/auth/profile"], "fullName": "Authentication Routes PUT /api/auth/profile should return validation error for invalid email", "status": "failed", "title": "should return validation error for invalid email", "duration": 25.912007999999332, "failureMessages": ["AssertionError: expected 429 to be 400 // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/routes/auth.test.js:360:31\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected 429 to be 400 // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/routes/auth.test.js:360:31\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected 429 to be 400 // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/routes/auth.test.js:360:31\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 352, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "PUT /api/auth/password"], "fullName": "Authentication Routes PUT /api/auth/password should change password successfully", "status": "failed", "title": "should change password successfully", "duration": 12.121248999999807, "failureMessages": ["AssertionError: expected 429 to be 200 // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/routes/auth.test.js:382:31\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected 429 to be 200 // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/routes/auth.test.js:382:31\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected 429 to be 200 // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/routes/auth.test.js:382:31\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 367, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "PUT /api/auth/password"], "fullName": "Authentication Routes PUT /api/auth/password should return validation error for password mismatch", "status": "failed", "title": "should return validation error for password mismatch", "duration": 14.62412200000108, "failureMessages": ["AssertionError: expected 429 to be 400 // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/routes/auth.test.js:403:31\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected 429 to be 400 // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/routes/auth.test.js:403:31\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected 429 to be 400 // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/routes/auth.test.js:403:31\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 393, "column": 7}, "meta": {}}], "startTime": 1752872931953, "endTime": 1752872932655.624, "status": "failed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/unit/routes/auth.test.js"}, {"assertionResults": [], "startTime": 1752872916860, "endTime": 1752872916860, "status": "failed", "message": "Cannot access 'mockPocketBase' before initialization", "name": "/Volumes/External Drive/Development/track-tasks/test/unit/services/authService.test.js"}, {"assertionResults": [{"ancestorTitles": ["PocketBase Service", "Initialization"], "fullName": "PocketBase Service Initialization should initialize with default URL when no environment variable is set", "status": "passed", "title": "should initialize with default URL when no environment variable is set", "duration": 5.904762000000119, "failureMessages": [], "location": {"line": 40, "column": 7}, "meta": {}}, {"ancestorTitles": ["PocketBase Service", "Initialization"], "fullName": "PocketBase Service Initialization should load auth state from cookies on initialization", "status": "failed", "title": "should load auth state from cookies on initialization", "duration": 7.400432999998884, "failureMessages": ["AssertionError: expected \"spy\" to be called with arguments: [ '' ]\u001b[90m\n\nNumber of calls: \u001b[1m0\u001b[22m\n\u001b[39m\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1355:10)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/services/pocketbase.test.js:46:61\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)", "AssertionError: expected \"spy\" to be called with arguments: [ '' ]\u001b[90m\n\nNumber of calls: \u001b[1m0\u001b[22m\n\u001b[39m\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1355:10)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/services/pocketbase.test.js:46:61\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)", "AssertionError: expected \"spy\" to be called with arguments: [ '' ]\u001b[90m\n\nNumber of calls: \u001b[1m0\u001b[22m\n\u001b[39m\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1355:10)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/services/pocketbase.test.js:46:61\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)"], "location": {"line": 45, "column": 7}, "meta": {}}, {"ancestorTitles": ["PocketBase Service", "Authentication Check"], "fullName": "PocketBase Service Authentication Check should return false when user is not authenticated", "status": "passed", "title": "should return false when user is not authenticated", "duration": 0.6188610000008339, "failureMessages": [], "location": {"line": 51, "column": 7}, "meta": {}}, {"ancestorTitles": ["PocketBase Service", "Authentication Check"], "fullName": "PocketBase Service Authentication Check should return true when user is authenticated", "status": "passed", "title": "should return true when user is authenticated", "duration": 2.298891000000367, "failureMessages": [], "location": {"line": 56, "column": 7}, "meta": {}}, {"ancestorTitles": ["PocketBase Service", "Current User"], "fullName": "PocketBase Service Current User should return null when no user is authenticated", "status": "passed", "title": "should return null when no user is authenticated", "duration": 1.317218000000139, "failureMessages": [], "location": {"line": 63, "column": 7}, "meta": {}}, {"ancestorTitles": ["PocketBase Service", "Current User"], "fullName": "PocketBase Service Current User should return user data when authenticated", "status": "passed", "title": "should return user data when authenticated", "duration": 1.343579000000318, "failureMessages": [], "location": {"line": 68, "column": 7}, "meta": {}}, {"ancestorTitles": ["PocketBase Service", "<PERSON><PERSON>"], "fullName": "PocketBase Service Login should successfully authenticate user with valid credentials", "status": "passed", "title": "should successfully authenticate user with valid credentials", "duration": 1.2319310000002588, "failureMessages": [], "location": {"line": 76, "column": 7}, "meta": {}}, {"ancestorTitles": ["PocketBase Service", "<PERSON><PERSON>"], "fullName": "PocketBase Service Login should return error when login fails", "status": "passed", "title": "should return error when login fails", "duration": 0.6758709999994608, "failureMessages": [], "location": {"line": 90, "column": 7}, "meta": {}}, {"ancestorTitles": ["PocketBase Service", "Registration"], "fullName": "PocketBase Service Registration should successfully register a new user", "status": "passed", "title": "should successfully register a new user", "duration": 0.9217289999996865, "failureMessages": [], "location": {"line": 102, "column": 7}, "meta": {}}, {"ancestorTitles": ["PocketBase Service", "Registration"], "fullName": "PocketBase Service Registration should return error when registration fails", "status": "passed", "title": "should return error when registration fails", "duration": 0.7321520000004966, "failureMessages": [], "location": {"line": 116, "column": 7}, "meta": {}}, {"ancestorTitles": ["PocketBase Service", "Logout"], "fullName": "PocketBase Service Logout should clear auth store on logout", "status": "passed", "title": "should clear auth store on logout", "duration": 0.3925650000001042, "failureMessages": [], "location": {"line": 128, "column": 7}, "meta": {}}, {"ancestorTitles": ["PocketBase Service", "Collection Access"], "fullName": "PocketBase Service Collection Access should return collection reference", "status": "passed", "title": "should return collection reference", "duration": 0.33327400000052876, "failureMessages": [], "location": {"line": 135, "column": 7}, "meta": {}}, {"ancestorTitles": ["PocketBase Service", "Real-time Subscriptions"], "fullName": "PocketBase Service Real-time Subscriptions should subscribe to real-time updates", "status": "passed", "title": "should subscribe to real-time updates", "duration": 0.47715400000015507, "failureMessages": [], "location": {"line": 143, "column": 7}, "meta": {}}, {"ancestorTitles": ["PocketBase Service", "Real-time Subscriptions"], "fullName": "PocketBase Service Real-time Subscriptions should unsubscribe from real-time updates", "status": "passed", "title": "should unsubscribe from real-time updates", "duration": 0.30025799999930314, "failureMessages": [], "location": {"line": 152, "column": 7}, "meta": {}}, {"ancestorTitles": ["PocketBase Service", "Client Access"], "fullName": "PocketBase Service Client Access should return PocketBase client instance", "status": "passed", "title": "should return PocketBase client instance", "duration": 0.44308299999829615, "failureMessages": [], "location": {"line": 162, "column": 7}, "meta": {}}], "startTime": 1752872927464, "endTime": 1752872927491.443, "status": "failed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/unit/services/pocketbase.test.js"}, {"assertionResults": [{"ancestorTitles": ["SessionService", "createSession"], "fullName": "SessionService createSession should create a new session with valid data", "status": "passed", "title": "should create a new session with valid data", "duration": 3.662924000000203, "failureMessages": [], "location": {"line": 21, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "createSession"], "fullName": "SessionService createSession should generate unique session IDs", "status": "passed", "title": "should generate unique session IDs", "duration": 3.1022699999994074, "failureMessages": [], "location": {"line": 40, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "getSession"], "fullName": "SessionService getSession should retrieve session by ID", "status": "passed", "title": "should retrieve session by ID", "duration": 1.446778000001359, "failureMessages": [], "location": {"line": 49, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "getSession"], "fullName": "SessionService getSession should return null for non-existent session", "status": "passed", "title": "should return null for non-existent session", "duration": 0.6087770000012824, "failureMessages": [], "location": {"line": 58, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "getSessionByToken"], "fullName": "SessionService getSessionByToken should retrieve session by token", "status": "passed", "title": "should retrieve session by token", "duration": 0.9808499999999185, "failureMessages": [], "location": {"line": 65, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "getSessionByToken"], "fullName": "SessionService getSessionByToken should return null for non-existent token", "status": "passed", "title": "should return null for non-existent token", "duration": 0.5191950000007637, "failureMessages": [], "location": {"line": 75, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "getUserSessions"], "fullName": "SessionService getUserSessions should return all active sessions for a user", "status": "failed", "title": "should return all active sessions for a user", "duration": 19.03433099999893, "failureMessages": ["AssertionError: expected [ Session{ …(8) }, …(4) ] to have a length of 2 but got 5\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1257:20)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/services/sessionService.test.js:92:28\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)", "AssertionError: expected [ Session{ …(8) }, …(6) ] to have a length of 2 but got 7\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1257:20)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/services/sessionService.test.js:92:28\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)", "AssertionError: expected [ Session{ …(8) }, …(8) ] to have a length of 2 but got 9\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1257:20)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/services/sessionService.test.js:92:28\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)"], "location": {"line": 82, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "getUserSessions"], "fullName": "SessionService getUserSessions should return empty array for user with no sessions", "status": "passed", "title": "should return empty array for user with no sessions", "duration": 0.41204799999832176, "failureMessages": [], "location": {"line": 97, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "updateSessionActivity"], "fullName": "SessionService updateSessionActivity should update last activity for valid session", "status": "passed", "title": "should update last activity for valid session", "duration": 0.235515000000305, "failureMessages": [], "location": {"line": 104, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "updateSessionActivity"], "fullName": "SessionService updateSessionActivity should return false for non-existent token", "status": "passed", "title": "should return false for non-existent token", "duration": 0.26816599999983737, "failureMessages": [], "location": {"line": 119, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "updateSessionActivity"], "fullName": "SessionService updateSessionActivity should return false for inactive session", "status": "passed", "title": "should return false for inactive session", "duration": 0.8915239999987534, "failureMessages": [], "location": {"line": 124, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "invalidateSession"], "fullName": "SessionService invalidateSession should invalidate session by ID", "status": "passed", "title": "should invalidate session by ID", "duration": 0.2559820000005857, "failureMessages": [], "location": {"line": 137, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "invalidateSession"], "fullName": "SessionService invalidateSession should add token to blacklist when invalidating", "status": "passed", "title": "should add token to blacklist when invalidating", "duration": 0.3496889999987616, "failureMessages": [], "location": {"line": 147, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "invalidateSession"], "fullName": "SessionService invalidateSession should return false for non-existent session", "status": "passed", "title": "should return false for non-existent session", "duration": 0.20973500000036438, "failureMessages": [], "location": {"line": 157, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "invalidateSessionByToken"], "fullName": "SessionService invalidateSessionByToken should invalidate session by token", "status": "passed", "title": "should invalidate session by token", "duration": 0.3746640000008483, "failureMessages": [], "location": {"line": 164, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "invalidateSessionByToken"], "fullName": "SessionService invalidateSessionByToken should return false for non-existent token", "status": "passed", "title": "should return false for non-existent token", "duration": 0.18578200000047218, "failureMessages": [], "location": {"line": 176, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "invalidateAllUserSessions"], "fullName": "SessionService invalidateAllUserSessions should invalidate all sessions for a user", "status": "passed", "title": "should invalidate all sessions for a user", "duration": 1.4650949999995646, "failureMessages": ["AssertionError: expected 13 to be 3 // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/services/sessionService.test.js:193:32\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)"], "location": {"line": 183, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "invalidateAllUserSessions"], "fullName": "SessionService invalidateAllUserSessions should exclude specified token from invalidation", "status": "failed", "title": "should exclude specified token from invalidation", "duration": 7.611797999999908, "failureMessages": ["AssertionError: expected true to be false // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/services/sessionService.test.js:213:63\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)", "AssertionError: expected true to be false // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/services/sessionService.test.js:213:63\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)", "AssertionError: expected true to be false // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/services/sessionService.test.js:213:63\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)"], "location": {"line": 201, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "isTokenBlacklisted"], "fullName": "SessionService isTokenBlacklisted should return true for blacklisted tokens", "status": "passed", "title": "should return true for blacklisted tokens", "duration": 0.34001899999930174, "failureMessages": [], "location": {"line": 219, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "isTokenBlacklisted"], "fullName": "SessionService isTokenBlacklisted should return false for non-blacklisted tokens", "status": "passed", "title": "should return false for non-blacklisted tokens", "duration": 0.2863049999996292, "failureMessages": [], "location": {"line": 228, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "validateSession"], "fullName": "SessionService validateSession should validate active session and update activity", "status": "failed", "title": "should validate active session and update activity", "duration": 3.122438999998849, "failureMessages": ["AssertionError: expected false to be true // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/services/sessionService.test.js:243:32\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)", "AssertionError: expected false to be true // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/services/sessionService.test.js:243:32\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)", "AssertionError: expected false to be true // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/services/sessionService.test.js:243:32\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)"], "location": {"line": 237, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "validateSession"], "fullName": "SessionService validateSession should reject blacklisted tokens", "status": "passed", "title": "should reject blacklisted tokens", "duration": 0.3283799999990151, "failureMessages": [], "location": {"line": 248, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "validateSession"], "fullName": "SessionService validateSession should reject non-existent sessions", "status": "passed", "title": "should reject non-existent sessions", "duration": 0.2803990000011254, "failureMessages": [], "location": {"line": 260, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "validateSession"], "fullName": "SessionService validateSession should reject inactive sessions", "status": "failed", "title": "should reject inactive sessions", "duration": 5.169869999999719, "failureMessages": ["AssertionError: expected 'Token is blacklisted' to be 'Session is inactive' // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/services/sessionService.test.js:277:33\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)", "AssertionError: expected 'Token is blacklisted' to be 'Session is inactive' // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/services/sessionService.test.js:277:33\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)", "AssertionError: expected 'Token is blacklisted' to be 'Session is inactive' // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/services/sessionService.test.js:277:33\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)"], "location": {"line": 267, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "getSessionStats"], "fullName": "SessionService getSessionStats should return correct session statistics", "status": "failed", "title": "should return correct session statistics", "duration": 4.52670500000022, "failureMessages": ["AssertionError: expected 14 to be 2 // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/services/sessionService.test.js:293:36\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)", "AssertionError: expected 16 to be 2 // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/services/sessionService.test.js:293:36\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)", "AssertionError: expected 18 to be 2 // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/services/sessionService.test.js:293:36\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)"], "location": {"line": 282, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "forceLogoutUser"], "fullName": "SessionService forceLogoutUser should force logout all sessions for a user", "status": "passed", "title": "should force logout all sessions for a user", "duration": 9.714417000001049, "failureMessages": ["AssertionError: expected 9 to be 2 // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/unit/services/sessionService.test.js:311:42\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)"], "location": {"line": 302, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "getUserSessionDetails"], "fullName": "SessionService getUserSessionDetails should return detailed session information", "status": "passed", "title": "should return detailed session information", "duration": 1.1062700000002224, "failureMessages": [], "location": {"line": 317, "column": 7}, "meta": {}}], "startTime": 1752872931175, "endTime": 1752872931245.1062, "status": "failed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/unit/services/sessionService.test.js"}]}