{"numTotalTestSuites": 26, "numPassedTestSuites": 26, "numFailedTestSuites": 0, "numPendingTestSuites": 0, "numTotalTests": 45, "numPassedTests": 45, "numFailedTests": 0, "numPendingTests": 0, "numTodoTests": 0, "snapshot": {"added": 0, "failure": false, "filesAdded": 0, "filesRemoved": 0, "filesRemovedList": [], "filesUnmatched": 0, "filesUpdated": 0, "matched": 0, "total": 0, "unchecked": 0, "uncheckedKeysByFile": [], "unmatched": 0, "updated": 0, "didUpdate": false}, "startTime": 1752957434630, "success": true, "testResults": [{"assertionResults": [{"ancestorTitles": ["Authentication Routes", "POST /api/auth/login"], "fullName": "Authentication Routes POST /api/auth/login should login user with valid credentials", "status": "passed", "title": "should login user with valid credentials", "duration": 180.20078300000023, "failureMessages": [], "location": {"line": 201, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/login"], "fullName": "Authentication Routes POST /api/auth/login should return validation error for invalid email", "status": "passed", "title": "should return validation error for invalid email", "duration": 4.779554000000644, "failureMessages": [], "location": {"line": 232, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/login"], "fullName": "Authentication Routes POST /api/auth/login should return validation error for missing password", "status": "passed", "title": "should return validation error for missing password", "duration": 4.025680000000648, "failureMessages": [], "location": {"line": 245, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/register"], "fullName": "Authentication Routes POST /api/auth/register should register user with valid data", "status": "passed", "title": "should register user with valid data", "duration": 4.468716999999742, "failureMessages": [], "location": {"line": 259, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/register"], "fullName": "Authentication Routes POST /api/auth/register should return validation error for weak password", "status": "passed", "title": "should return validation error for weak password", "duration": 4.481153999999151, "failureMessages": [], "location": {"line": 293, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/register"], "fullName": "Authentication Routes POST /api/auth/register should return validation error for password mismatch", "status": "passed", "title": "should return validation error for password mismatch", "duration": 3.3451420000001235, "failureMessages": [], "location": {"line": 308, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/logout"], "fullName": "Authentication Routes POST /api/auth/logout should logout user successfully", "status": "passed", "title": "should logout user successfully", "duration": 3.8153050000000803, "failureMessages": [], "location": {"line": 325, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/refresh"], "fullName": "Authentication Routes POST /api/auth/refresh should refresh token successfully", "status": "passed", "title": "should refresh token successfully", "duration": 3.182847000000038, "failureMessages": [], "location": {"line": 343, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/refresh"], "fullName": "Authentication Routes POST /api/auth/refresh should return validation error for missing refresh token", "status": "passed", "title": "should return validation error for missing refresh token", "duration": 4.564656999998988, "failureMessages": [], "location": {"line": 363, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/forgot-password"], "fullName": "Authentication Routes POST /api/auth/forgot-password should send password reset email", "status": "passed", "title": "should send password reset email", "duration": 3.8167730000004667, "failureMessages": [], "location": {"line": 375, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/forgot-password"], "fullName": "Authentication Routes POST /api/auth/forgot-password should return validation error for invalid email", "status": "passed", "title": "should return validation error for invalid email", "duration": 3.3619589999998425, "failureMessages": [], "location": {"line": 393, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/reset-password"], "fullName": "Authentication Routes POST /api/auth/reset-password should reset password successfully", "status": "passed", "title": "should reset password successfully", "duration": 4.717432999999801, "failureMessages": [], "location": {"line": 407, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/reset-password"], "fullName": "Authentication Routes POST /api/auth/reset-password should return validation error for missing token", "status": "passed", "title": "should return validation error for missing token", "duration": 3.7999569999992673, "failureMessages": [], "location": {"line": 427, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "GET /api/auth/me"], "fullName": "Authentication Routes GET /api/auth/me should get current user profile", "status": "passed", "title": "should get current user profile", "duration": 3.003838000000542, "failureMessages": [], "location": {"line": 442, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "PUT /api/auth/profile"], "fullName": "Authentication Routes PUT /api/auth/profile should update user profile", "status": "passed", "title": "should update user profile", "duration": 2.985289999998713, "failureMessages": [], "location": {"line": 467, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "PUT /api/auth/profile"], "fullName": "Authentication Routes PUT /api/auth/profile should return validation error for invalid email", "status": "passed", "title": "should return validation error for invalid email", "duration": 2.5939020000005257, "failureMessages": [], "location": {"line": 499, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "PUT /api/auth/password"], "fullName": "Authentication Routes PUT /api/auth/password should change password successfully", "status": "passed", "title": "should change password successfully", "duration": 2.6629099999991013, "failureMessages": [], "location": {"line": 514, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "PUT /api/auth/password"], "fullName": "Authentication Routes PUT /api/auth/password should return validation error for password mismatch", "status": "passed", "title": "should return validation error for password mismatch", "duration": 2.7621190000008937, "failureMessages": [], "location": {"line": 540, "column": 5}, "meta": {}}], "startTime": 1752957442264, "endTime": 1752957442507.7622, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/unit/routes/auth.test.js"}, {"assertionResults": [{"ancestorTitles": ["SessionService", "createSession"], "fullName": "SessionService createSession should create a new session with valid data", "status": "passed", "title": "should create a new session with valid data", "duration": 9.77813099999912, "failureMessages": [], "location": {"line": 23, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "createSession"], "fullName": "SessionService createSession should generate unique session IDs", "status": "passed", "title": "should generate unique session IDs", "duration": 1.2641639999983454, "failureMessages": [], "location": {"line": 42, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "getSession"], "fullName": "SessionService getSession should retrieve session by ID", "status": "passed", "title": "should retrieve session by ID", "duration": 0.4261450000012701, "failureMessages": [], "location": {"line": 51, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "getSession"], "fullName": "SessionService getSession should return null for non-existent session", "status": "passed", "title": "should return null for non-existent session", "duration": 0.29136299999845505, "failureMessages": [], "location": {"line": 60, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "getSessionByToken"], "fullName": "SessionService getSessionByToken should retrieve session by token", "status": "passed", "title": "should retrieve session by token", "duration": 0.41577700000016193, "failureMessages": [], "location": {"line": 67, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "getSessionByToken"], "fullName": "SessionService getSessionByToken should return null for non-existent token", "status": "passed", "title": "should return null for non-existent token", "duration": 0.2892549999996845, "failureMessages": [], "location": {"line": 77, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "getUserSessions"], "fullName": "SessionService getUserSessions should return all active sessions for a user", "status": "passed", "title": "should return all active sessions for a user", "duration": 1.7580390000002808, "failureMessages": [], "location": {"line": 84, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "getUserSessions"], "fullName": "SessionService getUserSessions should return empty array for user with no sessions", "status": "passed", "title": "should return empty array for user with no sessions", "duration": 0.36670199999934994, "failureMessages": [], "location": {"line": 99, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "updateSessionActivity"], "fullName": "SessionService updateSessionActivity should update last activity for valid session", "status": "passed", "title": "should update last activity for valid session", "duration": 11.460359999999127, "failureMessages": [], "location": {"line": 106, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "updateSessionActivity"], "fullName": "SessionService updateSessionActivity should return false for non-existent token", "status": "passed", "title": "should return false for non-existent token", "duration": 0.7546070000007603, "failureMessages": [], "location": {"line": 121, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "updateSessionActivity"], "fullName": "SessionService updateSessionActivity should return false for inactive session", "status": "passed", "title": "should return false for inactive session", "duration": 0.560034000000087, "failureMessages": [], "location": {"line": 126, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "invalidateSession"], "fullName": "SessionService invalidateSession should invalidate session by ID", "status": "passed", "title": "should invalidate session by ID", "duration": 0.43156900000030873, "failureMessages": [], "location": {"line": 139, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "invalidateSession"], "fullName": "SessionService invalidateSession should add token to blacklist when invalidating", "status": "passed", "title": "should add token to blacklist when invalidating", "duration": 0.24965999999949418, "failureMessages": [], "location": {"line": 149, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "invalidateSession"], "fullName": "SessionService invalidateSession should return false for non-existent session", "status": "passed", "title": "should return false for non-existent session", "duration": 0.42657900000085647, "failureMessages": [], "location": {"line": 159, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "invalidateSessionByToken"], "fullName": "SessionService invalidateSessionByToken should invalidate session by token", "status": "passed", "title": "should invalidate session by token", "duration": 0.4159989999989193, "failureMessages": [], "location": {"line": 166, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "invalidateSessionByToken"], "fullName": "SessionService invalidateSessionByToken should return false for non-existent token", "status": "passed", "title": "should return false for non-existent token", "duration": 0.19309099999918544, "failureMessages": [], "location": {"line": 178, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "invalidateAllUserSessions"], "fullName": "SessionService invalidateAllUserSessions should invalidate all sessions for a user", "status": "passed", "title": "should invalidate all sessions for a user", "duration": 0.4166160000004311, "failureMessages": [], "location": {"line": 185, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "invalidateAllUserSessions"], "fullName": "SessionService invalidateAllUserSessions should exclude specified token from invalidation", "status": "passed", "title": "should exclude specified token from invalidation", "duration": 0.4662339999995311, "failureMessages": [], "location": {"line": 203, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "isTokenBlacklisted"], "fullName": "SessionService isTokenBlacklisted should return true for blacklisted tokens", "status": "passed", "title": "should return true for blacklisted tokens", "duration": 0.25904199999968114, "failureMessages": [], "location": {"line": 221, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "isTokenBlacklisted"], "fullName": "SessionService isTokenBlacklisted should return false for non-blacklisted tokens", "status": "passed", "title": "should return false for non-blacklisted tokens", "duration": 0.19947599999977683, "failureMessages": [], "location": {"line": 230, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "validateSession"], "fullName": "SessionService validateSession should validate active session and update activity", "status": "passed", "title": "should validate active session and update activity", "duration": 0.34571299999879557, "failureMessages": [], "location": {"line": 239, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "validateSession"], "fullName": "SessionService validateSession should reject blacklisted tokens", "status": "passed", "title": "should reject blacklisted tokens", "duration": 0.27797199999986333, "failureMessages": [], "location": {"line": 250, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "validateSession"], "fullName": "SessionService validateSession should reject non-existent sessions", "status": "passed", "title": "should reject non-existent sessions", "duration": 0.5535859999999957, "failureMessages": [], "location": {"line": 262, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "validateSession"], "fullName": "SessionService validateSession should reject inactive sessions", "status": "passed", "title": "should reject inactive sessions", "duration": 0.3919300000015937, "failureMessages": [], "location": {"line": 269, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "getSessionStats"], "fullName": "SessionService getSessionStats should return correct session statistics", "status": "passed", "title": "should return correct session statistics", "duration": 0.4793580000005022, "failureMessages": [], "location": {"line": 284, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "forceLogoutUser"], "fullName": "SessionService forceLogoutUser should force logout all sessions for a user", "status": "passed", "title": "should force logout all sessions for a user", "duration": 0.4710660000000644, "failureMessages": [], "location": {"line": 304, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "getUserSessionDetails"], "fullName": "SessionService getUserSessionDetails should return detailed session information", "status": "passed", "title": "should return detailed session information", "duration": 1.2531640000015614, "failureMessages": [], "location": {"line": 319, "column": 7}, "meta": {}}], "startTime": 1752957442267, "endTime": 1752957442302.2532, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/unit/services/sessionService.test.js"}]}