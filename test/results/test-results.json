{"numTotalTestSuites": 166, "numPassedTestSuites": 164, "numFailedTestSuites": 2, "numPendingTestSuites": 0, "numTotalTests": 293, "numPassedTests": 293, "numFailedTests": 0, "numPendingTests": 0, "numTodoTests": 0, "snapshot": {"added": 0, "failure": false, "filesAdded": 0, "filesRemoved": 0, "filesRemovedList": [], "filesUnmatched": 0, "filesUpdated": 0, "matched": 0, "total": 0, "unchecked": 0, "uncheckedKeysByFile": [], "unmatched": 0, "updated": 0, "didUpdate": false}, "startTime": 1752960745404, "success": false, "testResults": [{"assertionResults": [{"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "User Registration Journey"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) User Registration Journey should complete full user registration flow", "status": "passed", "title": "should complete full user registration flow", "duration": 13.164700000001176, "failureMessages": [], "location": {"line": 90, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "User Registration Journey"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) User Registration Journey should handle registration validation errors", "status": "passed", "title": "should handle registration validation errors", "duration": 1.2695200000016484, "failureMessages": [], "location": {"line": 117, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "User Registration Journey"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) User Registration Journey should handle existing email registration attempt", "status": "passed", "title": "should handle existing email registration attempt", "duration": 1.226569999998901, "failureMessages": [], "location": {"line": 138, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "User Login <PERSON>"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) User Login Journey should complete successful login flow", "status": "passed", "title": "should complete successful login flow", "duration": 1.392743000000337, "failureMessages": [], "location": {"line": 161, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "User Login <PERSON>"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) User Login Journey should handle invalid login credentials", "status": "passed", "title": "should handle invalid login credentials", "duration": 1.0627290000011271, "failureMessages": [], "location": {"line": 184, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "User Login <PERSON>"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) User Login Journey should redirect to intended page after login", "status": "passed", "title": "should redirect to intended page after login", "duration": 1.2723519999999553, "failureMessages": [], "location": {"line": 203, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "User Lo<PERSON>ut <PERSON>"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) User Logout Journey should complete logout flow", "status": "passed", "title": "should complete logout flow", "duration": 1.627294999998412, "failureMessages": [], "location": {"line": 223, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "Password Reset Journey"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) Password Reset Journey should complete password reset request flow", "status": "passed", "title": "should complete password reset request flow", "duration": 0.6909960000011779, "failureMessages": [], "location": {"line": 246, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "Password Reset Journey"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) Password Reset Journey should complete password reset confirmation flow", "status": "passed", "title": "should complete password reset confirmation flow", "duration": 0.8359070000005886, "failureMessages": [], "location": {"line": 262, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "Profile Management Journey"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) Profile Management Journey should complete profile update flow", "status": "passed", "title": "should complete profile update flow", "duration": 3.174250000000029, "failureMessages": [], "location": {"line": 285, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "Profile Management Journey"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) Profile Management Journey should complete password change flow", "status": "passed", "title": "should complete password change flow", "duration": 1.5311130000009143, "failureMessages": [], "location": {"line": 303, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "Session Management Journey"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) Session Management Journey should handle session timeout gracefully", "status": "passed", "title": "should handle session timeout gracefully", "duration": 0.8065600000008999, "failureMessages": [], "location": {"line": 324, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "Session Management Journey"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) Session Management Journey should handle concurrent sessions", "status": "passed", "title": "should handle concurrent sessions", "duration": 0.48285200000100303, "failureMessages": [], "location": {"line": 347, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "Error <PERSON>"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) Error Handling Journey should handle network errors gracefully", "status": "passed", "title": "should handle network errors gracefully", "duration": 0.7592519999998331, "failureMessages": [], "location": {"line": 367, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "Error <PERSON>"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) Error Handling Journey should handle server errors gracefully", "status": "passed", "title": "should handle server errors gracefully", "duration": 2.1697600000006787, "failureMessages": [], "location": {"line": 388, "column": 7}, "meta": {}}], "startTime": 1752960771201, "endTime": 1752960771236.1697, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/e2e/auth-journeys.test.js"}, {"assertionResults": [{"ancestorTitles": ["Authentication Flows Integration Tests (TEST-005)", "Complete Login Flow"], "fullName": "Authentication Flows Integration Tests (TEST-005) Complete Login Flow should complete full login flow successfully", "status": "passed", "title": "should complete full login flow successfully", "duration": 31.72933699999703, "failureMessages": [], "location": {"line": 66, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Flows Integration Tests (TEST-005)", "Complete Login Flow"], "fullName": "Authentication Flows Integration Tests (TEST-005) Complete Login Flow should handle login failure gracefully", "status": "passed", "title": "should handle login failure gracefully", "duration": 10.945728999999119, "failureMessages": [], "location": {"line": 101, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Flows Integration Tests (TEST-005)", "Complete Login Flow"], "fullName": "Authentication Flows Integration Tests (TEST-005) Complete Login Flow should handle network errors during login", "status": "passed", "title": "should handle network errors during login", "duration": 3.0308139999979176, "failureMessages": [], "location": {"line": 126, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Flows Integration Tests (TEST-005)", "Complete Registration Flow"], "fullName": "Authentication Flows Integration Tests (TEST-005) Complete Registration Flow should complete full registration flow successfully", "status": "passed", "title": "should complete full registration flow successfully", "duration": 2.511420000002545, "failureMessages": [], "location": {"line": 142, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Flows Integration Tests (TEST-005)", "Complete Registration Flow"], "fullName": "Authentication Flows Integration Tests (TEST-005) Complete Registration Flow should handle registration validation errors", "status": "passed", "title": "should handle registration validation errors", "duration": 0.9789529999980005, "failureMessages": [], "location": {"line": 165, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Flows Integration Tests (TEST-005)", "Complete Logout Flow"], "fullName": "Authentication Flows Integration Tests (TEST-005) Complete Logout Flow should complete full logout flow successfully", "status": "passed", "title": "should complete full logout flow successfully", "duration": 3.6923730000016803, "failureMessages": [], "location": {"line": 187, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Flows Integration Tests (TEST-005)", "Complete Logout Flow"], "fullName": "Authentication Flows Integration Tests (TEST-005) Complete Logout Flow should handle logout errors gracefully", "status": "passed", "title": "should handle logout errors gracefully", "duration": 4.1589530000019295, "failureMessages": [], "location": {"line": 212, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Flows Integration Tests (TEST-005)", "Password Reset Flow"], "fullName": "Authentication Flows Integration Tests (TEST-005) Password Reset Flow should complete password reset request flow", "status": "passed", "title": "should complete password reset request flow", "duration": 1.2080470000000787, "failureMessages": [], "location": {"line": 232, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Flows Integration Tests (TEST-005)", "Password Reset Flow"], "fullName": "Authentication Flows Integration Tests (TEST-005) Password Reset Flow should complete password reset confirmation flow", "status": "passed", "title": "should complete password reset confirmation flow", "duration": 1.5207329999975627, "failureMessages": [], "location": {"line": 245, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Flows Integration Tests (TEST-005)", "Session Restoration Flow"], "fullName": "Authentication Flows Integration Tests (TEST-005) Session Restoration Flow should restore session from localStorage on initialization", "status": "passed", "title": "should restore session from localStorage on initialization", "duration": 2.2016920000023674, "failureMessages": [], "location": {"line": 260, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Flows Integration Tests (TEST-005)", "Session Restoration Flow"], "fullName": "Authentication Flows Integration Tests (TEST-005) Session Restoration Flow should handle invalid stored session gracefully", "status": "passed", "title": "should handle invalid stored session gracefully", "duration": 1.4667130000016186, "failureMessages": [], "location": {"line": 288, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Flows Integration Tests (TEST-005)", "Profile Management Flow"], "fullName": "Authentication Flows Integration Tests (TEST-005) Profile Management Flow should complete profile update flow", "status": "passed", "title": "should complete profile update flow", "duration": 1.1762909999997646, "failureMessages": [], "location": {"line": 310, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Flows Integration Tests (TEST-005)", "Profile Management Flow"], "fullName": "Authentication Flows Integration Tests (TEST-005) Profile Management Flow should complete password change flow", "status": "passed", "title": "should complete password change flow", "duration": 1.9218839999994088, "failureMessages": [], "location": {"line": 331, "column": 7}, "meta": {}}], "startTime": 1752960771321, "endTime": 1752960771389.9219, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/integration/auth-flows.test.js"}, {"assertionResults": [{"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Network Error Handling"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Network Error Handling should handle network errors during login", "status": "passed", "title": "should handle network errors during login", "duration": 12.112213000000338, "failureMessages": [], "location": {"line": 101, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Network Error Handling"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Network Error Handling should handle timeout errors", "status": "passed", "title": "should handle timeout errors", "duration": 51.887111000000004, "failureMessages": [], "location": {"line": 118, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Network Error Handling"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Network Error Handling should handle server errors gracefully", "status": "passed", "title": "should handle server errors gracefully", "duration": 1.2179909999995289, "failureMessages": [], "location": {"line": 137, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "LocalStorage Error Handling"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) LocalStorage Error Handling should handle localStorage unavailability", "status": "passed", "title": "should handle localStorage unavailability", "duration": 4.416430000001128, "failureMessages": [], "location": {"line": 158, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "LocalStorage Error Handling"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) LocalStorage Error Handling should handle localStorage quota exceeded", "status": "passed", "title": "should handle localStorage quota exceeded", "duration": 1.5582500000000437, "failureMessages": [], "location": {"line": 175, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "LocalStorage Error Handling"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) LocalStorage Error Handling should handle corrupted localStorage data", "status": "passed", "title": "should handle corrupted localStorage data", "duration": 2.204952999998568, "failureMessages": [], "location": {"line": 191, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Authentication Edge Cases"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Authentication Edge Cases should handle simultaneous login attempts", "status": "passed", "title": "should handle simultaneous login attempts", "duration": 104.62916300000143, "failureMessages": [], "location": {"line": 212, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Authentication Edge Cases"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Authentication Edge Cases should handle login with empty credentials", "status": "passed", "title": "should handle login with empty credentials", "duration": 0.72329499999978, "failureMessages": [], "location": {"line": 243, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Authentication Edge Cases"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Authentication Edge Cases should handle login with null/undefined credentials", "status": "passed", "title": "should handle login with null/undefined credentials", "duration": 0.7147750000003725, "failureMessages": [], "location": {"line": 252, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Authentication Edge Cases"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Authentication Edge Cases should handle extremely long input values", "status": "passed", "title": "should handle extremely long input values", "duration": 101.24319299999843, "failureMessages": [], "location": {"line": 263, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Token Refresh Edge Cases"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Token Refresh Edge Cases should handle token refresh when already refreshing", "status": "passed", "title": "should handle token refresh when already refreshing", "duration": 202.5902640000004, "failureMessages": [], "location": {"line": 280, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Token Refresh Edge Cases"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Token Refresh Edge Cases should handle token refresh with invalid response", "status": "passed", "title": "should handle token refresh with invalid response", "duration": 0.8462090000011813, "failureMessages": [], "location": {"line": 315, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Session Management Edge Cases"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Session Management Edge Cases should handle session restoration with partial data", "status": "passed", "title": "should handle session restoration with partial data", "duration": 0.7738730000000942, "failureMessages": [], "location": {"line": 333, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Session Management Edge Cases"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Session Management Edge Cases should handle concurrent session modifications", "status": "passed", "title": "should handle concurrent session modifications", "duration": 1.6623990000007325, "failureMessages": [], "location": {"line": 352, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Memory and Performance Edge Cases"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Memory and Performance Edge Cases should handle memory pressure gracefully", "status": "passed", "title": "should handle memory pressure gracefully", "duration": 308.2546600000005, "failureMessages": [], "location": {"line": 379, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Memory and Performance Edge Cases"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Memory and Performance Edge Cases should handle rapid successive operations", "status": "passed", "title": "should handle rapid successive operations", "duration": 148.27551200000016, "failureMessages": [], "location": {"line": 394, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Browser Compatibility Edge Cases"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Browser Compatibility Edge Cases should handle missing browser APIs gracefully", "status": "passed", "title": "should handle missing browser APIs gracefully", "duration": 3.1355359999997745, "failureMessages": [], "location": {"line": 415, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Browser Compatibility Edge Cases"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Browser Compatibility Edge Cases should handle disabled cookies", "status": "passed", "title": "should handle disabled cookies", "duration": 1.6617129999995086, "failureMessages": [], "location": {"line": 428, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Data Validation Edge Cases"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Data Validation Edge Cases should handle malformed server responses", "status": "passed", "title": "should handle malformed server responses", "duration": 2.8168900000000576, "failureMessages": [], "location": {"line": 445, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Data Validation Edge Cases"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Data Validation Edge Cases should handle unexpected data types", "status": "passed", "title": "should handle unexpected data types", "duration": 2.682042000000365, "failureMessages": [], "location": {"line": 465, "column": 7}, "meta": {}}], "startTime": 1752960757005, "endTime": 1752960757960.6821, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/integration/error-handling.test.js"}, {"assertionResults": [{"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Role Checking"], "fullName": "Role-Based Access Control Tests (TEST-007) Role Checking should correctly identify admin users", "status": "passed", "title": "should correctly identify admin users", "duration": 9.306518000001233, "failureMessages": [], "location": {"line": 107, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Role Checking"], "fullName": "Role-Based Access Control Tests (TEST-007) Role Checking should correctly identify regular users", "status": "passed", "title": "should correctly identify regular users", "duration": 1.3748900000027788, "failureMessages": [], "location": {"line": 120, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Role Checking"], "fullName": "Role-Based Access Control Tests (TEST-007) Role Checking should handle multiple roles", "status": "passed", "title": "should handle multiple roles", "duration": 0.9414709999982733, "failureMessages": [], "location": {"line": 135, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Permission Checking"], "fullName": "Role-Based Access Control Tests (TEST-007) Permission Checking should check individual permissions correctly", "status": "passed", "title": "should check individual permissions correctly", "duration": 1.0927670000019134, "failureMessages": [], "location": {"line": 152, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Permission Checking"], "fullName": "Role-Based Access Control Tests (TEST-007) Permission Checking should handle admin users with all permissions", "status": "passed", "title": "should handle admin users with all permissions", "duration": 0.8029610000012326, "failureMessages": [], "location": {"line": 167, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Permission Checking"], "fullName": "Role-Based Access Control Tests (TEST-007) Permission Checking should handle users with no permissions", "status": "passed", "title": "should handle users with no permissions", "duration": 0.6431960000008985, "failureMessages": [], "location": {"line": 182, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Route Access Control"], "fullName": "Role-Based Access Control Tests (TEST-007) Route Access Control should allow admin access to admin routes", "status": "passed", "title": "should allow admin access to admin routes", "duration": 3.4662529999986873, "failureMessages": [], "location": {"line": 197, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Route Access Control"], "fullName": "Role-Based Access Control Tests (TEST-007) Route Access Control should deny regular user access to admin routes", "status": "passed", "title": "should deny regular user access to admin routes", "duration": 5.165734999998676, "failureMessages": [], "location": {"line": 211, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Route Access Control"], "fullName": "Role-Based Access Control Tests (TEST-007) Route Access Control should enforce role-based route access", "status": "passed", "title": "should enforce role-based route access", "duration": 1.0466020000021672, "failureMessages": [], "location": {"line": 225, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Route Access Control"], "fullName": "Role-Based Access Control Tests (TEST-007) Route Access Control should deny access when user lacks required role", "status": "passed", "title": "should deny access when user lacks required role", "duration": 1.0488359999981185, "failureMessages": [], "location": {"line": 239, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Route Access Control"], "fullName": "Role-Based Access Control Tests (TEST-007) Route Access Control should enforce permission-based route access", "status": "passed", "title": "should enforce permission-based route access", "duration": 0.7943460000024061, "failureMessages": [], "location": {"line": 253, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Route Access Control"], "fullName": "Role-Based Access Control Tests (TEST-007) Route Access Control should deny access when user lacks required permissions", "status": "passed", "title": "should deny access when user lacks required permissions", "duration": 0.8454700000002049, "failureMessages": [], "location": {"line": 271, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Route Access Control"], "fullName": "Role-Based Access Control Tests (TEST-007) Route Access Control should redirect unauthenticated users to login", "status": "passed", "title": "should redirect unauthenticated users to login", "duration": 0.6622729999980947, "failureMessages": [], "location": {"line": 289, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Complex Access Control Scenarios"], "fullName": "Role-Based Access Control Tests (TEST-007) Complex Access Control Scenarios should handle routes with multiple requirements", "status": "passed", "title": "should handle routes with multiple requirements", "duration": 1.0576189999992494, "failureMessages": [], "location": {"line": 302, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Complex Access Control Scenarios"], "fullName": "Role-Based Access Control Tests (TEST-007) Complex Access Control Scenarios should deny access if any requirement is not met", "status": "passed", "title": "should deny access if any requirement is not met", "duration": 0.8649130000012519, "failureMessages": [], "location": {"line": 324, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Dynamic Permission Checking"], "fullName": "Role-Based Access Control Tests (TEST-007) Dynamic Permission Checking should check resource-specific permissions", "status": "passed", "title": "should check resource-specific permissions", "duration": 0.5198960000016086, "failureMessages": [], "location": {"line": 348, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Dynamic Permission Checking"], "fullName": "Role-Based Access Control Tests (TEST-007) Dynamic Permission Checking should handle hierarchical permissions", "status": "passed", "title": "should handle hierarchical permissions", "duration": 0.5860469999970519, "failureMessages": [], "location": {"line": 363, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Role Inheritance"], "fullName": "Role-Based Access Control Tests (TEST-007) Role Inheritance should handle role inheritance correctly", "status": "passed", "title": "should handle role inheritance correctly", "duration": 0.4577050000007148, "failureMessages": [], "location": {"line": 382, "column": 7}, "meta": {}}], "startTime": 1752960766705, "endTime": 1752960766737.4578, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/integration/rbac.test.js"}, {"assertionResults": [{"ancestorTitles": ["Token Refresh Mechanism Tests (TEST-006)", "Automatic Token Refresh"], "fullName": "Token Refresh Mechanism Tests (TEST-006) Automatic Token Refresh should refresh token automatically before expiration", "status": "passed", "title": "should refresh token automatically before expiration", "duration": 152.62848300000041, "failureMessages": [], "location": {"line": 55, "column": 7}, "meta": {}}, {"ancestorTitles": ["Token Refresh Mechanism Tests (TEST-006)", "Automatic Token Refresh"], "fullName": "Token Refresh Mechanism Tests (TEST-006) Automatic Token Refresh should handle token refresh failure by logging out", "status": "passed", "title": "should handle token refresh failure by logging out", "duration": 6.7098039999982575, "failureMessages": [], "location": {"line": 85, "column": 7}, "meta": {}}, {"ancestorTitles": ["Token Refresh Mechanism Tests (TEST-006)", "Automatic Token Refresh"], "fullName": "Token Refresh Mechanism Tests (TEST-006) Automatic Token Refresh should handle network errors during token refresh", "status": "passed", "title": "should handle network errors during token refresh", "duration": 3.1758410000002186, "failureMessages": [], "location": {"line": 112, "column": 7}, "meta": {}}, {"ancestorTitles": ["Token Refresh Mechanism Tests (TEST-006)", "Token Refresh Timing"], "fullName": "Token Refresh Mechanism Tests (TEST-006) Token Refresh Timing should schedule token refresh based on token expiration", "status": "passed", "title": "should schedule token refresh based on token expiration", "duration": 85.57728099999804, "failureMessages": [], "location": {"line": 131, "column": 7}, "meta": {}}, {"ancestorTitles": ["Token Refresh Mechanism Tests (TEST-006)", "Token Refresh Timing"], "fullName": "Token Refresh Mechanism Tests (TEST-006) Token Refresh Timing should refresh immediately if token is already expired", "status": "passed", "title": "should refresh immediately if token is already expired", "duration": 4.297477000000072, "failureMessages": [], "location": {"line": 158, "column": 7}, "meta": {}}, {"ancestorTitles": ["Token Refresh Mechanism Tests (TEST-006)", "Token Refresh During API Calls"], "fullName": "Token Refresh Mechanism Tests (TEST-006) Token Refresh During API Calls should refresh token when API returns 401 Unauthorized", "status": "passed", "title": "should refresh token when API returns 401 Unauthorized", "duration": 4.49546200000259, "failureMessages": [], "location": {"line": 184, "column": 7}, "meta": {}}, {"ancestorTitles": ["Token Refresh Mechanism Tests (TEST-006)", "Token Refresh During API Calls"], "fullName": "Token Refresh Mechanism Tests (TEST-006) Token Refresh During API Calls should logout user if token refresh fails during API call", "status": "passed", "title": "should logout user if token refresh fails during API call", "duration": 34.16046500000084, "failureMessages": [], "location": {"line": 251, "column": 7}, "meta": {}}, {"ancestorTitles": ["Token Refresh Mechanism Tests (TEST-006)", "Concurrent Token Refresh"], "fullName": "Token Refresh Mechanism Tests (TEST-006) Concurrent Token Refresh should handle multiple simultaneous refresh requests", "status": "passed", "title": "should handle multiple simultaneous refresh requests", "duration": 2.4593800000002375, "failureMessages": [], "location": {"line": 303, "column": 7}, "meta": {}}, {"ancestorTitles": ["Token Refresh Mechanism Tests (TEST-006)", "Token Refresh State Management"], "fullName": "Token Refresh Mechanism Tests (TEST-006) Token Refresh State Management should set loading state during token refresh", "status": "passed", "title": "should set loading state during token refresh", "duration": 7.233206000000791, "failureMessages": [], "location": {"line": 339, "column": 7}, "meta": {}}, {"ancestorTitles": ["Token Refresh Mechanism Tests (TEST-006)", "Token Refresh State Management"], "fullName": "Token Refresh Mechanism Tests (TEST-006) Token Refresh State Management should clear error state on successful token refresh", "status": "passed", "title": "should clear error state on successful token refresh", "duration": 1.4656749999994645, "failureMessages": [], "location": {"line": 364, "column": 7}, "meta": {}}], "startTime": 1752960766837, "endTime": 1752960767141.4656, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/integration/token-refresh.test.js"}, {"assertionResults": [{"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Login API"], "fullName": "Authentication API Middleware (TEST-004) Login API should call PocketBase login with correct parameters", "status": "passed", "title": "should call PocketBase login with correct parameters", "duration": 8.135673999999199, "failureMessages": [], "location": {"line": 73, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Login API"], "fullName": "Authentication API Middleware (TEST-004) Login API should handle login API errors", "status": "passed", "title": "should handle login API errors", "duration": 3.277830000000904, "failureMessages": [], "location": {"line": 88, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Login API"], "fullName": "Authentication API Middleware (TEST-004) Login API should validate input parameters", "status": "passed", "title": "should validate input parameters", "duration": 0.8547159999998257, "failureMessages": [], "location": {"line": 95, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Registration API"], "fullName": "Authentication API Middleware (TEST-004) Registration API should call PocketBase register with correct parameters", "status": "passed", "title": "should call PocketBase register with correct parameters", "duration": 1.5001240000001417, "failureMessages": [], "location": {"line": 105, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Registration API"], "fullName": "Authentication API Middleware (TEST-004) Registration API should handle registration API errors", "status": "passed", "title": "should handle registration API errors", "duration": 1.1460230000011506, "failureMessages": [], "location": {"line": 124, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Registration API"], "fullName": "Authentication API Middleware (TEST-004) Registration API should validate registration data", "status": "passed", "title": "should validate registration data", "duration": 0.9724850000002334, "failureMessages": [], "location": {"line": 132, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Token Refresh API"], "fullName": "Authentication API Middleware (TEST-004) Token Refresh API should call PocketBase refreshToken", "status": "passed", "title": "should call PocketBase refreshToken", "duration": 0.783584000000701, "failureMessages": [], "location": {"line": 140, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Token Refresh API"], "fullName": "Authentication API Middleware (TEST-004) Token Refresh API should handle token refresh API errors", "status": "passed", "title": "should handle token refresh API errors", "duration": 0.570622999999614, "failureMessages": [], "location": {"line": 155, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Token Refresh API"], "fullName": "Authentication API Middleware (TEST-004) Token Refresh API should handle expired refresh tokens", "status": "passed", "title": "should handle expired refresh tokens", "duration": 0.8366150000001653, "failureMessages": [], "location": {"line": 162, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Password Reset API"], "fullName": "Authentication API Middleware (TEST-004) Password Reset API should call PocketBase requestPasswordReset", "status": "passed", "title": "should call PocketBase requestPasswordReset", "duration": 0.7233059999998659, "failureMessages": [], "location": {"line": 174, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Password Reset API"], "fullName": "Authentication API Middleware (TEST-004) Password Reset API should call PocketBase resetPassword", "status": "passed", "title": "should call PocketBase resetPassword", "duration": 0.754371999999421, "failureMessages": [], "location": {"line": 184, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Profile Management API"], "fullName": "Authentication API Middleware (TEST-004) Profile Management API should call PocketBase updateProfile", "status": "passed", "title": "should call PocketBase updateProfile", "duration": 0.6005809999987832, "failureMessages": [], "location": {"line": 199, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Profile Management API"], "fullName": "Authentication API Middleware (TEST-004) Profile Management API should call PocketBase changePassword", "status": "passed", "title": "should call PocketBase changePassword", "duration": 0.5166709999994055, "failureMessages": [], "location": {"line": 213, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Authentication Status API"], "fullName": "Authentication API Middleware (TEST-004) Authentication Status API should call PocketBase isAuthenticated", "status": "passed", "title": "should call PocketBase isAuthenticated", "duration": 0.35712099999909697, "failureMessages": [], "location": {"line": 228, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Authentication Status API"], "fullName": "Authentication API Middleware (TEST-004) Authentication Status API should call PocketBase getCurrentUser", "status": "passed", "title": "should call PocketBase getCurrentUser", "duration": 0.42378000000098837, "failureMessages": [], "location": {"line": 237, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Authentication Status API"], "fullName": "Authentication API Middleware (TEST-004) Authentication Status API should call PocketBase getToken", "status": "passed", "title": "should call PocketBase getToken", "duration": 0.33643600000141305, "failureMessages": [], "location": {"line": 250, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Authentication Status API"], "fullName": "Authentication API Middleware (TEST-004) Authentication Status API should handle authentication check errors", "status": "passed", "title": "should handle authentication check errors", "duration": 0.5033199999998033, "failureMessages": [], "location": {"line": 260, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Logout API"], "fullName": "Authentication API Middleware (TEST-004) Logout API should call PocketBase logout", "status": "passed", "title": "should call PocketBase logout", "duration": 0.39763999999922817, "failureMessages": [], "location": {"line": 268, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Logout API"], "fullName": "Authentication API Middleware (TEST-004) Logout API should handle logout API errors", "status": "passed", "title": "should handle logout API errors", "duration": 0.44444099999964237, "failureMessages": [], "location": {"line": 278, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Logout API"], "fullName": "Authentication API Middleware (TEST-004) Logout API should handle logout when not authenticated", "status": "passed", "title": "should handle logout when not authenticated", "duration": 0.2684369999988121, "failureMessages": [], "location": {"line": 285, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "API Response Handling"], "fullName": "Authentication API Middleware (TEST-004) API Response Handling should handle network timeouts", "status": "passed", "title": "should handle network timeouts", "duration": 103.19129099999918, "failureMessages": [], "location": {"line": 298, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "API Response Handling"], "fullName": "Authentication API Middleware (TEST-004) API Response Handling should handle rate limiting", "status": "passed", "title": "should handle rate limiting", "duration": 2.103968999999779, "failureMessages": [], "location": {"line": 308, "column": 7}, "meta": {}}], "startTime": 1752960756912, "endTime": 1752960757042.104, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/unit/middleware/auth-middleware.test.js"}, {"assertionResults": [{"ancestorTitles": ["CSRF Middleware", "generateCSRFToken"], "fullName": "CSRF Middleware generateCSRFToken should generate and set CSRF token in cookie and header", "status": "passed", "title": "should generate and set CSRF token in cookie and header", "duration": 291.4680580000004, "failureMessages": [], "location": {"line": 38, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "generateCSRFToken"], "fullName": "CSRF Middleware generateCSRFToken should set secure cookie in production", "status": "passed", "title": "should set secure cookie in production", "duration": 163.5668969999988, "failureMessages": [], "location": {"line": 53, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "validateCSRFToken"], "fullName": "CSRF Middleware validateCSRFToken should skip validation for GET requests", "status": "passed", "title": "should skip validation for GET requests", "duration": 10.319127999999182, "failureMessages": [], "location": {"line": 70, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "validateCSRFToken"], "fullName": "CSRF Middleware validateCSRFToken should skip validation for HEAD requests", "status": "passed", "title": "should skip validation for HEAD requests", "duration": 7.204124999998385, "failureMessages": [], "location": {"line": 79, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "validateCSRFToken"], "fullName": "CSRF Middleware validateCSRFToken should skip validation for OPTIONS requests", "status": "passed", "title": "should skip validation for OPTIONS requests", "duration": 8.744268000000375, "failureMessages": [], "location": {"line": 88, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "csrfProtection"], "fullName": "CSRF Middleware csrfProtection should protect authentication endpoints", "status": "passed", "title": "should protect authentication endpoints", "duration": 17.382732000000033, "failureMessages": [], "location": {"line": 105, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "csrfProtection"], "fullName": "CSRF Middleware csrfProtection should protect registration endpoints", "status": "passed", "title": "should protect registration endpoints", "duration": 6.06009400000039, "failureMessages": [], "location": {"line": 114, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "csrfProtection"], "fullName": "CSRF Middleware csrfProtection should not protect non-authentication endpoints", "status": "passed", "title": "should not protect non-authentication endpoints", "duration": 6.238037000000986, "failureMessages": [], "location": {"line": 123, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "csrfProtection"], "fullName": "CSRF Middleware csrfProtection should not protect GET requests to auth endpoints", "status": "passed", "title": "should not protect GET requests to auth endpoints", "duration": 30.034390999999232, "failureMessages": [], "location": {"line": 132, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "getCSRFToken"], "fullName": "CSRF Middleware getCSRFToken should return null for non-existent session", "status": "passed", "title": "should return null for non-existent session", "duration": 11.285307000000103, "failureMessages": [], "location": {"line": 143, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "cleanupExpiredTokens"], "fullName": "CSRF Middleware cleanupExpiredTokens should clean up expired tokens", "status": "passed", "title": "should clean up expired tokens", "duration": 8.091510999998718, "failureMessages": [], "location": {"line": 151, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "CSRF token endpoint"], "fullName": "CSRF Middleware CSRF token endpoint should provide CSRF token via API endpoint", "status": "passed", "title": "should provide CSRF token via API endpoint", "duration": 11.998620000000301, "failureMessages": [], "location": {"line": 159, "column": 5}, "meta": {}}], "startTime": 1752960759829, "endTime": 1752960760401.9985, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/unit/middleware/csrf.test.js"}, {"assertionResults": [{"ancestorTitles": ["Password Reset Middleware", "storeResetToken"], "fullName": "Password Reset Middleware storeResetToken should store reset token with expiration", "status": "passed", "title": "should store reset token with expiration", "duration": 20.61953700000049, "failureMessages": [], "location": {"line": 39, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "storeResetToken"], "fullName": "Password Reset Middleware storeResetToken should use default expiration time", "status": "passed", "title": "should use default expiration time", "duration": 1.2286729999996169, "failureMessages": [], "location": {"line": 55, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "validateResetToken"], "fullName": "Password Reset Middleware validateResetToken should validate valid token", "status": "passed", "title": "should validate valid token", "duration": 93.79193200000009, "failureMessages": [], "location": {"line": 66, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "validateResetToken"], "fullName": "Password Reset Middleware validateResetToken should reject non-existent token", "status": "passed", "title": "should reject non-existent token", "duration": 1.668333999999959, "failureMessages": [], "location": {"line": 78, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "validateResetToken"], "fullName": "Password Reset Middleware validateResetToken should reject expired token", "status": "passed", "title": "should reject expired token", "duration": 0.9495490000008431, "failureMessages": [], "location": {"line": 85, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "validateResetToken"], "fullName": "Password Reset Middleware validateResetToken should reject used token", "status": "passed", "title": "should reject used token", "duration": 8.709002999999939, "failureMessages": [], "location": {"line": 100, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "validateResetToken"], "fullName": "Password Reset Middleware validateResetToken should increment attempt counter", "status": "passed", "title": "should increment attempt counter", "duration": 1.377386000000115, "failureMessages": [], "location": {"line": 113, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "validateResetToken"], "fullName": "Password Reset Middleware validateResetToken should reject token after too many attempts", "status": "passed", "title": "should reject token after too many attempts", "duration": 1.9559230000013486, "failureMessages": [], "location": {"line": 126, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "markTokenAsUsed"], "fullName": "Password Reset Middleware markTokenAsUsed should mark token as used", "status": "passed", "title": "should mark token as used", "duration": 20.01167400000122, "failureMessages": [], "location": {"line": 146, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "markTokenAsUsed"], "fullName": "Password Reset Middleware markTokenAsUsed should handle non-existent token gracefully", "status": "passed", "title": "should handle non-existent token gracefully", "duration": 9.701035000000047, "failureMessages": [], "location": {"line": 157, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "invalidateEmailResetTokens"], "fullName": "Password Reset Middleware invalidateEmailResetTokens should invalidate existing tokens for email", "status": "passed", "title": "should invalidate existing tokens for email", "duration": 1.1415730000007898, "failureMessages": [], "location": {"line": 163, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "passwordResetRateLimit"], "fullName": "Password Reset Middleware passwordResetRateLimit should allow requests within rate limit", "status": "passed", "title": "should allow requests within rate limit", "duration": 452.9905370000015, "failureMessages": [], "location": {"line": 185, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "passwordResetRateLimit"], "fullName": "Password Reset Middleware passwordResetRateLimit should block requests exceeding rate limit", "status": "passed", "title": "should block requests exceeding rate limit", "duration": 51.75602599999911, "failureMessages": [], "location": {"line": 198, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "emailResetRateLimit"], "fullName": "Password Reset Middleware emailResetRateLimit should allow requests for different emails", "status": "passed", "title": "should allow requests for different emails", "duration": 17.096677999999883, "failureMessages": [], "location": {"line": 228, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "emailResetRateLimit"], "fullName": "Password Reset Middleware emailResetRateLimit should block excessive requests for same email", "status": "passed", "title": "should block excessive requests for same email", "duration": 104.90792300000066, "failureMessages": [], "location": {"line": 246, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "validateResetTokenMiddleware"], "fullName": "Password Reset Middleware validateResetTokenMiddleware should validate token and attach data to request", "status": "passed", "title": "should validate token and attach data to request", "duration": 11.54754099999991, "failureMessages": [], "location": {"line": 277, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "validateResetTokenMiddleware"], "fullName": "Password Reset Middleware validateResetTokenMiddleware should reject request with invalid token", "status": "passed", "title": "should reject request with invalid token", "duration": 78.36252500000046, "failureMessages": [], "location": {"line": 301, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "validateResetTokenMiddleware"], "fullName": "Password Reset Middleware validateResetTokenMiddleware should reject request without token", "status": "passed", "title": "should reject request without token", "duration": 39.74300300000141, "failureMessages": [], "location": {"line": 313, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "progressiveDelayMiddleware"], "fullName": "Password Reset Middleware progressiveDelayMiddleware should not delay first request", "status": "passed", "title": "should not delay first request", "duration": 11.173386999998911, "failureMessages": [], "location": {"line": 327, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "progressiveDelayMiddleware"], "fullName": "Password Reset Middleware progressiveDelayMiddleware should handle missing email gracefully", "status": "passed", "title": "should handle missing email gracefully", "duration": 13.415256000000227, "failureMessages": [], "location": {"line": 342, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "cleanupExpiredData"], "fullName": "Password Reset Middleware cleanupExpiredData should clean up expired tokens", "status": "passed", "title": "should clean up expired tokens", "duration": 0.49244700000053854, "failureMessages": [], "location": {"line": 356, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "cleanupExpiredData"], "fullName": "Password Reset Middleware cleanupExpiredData should not throw errors during cleanup", "status": "passed", "title": "should not throw errors during cleanup", "duration": 0.8581730000005336, "failureMessages": [], "location": {"line": 373, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "getResetTokenStats"], "fullName": "Password Reset Middleware getResetTokenStats should return correct statistics", "status": "passed", "title": "should return correct statistics", "duration": 56.836191000002145, "failureMessages": [], "location": {"line": 379, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "Integration tests"], "fullName": "Password Reset Middleware Integration tests should handle complete password reset flow", "status": "passed", "title": "should handle complete password reset flow", "duration": 24.816697999998723, "failureMessages": [], "location": {"line": 404, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "Integration tests"], "fullName": "Password Reset Middleware Integration tests should handle token expiration correctly", "status": "passed", "title": "should handle token expiration correctly", "duration": 9.833029000001261, "failureMessages": [], "location": {"line": 424, "column": 5}, "meta": {}}], "startTime": 1752960759886, "endTime": 1752960760923.833, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/unit/middleware/passwordReset.test.js"}, {"assertionResults": [{"ancestorTitles": ["Route Guards (TEST-003)", "Authentication Guard"], "fullName": "Route Guards (TEST-003) Authentication Guard should initialize auth store if not initialized", "status": "passed", "title": "should initialize auth store if not initialized", "duration": 10.15161700000317, "failureMessages": [], "location": {"line": 106, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Authentication Guard"], "fullName": "Route Guards (TEST-003) Authentication Guard should not initialize auth store if already initialized", "status": "passed", "title": "should not initialize auth store if already initialized", "duration": 1.3022229999987758, "failureMessages": [], "location": {"line": 120, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Protected Routes"], "fullName": "Route Guards (TEST-003) Protected Routes should allow access to protected routes when authenticated", "status": "passed", "title": "should allow access to protected routes when authenticated", "duration": 1.0259829999995418, "failureMessages": [], "location": {"line": 135, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Protected Routes"], "fullName": "Route Guards (TEST-003) Protected Routes should redirect to login when accessing protected routes while unauthenticated", "status": "passed", "title": "should redirect to login when accessing protected routes while unauthenticated", "duration": 4.383448000000499, "failureMessages": [], "location": {"line": 148, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Protected Routes"], "fullName": "Route Guards (TEST-003) Protected Routes should preserve query parameters in redirect", "status": "passed", "title": "should preserve query parameters in redirect", "duration": 1.9337570000025153, "failureMessages": [], "location": {"line": 164, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Guest-Only Routes"], "fullName": "Route Guards (TEST-003) Guest-Only Routes should allow access to guest-only routes when unauthenticated", "status": "passed", "title": "should allow access to guest-only routes when unauthenticated", "duration": 0.6368029999975988, "failureMessages": [], "location": {"line": 183, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Guest-Only Routes"], "fullName": "Route Guards (TEST-003) Guest-Only Routes should redirect to home when accessing guest-only routes while authenticated", "status": "passed", "title": "should redirect to home when accessing guest-only routes while authenticated", "duration": 0.8287720000007539, "failureMessages": [], "location": {"line": 196, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Guest-Only Routes"], "fullName": "Route Guards (TEST-003) Guest-Only Routes should redirect authenticated users from register page", "status": "passed", "title": "should redirect authenticated users from register page", "duration": 3.2304949999997916, "failureMessages": [], "location": {"line": 209, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Guest-Only Routes"], "fullName": "Route Guards (TEST-003) Guest-Only Routes should redirect authenticated users from forgot-password page", "status": "passed", "title": "should redirect authenticated users from forgot-password page", "duration": 2.492923999998311, "failureMessages": [], "location": {"line": 222, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Admin Routes"], "fullName": "Route Guards (TEST-003) Admin Routes should allow access to admin routes when user is admin", "status": "passed", "title": "should allow access to admin routes when user is admin", "duration": 0.8672440000009374, "failureMessages": [], "location": {"line": 237, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Admin Routes"], "fullName": "Route Guards (TEST-003) Admin Routes should redirect to unauthorized when non-admin user accesses admin routes", "status": "passed", "title": "should redirect to unauthorized when non-admin user accesses admin routes", "duration": 0.5825460000014573, "failureMessages": [], "location": {"line": 251, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Admin Routes"], "fullName": "Route Guards (TEST-003) Admin Routes should redirect to login when unauthenticated user accesses admin routes", "status": "passed", "title": "should redirect to login when unauthenticated user accesses admin routes", "duration": 0.6893099999979313, "failureMessages": [], "location": {"line": 265, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Public Routes"], "fullName": "Route Guards (TEST-003) Public Routes should allow access to public routes regardless of authentication status", "status": "passed", "title": "should allow access to public routes regardless of authentication status", "duration": 0.9964399999989837, "failureMessages": [], "location": {"line": 284, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Public Routes"], "fullName": "Route Guards (TEST-003) Public Routes should allow authenticated users to access public routes", "status": "passed", "title": "should allow authenticated users to access public routes", "duration": 1.356748999998672, "failureMessages": [], "location": {"line": 297, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Erro<PERSON>"], "fullName": "Route Guards (TEST-003) Error Handling should handle auth initialization errors gracefully", "status": "passed", "title": "should handle auth initialization errors gracefully", "duration": 1.6627490000028047, "failureMessages": [], "location": {"line": 312, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Route Meta Combinations"], "fullName": "Route Guards (TEST-003) Route Meta Combinations should handle routes with multiple meta requirements", "status": "passed", "title": "should handle routes with multiple meta requirements", "duration": 2.7530619999997725, "failureMessages": [], "location": {"line": 329, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Route Meta Combinations"], "fullName": "Route Guards (TEST-003) Route Meta Combinations should prioritize authentication check over admin check", "status": "passed", "title": "should prioritize authentication check over admin check", "duration": 1.4169000000001688, "failureMessages": [], "location": {"line": 346, "column": 7}, "meta": {}}], "startTime": 1752960771125, "endTime": 1752960771164.417, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/unit/router/guards.test.js"}, {"assertionResults": [{"ancestorTitles": ["Authentication Routes", "POST /api/auth/login"], "fullName": "Authentication Routes POST /api/auth/login should login user with valid credentials", "status": "passed", "title": "should login user with valid credentials", "duration": 560.5536040000006, "failureMessages": [], "location": {"line": 201, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/login"], "fullName": "Authentication Routes POST /api/auth/login should return validation error for invalid email", "status": "passed", "title": "should return validation error for invalid email", "duration": 13.99093399999947, "failureMessages": [], "location": {"line": 232, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/login"], "fullName": "Authentication Routes POST /api/auth/login should return validation error for missing password", "status": "passed", "title": "should return validation error for missing password", "duration": 22.56885399999919, "failureMessages": [], "location": {"line": 245, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/register"], "fullName": "Authentication Routes POST /api/auth/register should register user with valid data", "status": "passed", "title": "should register user with valid data", "duration": 37.86080899999979, "failureMessages": [], "location": {"line": 259, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/register"], "fullName": "Authentication Routes POST /api/auth/register should return validation error for weak password", "status": "passed", "title": "should return validation error for weak password", "duration": 17.400668000000223, "failureMessages": [], "location": {"line": 293, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/register"], "fullName": "Authentication Routes POST /api/auth/register should return validation error for password mismatch", "status": "passed", "title": "should return validation error for password mismatch", "duration": 15.35791700000118, "failureMessages": [], "location": {"line": 308, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/logout"], "fullName": "Authentication Routes POST /api/auth/logout should logout user successfully", "status": "passed", "title": "should logout user successfully", "duration": 12.269962000000305, "failureMessages": [], "location": {"line": 325, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/refresh"], "fullName": "Authentication Routes POST /api/auth/refresh should refresh token successfully", "status": "passed", "title": "should refresh token successfully", "duration": 8.122596000001067, "failureMessages": [], "location": {"line": 343, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/refresh"], "fullName": "Authentication Routes POST /api/auth/refresh should return validation error for missing refresh token", "status": "passed", "title": "should return validation error for missing refresh token", "duration": 47.54523700000027, "failureMessages": [], "location": {"line": 363, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/forgot-password"], "fullName": "Authentication Routes POST /api/auth/forgot-password should send password reset email", "status": "passed", "title": "should send password reset email", "duration": 27.382634999999937, "failureMessages": [], "location": {"line": 375, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/forgot-password"], "fullName": "Authentication Routes POST /api/auth/forgot-password should return validation error for invalid email", "status": "passed", "title": "should return validation error for invalid email", "duration": 76.10256899999877, "failureMessages": [], "location": {"line": 393, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/reset-password"], "fullName": "Authentication Routes POST /api/auth/reset-password should reset password successfully", "status": "passed", "title": "should reset password successfully", "duration": 8.088521000001492, "failureMessages": [], "location": {"line": 407, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/reset-password"], "fullName": "Authentication Routes POST /api/auth/reset-password should return validation error for missing token", "status": "passed", "title": "should return validation error for missing token", "duration": 5.002628000002005, "failureMessages": [], "location": {"line": 427, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "GET /api/auth/me"], "fullName": "Authentication Routes GET /api/auth/me should get current user profile", "status": "passed", "title": "should get current user profile", "duration": 8.743589999998221, "failureMessages": [], "location": {"line": 442, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "PUT /api/auth/profile"], "fullName": "Authentication Routes PUT /api/auth/profile should update user profile", "status": "passed", "title": "should update user profile", "duration": 5.821718999999575, "failureMessages": [], "location": {"line": 467, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "PUT /api/auth/profile"], "fullName": "Authentication Routes PUT /api/auth/profile should return validation error for invalid email", "status": "passed", "title": "should return validation error for invalid email", "duration": 8.349511000000348, "failureMessages": [], "location": {"line": 499, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "PUT /api/auth/password"], "fullName": "Authentication Routes PUT /api/auth/password should change password successfully", "status": "passed", "title": "should change password successfully", "duration": 5.6596950000021025, "failureMessages": [], "location": {"line": 514, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "PUT /api/auth/password"], "fullName": "Authentication Routes PUT /api/auth/password should return validation error for password mismatch", "status": "passed", "title": "should return validation error for password mismatch", "duration": 22.120732000003045, "failureMessages": [], "location": {"line": 540, "column": 5}, "meta": {}}], "startTime": 1752960759949, "endTime": 1752960760855.1208, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/unit/routes/auth.test.js"}, {"assertionResults": [{"ancestorTitles": ["Authentication Composable (TEST-002)"], "fullName": "Authentication Composable (TEST-002) should be properly configured", "status": "passed", "title": "should be properly configured", "duration": 7.193665999999212, "failureMessages": [], "location": {"line": 149, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "State Access"], "fullName": "Authentication Composable (TEST-002) State Access should provide access to authentication state", "status": "passed", "title": "should provide access to authentication state", "duration": 4.203551999999036, "failureMessages": [], "location": {"line": 154, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "State Access"], "fullName": "Authentication Composable (TEST-002) State Access should reflect changes in authentication state", "status": "passed", "title": "should reflect changes in authentication state", "duration": 2.8728439999977127, "failureMessages": [], "location": {"line": 164, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Login Function"], "fullName": "Authentication Composable (TEST-002) Login Function should login and redirect on success", "status": "passed", "title": "should login and redirect on success", "duration": 6.101961000000301, "failureMessages": [], "location": {"line": 178, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Login Function"], "fullName": "Authentication Composable (TEST-002) Login Function should not redirect on login failure", "status": "passed", "title": "should not redirect on login failure", "duration": 2.2243930000004184, "failureMessages": [], "location": {"line": 191, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Register Function"], "fullName": "Authentication Composable (TEST-002) Register Function should register and redirect to login on success", "status": "passed", "title": "should register and redirect to login on success", "duration": 0.932450999996945, "failureMessages": [], "location": {"line": 206, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Register Function"], "fullName": "Authentication Composable (TEST-002) Register Function should not redirect on registration failure", "status": "passed", "title": "should not redirect on registration failure", "duration": 1.2845449999986158, "failureMessages": [], "location": {"line": 219, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Logout Function"], "fullName": "Authentication Composable (TEST-002) Logout Function should logout and redirect to login", "status": "passed", "title": "should logout and redirect to login", "duration": 1.5187650000007125, "failureMessages": [], "location": {"line": 233, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Token Refresh Function"], "fullName": "Authentication Composable (TEST-002) Token Refresh Function should refresh token", "status": "passed", "title": "should refresh token", "duration": 1.0478459999976621, "failureMessages": [], "location": {"line": 246, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Password Reset Functions"], "fullName": "Authentication Composable (TEST-002) Password Reset Functions should request password reset", "status": "passed", "title": "should request password reset", "duration": 0.7910270000029413, "failureMessages": [], "location": {"line": 259, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Password Reset Functions"], "fullName": "Authentication Composable (TEST-002) Password Reset Functions should reset password and redirect to login on success", "status": "passed", "title": "should reset password and redirect to login on success", "duration": 0.8991679999999178, "failureMessages": [], "location": {"line": 271, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Profile Management Functions"], "fullName": "Authentication Composable (TEST-002) Profile Management Functions should update profile", "status": "passed", "title": "should update profile", "duration": 0.8889479999998002, "failureMessages": [], "location": {"line": 286, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Profile Management Functions"], "fullName": "Authentication Composable (TEST-002) Profile Management Functions should change password", "status": "passed", "title": "should change password", "duration": 0.560790999999881, "failureMessages": [], "location": {"line": 298, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Route Guards"], "fullName": "Authentication Composable (TEST-002) Route Guards should allow access when authenticated (requireAuth)", "status": "passed", "title": "should allow access when authenticated (requireAuth)", "duration": 0.4738809999980731, "failureMessages": [], "location": {"line": 312, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Route Guards"], "fullName": "Authentication Composable (TEST-002) Route Guards should redirect to login when not authenticated (requireAuth)", "status": "passed", "title": "should redirect to login when not authenticated (requireAuth)", "duration": 0.43601099999796133, "failureMessages": [], "location": {"line": 322, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Route Guards"], "fullName": "Authentication Composable (TEST-002) Route Guards should allow access when not authenticated (requireGuest)", "status": "passed", "title": "should allow access when not authenticated (requireGuest)", "duration": 0.3751959999972314, "failureMessages": [], "location": {"line": 332, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Route Guards"], "fullName": "Authentication Composable (TEST-002) Route Guards should redirect to home when authenticated (requireGuest)", "status": "passed", "title": "should redirect to home when authenticated (requireGuest)", "duration": 0.45882499999788706, "failureMessages": [], "location": {"line": 342, "column": 7}, "meta": {}}], "startTime": 1752960766508, "endTime": 1752960766541.4587, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/unit/composables/useAuth.test.js"}, {"assertionResults": [{"ancestorTitles": ["useSessionManagement", "initialization"], "fullName": "useSessionManagement initialization should initialize with empty sessions", "status": "passed", "title": "should initialize with empty sessions", "duration": 78.57888000000094, "failureMessages": [], "location": {"line": 63, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "fetchSessions"], "fullName": "useSessionManagement fetchSessions should fetch sessions successfully", "status": "passed", "title": "should fetch sessions successfully", "duration": 35.48322199999893, "failureMessages": [], "location": {"line": 81, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "fetchSessions"], "fullName": "useSessionManagement fetchSessions should handle fetch errors", "status": "passed", "title": "should handle fetch errors", "duration": 4.074306999998953, "failureMessages": [], "location": {"line": 142, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "fetchSessions"], "fullName": "useSessionManagement fetchSessions should handle API errors", "status": "passed", "title": "should handle API errors", "duration": 3.041284999999334, "failureMessages": [], "location": {"line": 163, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "fetchSessions"], "fullName": "useSessionManagement fetchSessions should handle API response errors", "status": "passed", "title": "should handle API response errors", "duration": 39.85652400000254, "failureMessages": [], "location": {"line": 186, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "fetchSessions"], "fullName": "useSessionManagement fetchSessions should not fetch when unauthenticated", "status": "passed", "title": "should not fetch when unauthenticated", "duration": 9.776778999999806, "failureMessages": [], "location": {"line": 211, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "terminateSession"], "fullName": "useSessionManagement terminateSession should terminate session successfully", "status": "passed", "title": "should terminate session successfully", "duration": 5.777759000000515, "failureMessages": [], "location": {"line": 225, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "terminateSession"], "fullName": "useSessionManagement terminateSession should handle termination errors", "status": "passed", "title": "should handle termination errors", "duration": 3.6029139999991457, "failureMessages": [], "location": {"line": 289, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "terminateSession"], "fullName": "useSessionManagement terminateSession should handle API termination errors", "status": "passed", "title": "should handle API termination errors", "duration": 18.284083999998984, "failureMessages": [], "location": {"line": 306, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "terminateAllOtherSessions"], "fullName": "useSessionManagement terminateAllOtherSessions should terminate all other sessions successfully", "status": "passed", "title": "should terminate all other sessions successfully", "duration": 6.20102400000178, "failureMessages": [], "location": {"line": 329, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "terminateAllOtherSessions"], "fullName": "useSessionManagement terminateAllOtherSessions should handle termination errors", "status": "passed", "title": "should handle termination errors", "duration": 1.8971519999977318, "failureMessages": [], "location": {"line": 381, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "refreshCurrentSession"], "fullName": "useSessionManagement refreshCurrentSession should refresh current session successfully", "status": "passed", "title": "should refresh current session successfully", "duration": 4.047460999998293, "failureMessages": [], "location": {"line": 400, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "refreshCurrentSession"], "fullName": "useSessionManagement refreshCurrentSession should handle refresh errors", "status": "passed", "title": "should handle refresh errors", "duration": 1.3827750000018568, "failureMessages": [], "location": {"line": 431, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "computed properties"], "fullName": "useSessionManagement computed properties should compute active sessions correctly", "status": "passed", "title": "should compute active sessions correctly", "duration": 2.70289099999718, "failureMessages": [], "location": {"line": 450, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "computed properties"], "fullName": "useSessionManagement computed properties should identify current session correctly", "status": "passed", "title": "should identify current session correctly", "duration": 2.892508999997517, "failureMessages": [], "location": {"line": 482, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "computed properties"], "fullName": "useSessionManagement computed properties should compute other sessions correctly", "status": "passed", "title": "should compute other sessions correctly", "duration": 5.794221000000107, "failureMessages": [], "location": {"line": 515, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "computed properties"], "fullName": "useSessionManagement computed properties should compute session statistics correctly", "status": "passed", "title": "should compute session statistics correctly", "duration": 1.3806790000016917, "failureMessages": [], "location": {"line": 548, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "utility functions"], "fullName": "useSessionManagement utility functions should detect device type correctly", "status": "passed", "title": "should detect device type correctly", "duration": 2.6886199999971723, "failureMessages": [], "location": {"line": 587, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "utility functions"], "fullName": "useSessionManagement utility functions should detect browser name correctly", "status": "passed", "title": "should detect browser name correctly", "duration": 1.3951530000013008, "failureMessages": [], "location": {"line": 604, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "CSRF token handling"], "fullName": "useSessionManagement CSRF token handling should get CSRF token successfully", "status": "passed", "title": "should get CSRF token successfully", "duration": 12.904388999999355, "failureMessages": [], "location": {"line": 623, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "CSRF token handling"], "fullName": "useSessionManagement CSRF token handling should handle CSRF token errors gracefully", "status": "passed", "title": "should handle CSRF token errors gracefully", "duration": 1.688227000002371, "failureMessages": [], "location": {"line": 652, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "auto refresh"], "fullName": "useSessionManagement auto refresh should start auto refresh", "status": "passed", "title": "should start auto refresh", "duration": 2.505239000001893, "failureMessages": [], "location": {"line": 673, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "auto refresh"], "fullName": "useSessionManagement auto refresh should stop auto refresh", "status": "passed", "title": "should stop auto refresh", "duration": 2.2422850000002654, "failureMessages": [], "location": {"line": 682, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "auto refresh"], "fullName": "useSessionManagement auto refresh should refresh sessions automatically", "status": "passed", "title": "should refresh sessions automatically", "duration": 133.0778190000019, "failureMessages": [], "location": {"line": 693, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "error handling"], "fullName": "useSessionManagement error handling should clear errors", "status": "passed", "title": "should clear errors", "duration": 7.199801000002481, "failureMessages": [], "location": {"line": 714, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "new session detection"], "fullName": "useSessionManagement new session detection should detect new sessions", "status": "passed", "title": "should detect new sessions", "duration": 3.291847999997117, "failureMessages": [], "location": {"line": 737, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "new session detection"], "fullName": "useSessionManagement new session detection should handle no new sessions", "status": "passed", "title": "should handle no new sessions", "duration": 3.2489559999994526, "failureMessages": [], "location": {"line": 789, "column": 7}, "meta": {}}], "startTime": 1752960767040, "endTime": 1752960767440.249, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionManagement.test.js"}, {"assertionResults": [{"ancestorTitles": ["useSessionTimeout", "initialization"], "fullName": "useSessionTimeout initialization should initialize with default configuration", "status": "passed", "title": "should initialize with default configuration", "duration": 11.181448999999702, "failureMessages": [], "location": {"line": 97, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "initialization"], "fullName": "useSessionTimeout initialization should accept custom configuration", "status": "passed", "title": "should accept custom configuration", "duration": 1.8972079999984999, "failureMessages": [], "location": {"line": 113, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "activity tracking"], "fullName": "useSessionTimeout activity tracking should register activity event listeners", "status": "passed", "title": "should register activity event listeners", "duration": 4.210285000001022, "failureMessages": [], "location": {"line": 128, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "activity tracking"], "fullName": "useSessionTimeout activity tracking should update activity timestamp on user activity", "status": "passed", "title": "should update activity timestamp on user activity", "duration": 7.0218799999965995, "failureMessages": [], "location": {"line": 141, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "activity tracking"], "fullName": "useSessionTimeout activity tracking should handle localStorage errors gracefully", "status": "passed", "title": "should handle localStorage errors gracefully", "duration": 3.027420999998867, "failureMessages": [], "location": {"line": 158, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "warning system"], "fullName": "useSessionTimeout warning system should show warning when approaching timeout", "status": "passed", "title": "should show warning when approaching timeout", "duration": 2.4699430000000575, "failureMessages": [], "location": {"line": 170, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "warning system"], "fullName": "useSessionTimeout warning system should hide warning when activity is detected", "status": "passed", "title": "should hide warning when activity is detected", "duration": 1.9477780000015628, "failureMessages": [], "location": {"line": 186, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "warning system"], "fullName": "useSessionTimeout warning system should format time remaining correctly", "status": "passed", "title": "should format time remaining correctly", "duration": 1.448566999999457, "failureMessages": [], "location": {"line": 206, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "session extension"], "fullName": "useSessionTimeout session extension should extend session successfully", "status": "passed", "title": "should extend session successfully", "duration": 86.02334100000007, "failureMessages": [], "location": {"line": 226, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "session extension"], "fullName": "useSessionTimeout session extension should handle session extension failure", "status": "passed", "title": "should handle session extension failure", "duration": 2.98029900000256, "failureMessages": [], "location": {"line": 246, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "session extension"], "fullName": "useSessionTimeout session extension should handle network errors during extension", "status": "passed", "title": "should handle network errors during extension", "duration": 1.381493999997474, "failureMessages": [], "location": {"line": 261, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "session timeout handling"], "fullName": "useSessionTimeout session timeout handling should logout user when session times out", "status": "passed", "title": "should logout user when session times out", "duration": 2.150632000000769, "failureMessages": [], "location": {"line": 274, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "session timeout handling"], "fullName": "useSessionTimeout session timeout handling should redirect to login even if logout fails", "status": "passed", "title": "should redirect to login even if logout fails", "duration": 1.1593229999998584, "failureMessages": [], "location": {"line": 284, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "cross-tab communication"], "fullName": "useSessionTimeout cross-tab communication should check for activity in other tabs", "status": "passed", "title": "should check for activity in other tabs", "duration": 17.55189899999823, "failureMessages": [], "location": {"line": 296, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "cross-tab communication"], "fullName": "useSessionTimeout cross-tab communication should handle localStorage errors during cross-tab check", "status": "passed", "title": "should handle localStorage errors during cross-tab check", "duration": 15.724072999997588, "failureMessages": [], "location": {"line": 310, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "cleanup"], "fullName": "useSessionTimeout cleanup should remove event listeners when stopped", "status": "passed", "title": "should remove event listeners when stopped", "duration": 1.770790999999008, "failureMessages": [], "location": {"line": 322, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "cleanup"], "fullName": "useSessionTimeout cleanup should clear timers when stopped", "status": "passed", "title": "should clear timers when stopped", "duration": 1.5718100000012782, "failureMessages": [], "location": {"line": 333, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "session status"], "fullName": "useSessionTimeout session status should provide comprehensive session status", "status": "passed", "title": "should provide comprehensive session status", "duration": 2.433468000002904, "failureMessages": [], "location": {"line": 345, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "edge cases"], "fullName": "useSessionTimeout edge cases should handle unauthenticated state gracefully", "status": "passed", "title": "should handle unauthenticated state gracefully", "duration": 1.4942709999995714, "failureMessages": [], "location": {"line": 370, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "edge cases"], "fullName": "useSessionTimeout edge cases should handle multiple start/stop cycles", "status": "passed", "title": "should handle multiple start/stop cycles", "duration": 1.4917010000026494, "failureMessages": [], "location": {"line": 382, "column": 7}, "meta": {}}], "startTime": 1752960767551, "endTime": 1752960767721.4917, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionTimeout.test.js"}, {"assertionResults": [], "startTime": 1752960745404, "endTime": 1752960745404, "status": "failed", "message": "Cannot access 'mockPocketBase' before initialization", "name": "/Volumes/External Drive/Development/track-tasks/test/unit/services/authService.test.js"}, {"assertionResults": [{"ancestorTitles": ["PocketBase Service", "Initialization"], "fullName": "PocketBase Service Initialization should initialize with default URL when no environment variable is set", "status": "passed", "title": "should initialize with default URL when no environment variable is set", "duration": 6.609846999999718, "failureMessages": [], "location": {"line": 40, "column": 7}, "meta": {}}, {"ancestorTitles": ["PocketBase Service", "Initialization"], "fullName": "PocketBase Service Initialization should load auth state from cookies on initialization", "status": "passed", "title": "should load auth state from cookies on initialization", "duration": 0.7783559999988938, "failureMessages": [], "location": {"line": 45, "column": 7}, "meta": {}}, {"ancestorTitles": ["PocketBase Service", "Authentication Check"], "fullName": "PocketBase Service Authentication Check should return false when user is not authenticated", "status": "passed", "title": "should return false when user is not authenticated", "duration": 0.6174019999998563, "failureMessages": [], "location": {"line": 63, "column": 7}, "meta": {}}, {"ancestorTitles": ["PocketBase Service", "Authentication Check"], "fullName": "PocketBase Service Authentication Check should return true when user is authenticated", "status": "passed", "title": "should return true when user is authenticated", "duration": 0.4719310000000405, "failureMessages": [], "location": {"line": 68, "column": 7}, "meta": {}}, {"ancestorTitles": ["PocketBase Service", "Current User"], "fullName": "PocketBase Service Current User should return null when no user is authenticated", "status": "passed", "title": "should return null when no user is authenticated", "duration": 0.516193999999814, "failureMessages": [], "location": {"line": 75, "column": 7}, "meta": {}}, {"ancestorTitles": ["PocketBase Service", "Current User"], "fullName": "PocketBase Service Current User should return user data when authenticated", "status": "passed", "title": "should return user data when authenticated", "duration": 1.6891130000003614, "failureMessages": [], "location": {"line": 80, "column": 7}, "meta": {}}, {"ancestorTitles": ["PocketBase Service", "<PERSON><PERSON>"], "fullName": "PocketBase Service Login should successfully authenticate user with valid credentials", "status": "passed", "title": "should successfully authenticate user with valid credentials", "duration": 4.634852999999566, "failureMessages": [], "location": {"line": 88, "column": 7}, "meta": {}}, {"ancestorTitles": ["PocketBase Service", "<PERSON><PERSON>"], "fullName": "PocketBase Service Login should return error when login fails", "status": "passed", "title": "should return error when login fails", "duration": 5.020767000001797, "failureMessages": [], "location": {"line": 102, "column": 7}, "meta": {}}, {"ancestorTitles": ["PocketBase Service", "Registration"], "fullName": "PocketBase Service Registration should successfully register a new user", "status": "passed", "title": "should successfully register a new user", "duration": 1.9551769999998214, "failureMessages": [], "location": {"line": 114, "column": 7}, "meta": {}}, {"ancestorTitles": ["PocketBase Service", "Registration"], "fullName": "PocketBase Service Registration should return error when registration fails", "status": "passed", "title": "should return error when registration fails", "duration": 0.6733380000005127, "failureMessages": [], "location": {"line": 128, "column": 7}, "meta": {}}, {"ancestorTitles": ["PocketBase Service", "Logout"], "fullName": "PocketBase Service Logout should clear auth store on logout", "status": "passed", "title": "should clear auth store on logout", "duration": 0.5871609999994689, "failureMessages": [], "location": {"line": 140, "column": 7}, "meta": {}}, {"ancestorTitles": ["PocketBase Service", "Collection Access"], "fullName": "PocketBase Service Collection Access should return collection reference", "status": "passed", "title": "should return collection reference", "duration": 0.6641829999971378, "failureMessages": [], "location": {"line": 147, "column": 7}, "meta": {}}, {"ancestorTitles": ["PocketBase Service", "Real-time Subscriptions"], "fullName": "PocketBase Service Real-time Subscriptions should subscribe to real-time updates", "status": "passed", "title": "should subscribe to real-time updates", "duration": 0.837708000002749, "failureMessages": [], "location": {"line": 155, "column": 7}, "meta": {}}, {"ancestorTitles": ["PocketBase Service", "Real-time Subscriptions"], "fullName": "PocketBase Service Real-time Subscriptions should unsubscribe from real-time updates", "status": "passed", "title": "should unsubscribe from real-time updates", "duration": 2.1447859999971115, "failureMessages": [], "location": {"line": 164, "column": 7}, "meta": {}}, {"ancestorTitles": ["PocketBase Service", "Client Access"], "fullName": "PocketBase Service Client Access should return PocketBase client instance", "status": "passed", "title": "should return PocketBase client instance", "duration": 0.3839029999980994, "failureMessages": [], "location": {"line": 174, "column": 7}, "meta": {}}], "startTime": 1752960771426, "endTime": 1752960771456.3838, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/unit/services/pocketbase.test.js"}, {"assertionResults": [{"ancestorTitles": ["SessionService", "createSession"], "fullName": "SessionService createSession should create a new session with valid data", "status": "passed", "title": "should create a new session with valid data", "duration": 12.29005700000198, "failureMessages": [], "location": {"line": 23, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "createSession"], "fullName": "SessionService createSession should generate unique session IDs", "status": "passed", "title": "should generate unique session IDs", "duration": 2.843040000003384, "failureMessages": [], "location": {"line": 42, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "getSession"], "fullName": "SessionService getSession should retrieve session by ID", "status": "passed", "title": "should retrieve session by ID", "duration": 6.091280000000552, "failureMessages": [], "location": {"line": 51, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "getSession"], "fullName": "SessionService getSession should return null for non-existent session", "status": "passed", "title": "should return null for non-existent session", "duration": 0.46827099999791244, "failureMessages": [], "location": {"line": 60, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "getSessionByToken"], "fullName": "SessionService getSessionByToken should retrieve session by token", "status": "passed", "title": "should retrieve session by token", "duration": 8.763936999999714, "failureMessages": [], "location": {"line": 67, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "getSessionByToken"], "fullName": "SessionService getSessionByToken should return null for non-existent token", "status": "passed", "title": "should return null for non-existent token", "duration": 0.4385930000025837, "failureMessages": [], "location": {"line": 77, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "getUserSessions"], "fullName": "SessionService getUserSessions should return all active sessions for a user", "status": "passed", "title": "should return all active sessions for a user", "duration": 4.26238899999953, "failureMessages": [], "location": {"line": 84, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "getUserSessions"], "fullName": "SessionService getUserSessions should return empty array for user with no sessions", "status": "passed", "title": "should return empty array for user with no sessions", "duration": 1.8721370000021125, "failureMessages": [], "location": {"line": 99, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "updateSessionActivity"], "fullName": "SessionService updateSessionActivity should update last activity for valid session", "status": "passed", "title": "should update last activity for valid session", "duration": 13.749718999999459, "failureMessages": [], "location": {"line": 106, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "updateSessionActivity"], "fullName": "SessionService updateSessionActivity should return false for non-existent token", "status": "passed", "title": "should return false for non-existent token", "duration": 0.8614120000020193, "failureMessages": [], "location": {"line": 121, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "updateSessionActivity"], "fullName": "SessionService updateSessionActivity should return false for inactive session", "status": "passed", "title": "should return false for inactive session", "duration": 2.0006829999983893, "failureMessages": [], "location": {"line": 126, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "invalidateSession"], "fullName": "SessionService invalidateSession should invalidate session by ID", "status": "passed", "title": "should invalidate session by ID", "duration": 0.4463759999998729, "failureMessages": [], "location": {"line": 139, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "invalidateSession"], "fullName": "SessionService invalidateSession should add token to blacklist when invalidating", "status": "passed", "title": "should add token to blacklist when invalidating", "duration": 0.3314280000013241, "failureMessages": [], "location": {"line": 149, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "invalidateSession"], "fullName": "SessionService invalidateSession should return false for non-existent session", "status": "passed", "title": "should return false for non-existent session", "duration": 0.25877699999909964, "failureMessages": [], "location": {"line": 159, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "invalidateSessionByToken"], "fullName": "SessionService invalidateSessionByToken should invalidate session by token", "status": "passed", "title": "should invalidate session by token", "duration": 0.6736550000023271, "failureMessages": [], "location": {"line": 166, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "invalidateSessionByToken"], "fullName": "SessionService invalidateSessionByToken should return false for non-existent token", "status": "passed", "title": "should return false for non-existent token", "duration": 0.27387199999793665, "failureMessages": [], "location": {"line": 178, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "invalidateAllUserSessions"], "fullName": "SessionService invalidateAllUserSessions should invalidate all sessions for a user", "status": "passed", "title": "should invalidate all sessions for a user", "duration": 0.5717469999981404, "failureMessages": [], "location": {"line": 185, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "invalidateAllUserSessions"], "fullName": "SessionService invalidateAllUserSessions should exclude specified token from invalidation", "status": "passed", "title": "should exclude specified token from invalidation", "duration": 2.6326659999976982, "failureMessages": [], "location": {"line": 203, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "isTokenBlacklisted"], "fullName": "SessionService isTokenBlacklisted should return true for blacklisted tokens", "status": "passed", "title": "should return true for blacklisted tokens", "duration": 0.4139599999980419, "failureMessages": [], "location": {"line": 221, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "isTokenBlacklisted"], "fullName": "SessionService isTokenBlacklisted should return false for non-blacklisted tokens", "status": "passed", "title": "should return false for non-blacklisted tokens", "duration": 0.4661420000011276, "failureMessages": [], "location": {"line": 230, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "validateSession"], "fullName": "SessionService validateSession should validate active session and update activity", "status": "passed", "title": "should validate active session and update activity", "duration": 0.5183050000014191, "failureMessages": [], "location": {"line": 239, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "validateSession"], "fullName": "SessionService validateSession should reject blacklisted tokens", "status": "passed", "title": "should reject blacklisted tokens", "duration": 0.34292500000083237, "failureMessages": [], "location": {"line": 250, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "validateSession"], "fullName": "SessionService validateSession should reject non-existent sessions", "status": "passed", "title": "should reject non-existent sessions", "duration": 0.32130099999994854, "failureMessages": [], "location": {"line": 262, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "validateSession"], "fullName": "SessionService validateSession should reject inactive sessions", "status": "passed", "title": "should reject inactive sessions", "duration": 0.32531400000152644, "failureMessages": [], "location": {"line": 269, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "getSessionStats"], "fullName": "SessionService getSessionStats should return correct session statistics", "status": "passed", "title": "should return correct session statistics", "duration": 1.436493999997765, "failureMessages": [], "location": {"line": 284, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "forceLogoutUser"], "fullName": "SessionService forceLogoutUser should force logout all sessions for a user", "status": "passed", "title": "should force logout all sessions for a user", "duration": 0.6515360000012151, "failureMessages": [], "location": {"line": 304, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "getUserSessionDetails"], "fullName": "SessionService getUserSessionDetails should return detailed session information", "status": "passed", "title": "should return detailed session information", "duration": 6.170184999999037, "failureMessages": [], "location": {"line": 319, "column": 7}, "meta": {}}], "startTime": 1752960768306, "endTime": 1752960768378.1702, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/unit/services/sessionService.test.js"}, {"assertionResults": [{"ancestorTitles": ["Authentication Store (TEST-001)", "Initial State"], "fullName": "Authentication Store (TEST-001) Initial State should initialize with correct defaults", "status": "passed", "title": "should initialize with correct defaults", "duration": 241.9769520000009, "failureMessages": [], "location": {"line": 47, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Getters"], "fullName": "Authentication Store (TEST-001) Getters should return correct isLoggedIn status", "status": "passed", "title": "should return correct isLoggedIn status", "duration": 4.030044999999518, "failureMessages": [], "location": {"line": 59, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Getters"], "fullName": "Authentication Store (TEST-001) Getters should return current user", "status": "passed", "title": "should return current user", "duration": 2.505557000000408, "failureMessages": [], "location": {"line": 68, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Login Action"], "fullName": "Authentication Store (TEST-001) Login Action should login successfully", "status": "passed", "title": "should login successfully", "duration": 5.025777000002563, "failureMessages": [], "location": {"line": 77, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Login Action"], "fullName": "Authentication Store (TEST-001) Login Action should handle login failure", "status": "passed", "title": "should handle login failure", "duration": 3.5882739999979094, "failureMessages": [], "location": {"line": 99, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Login Action"], "fullName": "Authentication Store (TEST-001) Login Action should handle login exception", "status": "passed", "title": "should handle login exception", "duration": 1.7680180000024848, "failureMessages": [], "location": {"line": 118, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Register Action"], "fullName": "Authentication Store (TEST-001) Register Action should register successfully", "status": "passed", "title": "should register successfully", "duration": 2.3887090000025637, "failureMessages": [], "location": {"line": 134, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Register Action"], "fullName": "Authentication Store (TEST-001) Register Action should handle registration failure", "status": "passed", "title": "should handle registration failure", "duration": 1.2536639999998442, "failureMessages": [], "location": {"line": 155, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Logout Action"], "fullName": "Authentication Store (TEST-001) Logout Action should logout successfully", "status": "passed", "title": "should logout successfully", "duration": 2.4111589999993157, "failureMessages": [], "location": {"line": 173, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Token Refresh"], "fullName": "Authentication Store (TEST-001) Token Refresh should refresh token successfully", "status": "passed", "title": "should refresh token successfully", "duration": 1.2964839999985998, "failureMessages": [], "location": {"line": 194, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Token Refresh"], "fullName": "Authentication Store (TEST-001) Token Refresh should handle token refresh failure", "status": "passed", "title": "should handle token refresh failure", "duration": 1.0300609999976587, "failureMessages": [], "location": {"line": 210, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Password Reset"], "fullName": "Authentication Store (TEST-001) Password Reset should request password reset successfully", "status": "passed", "title": "should request password reset successfully", "duration": 1.751565999999002, "failureMessages": [], "location": {"line": 224, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Password Reset"], "fullName": "Authentication Store (TEST-001) Password Reset should reset password successfully", "status": "passed", "title": "should reset password successfully", "duration": 1.9985989999986487, "failureMessages": [], "location": {"line": 237, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "State Persistence"], "fullName": "Authentication Store (TEST-001) State Persistence should persist auth state to localStorage", "status": "passed", "title": "should persist auth state to localStorage", "duration": 2.933063000000402, "failureMessages": [], "location": {"line": 252, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "State Persistence"], "fullName": "Authentication Store (TEST-001) State Persistence should restore auth state from localStorage", "status": "passed", "title": "should restore auth state from localStorage", "duration": 1.1629330000032496, "failureMessages": [], "location": {"line": 266, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "State Persistence"], "fullName": "Authentication Store (TEST-001) State Persistence should clear auth state", "status": "passed", "title": "should clear auth state", "duration": 1.359614999997575, "failureMessages": [], "location": {"line": 283, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Initialization"], "fullName": "Authentication Store (TEST-001) Initialization should initialize auth state", "status": "passed", "title": "should initialize auth state", "duration": 1.3100699999995413, "failureMessages": [], "location": {"line": 300, "column": 7}, "meta": {}}], "startTime": 1752960766704, "endTime": 1752960766985.31, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/auth.spec.js"}, {"assertionResults": [], "startTime": 1752960745404, "endTime": 1752960745404, "status": "failed", "message": "Failed to resolve import \"@/services/databaseService\" from \"ui/stores/__tests__/tasks.spec.js\". Does the file exist?", "name": "/Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js"}]}