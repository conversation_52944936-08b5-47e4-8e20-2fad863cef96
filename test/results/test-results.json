{"numTotalTestSuites": 10, "numPassedTestSuites": 4, "numFailedTestSuites": 6, "numPendingTestSuites": 0, "numTotalTests": 20, "numPassedTests": 13, "numFailedTests": 7, "numPendingTests": 0, "numTodoTests": 0, "snapshot": {"added": 0, "failure": false, "filesAdded": 0, "filesRemoved": 0, "filesRemovedList": [], "filesUnmatched": 0, "filesUpdated": 0, "matched": 0, "total": 0, "unchecked": 0, "uncheckedKeysByFile": [], "unmatched": 0, "updated": 0, "didUpdate": false}, "startTime": 1752960063654, "success": false, "testResults": [{"assertionResults": [{"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Network Error Handling"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Network Error Handling should handle network errors during login", "status": "passed", "title": "should handle network errors during login", "duration": 5.778265999999803, "failureMessages": [], "location": {"line": 101, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Network Error Handling"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Network Error Handling should handle timeout errors", "status": "passed", "title": "should handle timeout errors", "duration": 52.52216700000008, "failureMessages": [], "location": {"line": 118, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Network Error Handling"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Network Error Handling should handle server errors gracefully", "status": "passed", "title": "should handle server errors gracefully", "duration": 1.1275949999999284, "failureMessages": [], "location": {"line": 137, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "LocalStorage Error Handling"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) LocalStorage Error Handling should handle localStorage unavailability", "status": "failed", "title": "should handle localStorage unavailability", "duration": 9.271208000000115, "failureMessages": ["AssertionError: expected [Function] to not throw an error but 'Error: localStorage quota exceeded' was thrown\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1420:16)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/test/integration/error-handling.test.js:168:54\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)", "AssertionError: expected [Function] to not throw an error but 'Error: localStorage quota exceeded' was thrown\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1420:16)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/test/integration/error-handling.test.js:168:54\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)", "AssertionError: expected [Function] to not throw an error but 'Error: localStorage quota exceeded' was thrown\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1420:16)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/test/integration/error-handling.test.js:168:54\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)"], "location": {"line": 158, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "LocalStorage Error Handling"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) LocalStorage Error Handling should handle localStorage quota exceeded", "status": "failed", "title": "should handle localStorage quota exceeded", "duration": 3.889329000000089, "failureMessages": ["AssertionError: expected [Function] to not throw an error but 'Error: QuotaExceededError' was thrown\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1420:16)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/test/integration/error-handling.test.js:188:54\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)", "AssertionError: expected [Function] to not throw an error but 'Error: QuotaExceededError' was thrown\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1420:16)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/test/integration/error-handling.test.js:188:54\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)", "AssertionError: expected [Function] to not throw an error but 'Error: QuotaExceededError' was thrown\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1420:16)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/test/integration/error-handling.test.js:188:54\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)"], "location": {"line": 175, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "LocalStorage Error Handling"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) LocalStorage Error Handling should handle corrupted localStorage data", "status": "failed", "title": "should handle corrupted localStorage data", "duration": 3.394264999999905, "failureMessages": ["AssertionError: expected [Function] to not throw an error but 'SyntaxError: Unexpected token \\'i\\', …' was thrown\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1420:16)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/test/integration/error-handling.test.js:202:54\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)", "AssertionError: expected [Function] to not throw an error but 'SyntaxError: Unexpected token \\'i\\', …' was thrown\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1420:16)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/test/integration/error-handling.test.js:202:54\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)", "AssertionError: expected [Function] to not throw an error but 'SyntaxError: Unexpected token \\'i\\', …' was thrown\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1420:16)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/test/integration/error-handling.test.js:202:54\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)"], "location": {"line": 191, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Authentication Edge Cases"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Authentication Edge Cases should handle simultaneous login attempts", "status": "passed", "title": "should handle simultaneous login attempts", "duration": 102.65497000000005, "failureMessages": [], "location": {"line": 212, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Authentication Edge Cases"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Authentication Edge Cases should handle login with empty credentials", "status": "failed", "title": "should handle login with empty credentials", "duration": 307.53198199999997, "failureMessages": ["AssertionError: expected true to be false // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/integration/error-handling.test.js:248:30\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected true to be false // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/integration/error-handling.test.js:248:30\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected true to be false // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/integration/error-handling.test.js:248:30\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 243, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Authentication Edge Cases"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Authentication Edge Cases should handle login with null/undefined credentials", "status": "failed", "title": "should handle login with null/undefined credentials", "duration": 611.6795429999997, "failureMessages": ["AssertionError: expected true to be false // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/integration/error-handling.test.js:258:31\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected true to be false // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/integration/error-handling.test.js:258:31\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected true to be false // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/integration/error-handling.test.js:258:31\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 252, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Authentication Edge Cases"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Authentication Edge Cases should handle extremely long input values", "status": "passed", "title": "should handle extremely long input values", "duration": 101.96611499999972, "failureMessages": [], "location": {"line": 263, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Token Refresh Edge Cases"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Token Refresh Edge Cases should handle token refresh when already refreshing", "status": "passed", "title": "should handle token refresh when already refreshing", "duration": 203.15976799999953, "failureMessages": [], "location": {"line": 280, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Token Refresh Edge Cases"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Token Refresh Edge Cases should handle token refresh with invalid response", "status": "failed", "title": "should handle token refresh with invalid response", "duration": 3.3602789999995366, "failureMessages": ["AssertionError: expected true to be false // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/integration/error-handling.test.js:327:30\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected true to be false // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/integration/error-handling.test.js:327:30\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected true to be false // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/integration/error-handling.test.js:327:30\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 315, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Session Management Edge Cases"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Session Management Edge Cases should handle session restoration with partial data", "status": "failed", "title": "should handle session restoration with partial data", "duration": 3.512373999999909, "failureMessages": ["AssertionError: expected null to be 'stored-token' // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/integration/error-handling.test.js:347:31\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)", "AssertionError: expected null to be 'stored-token' // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/integration/error-handling.test.js:347:31\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)", "AssertionError: expected null to be 'stored-token' // Object.is equality\n    at /Volumes/External Drive/Development/track-tasks/test/integration/error-handling.test.js:347:31\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)"], "location": {"line": 333, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Session Management Edge Cases"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Session Management Edge Cases should handle concurrent session modifications", "status": "passed", "title": "should handle concurrent session modifications", "duration": 0.973508000000038, "failureMessages": [], "location": {"line": 352, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Memory and Performance Edge Cases"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Memory and Performance Edge Cases should handle memory pressure gracefully", "status": "passed", "title": "should handle memory pressure gracefully", "duration": 163.69641300000058, "failureMessages": [], "location": {"line": 379, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Memory and Performance Edge Cases"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Memory and Performance Edge Cases should handle rapid successive operations", "status": "passed", "title": "should handle rapid successive operations", "duration": 114.54330399999981, "failureMessages": [], "location": {"line": 394, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Browser Compatibility Edge Cases"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Browser Compatibility Edge Cases should handle missing browser APIs gracefully", "status": "passed", "title": "should handle missing browser APIs gracefully", "duration": 1.0178809999997611, "failureMessages": [], "location": {"line": 415, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Browser Compatibility Edge Cases"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Browser Compatibility Edge Cases should handle disabled cookies", "status": "passed", "title": "should handle disabled cookies", "duration": 0.8885550000004514, "failureMessages": [], "location": {"line": 428, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Data Validation Edge Cases"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Data Validation Edge Cases should handle malformed server responses", "status": "passed", "title": "should handle malformed server responses", "duration": 0.6182840000001306, "failureMessages": [], "location": {"line": 445, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Data Validation Edge Cases"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Data Validation Edge Cases should handle unexpected data types", "status": "passed", "title": "should handle unexpected data types", "duration": 0.5683850000004895, "failureMessages": [], "location": {"line": 465, "column": 7}, "meta": {}}], "startTime": 1752960065690, "endTime": 1752960067383.6182, "status": "failed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/integration/error-handling.test.js"}]}