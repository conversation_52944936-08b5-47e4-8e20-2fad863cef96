{"numTotalTestSuites": 9, "numPassedTestSuites": 5, "numFailedTestSuites": 4, "numPendingTestSuites": 0, "numTotalTests": 17, "numPassedTests": 14, "numFailedTests": 3, "numPendingTests": 0, "numTodoTests": 0, "snapshot": {"added": 0, "failure": false, "filesAdded": 0, "filesRemoved": 0, "filesRemovedList": [], "filesUnmatched": 0, "filesUpdated": 0, "matched": 0, "total": 0, "unchecked": 0, "uncheckedKeysByFile": [], "unmatched": 0, "updated": 0, "didUpdate": false}, "startTime": 1752959183974, "success": false, "testResults": [{"assertionResults": [{"ancestorTitles": ["Route Guards (TEST-003)", "Authentication Guard"], "fullName": "Route Guards (TEST-003) Authentication Guard should initialize auth store if not initialized", "status": "passed", "title": "should initialize auth store if not initialized", "duration": 3.6510910000001786, "failureMessages": [], "location": {"line": 94, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Authentication Guard"], "fullName": "Route Guards (TEST-003) Authentication Guard should not initialize auth store if already initialized", "status": "passed", "title": "should not initialize auth store if already initialized", "duration": 2.295951000000059, "failureMessages": [], "location": {"line": 108, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Protected Routes"], "fullName": "Route Guards (TEST-003) Protected Routes should allow access to protected routes when authenticated", "status": "passed", "title": "should allow access to protected routes when authenticated", "duration": 0.5493449999999029, "failureMessages": [], "location": {"line": 123, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Protected Routes"], "fullName": "Route Guards (TEST-003) Protected Routes should redirect to login when accessing protected routes while unauthenticated", "status": "passed", "title": "should redirect to login when accessing protected routes while unauthenticated", "duration": 2.6026090000000295, "failureMessages": [], "location": {"line": 136, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Protected Routes"], "fullName": "Route Guards (TEST-003) Protected Routes should preserve query parameters in redirect", "status": "passed", "title": "should preserve query parameters in redirect", "duration": 0.8914899999999761, "failureMessages": [], "location": {"line": 152, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Guest-Only Routes"], "fullName": "Route Guards (TEST-003) Guest-Only Routes should allow access to guest-only routes when unauthenticated", "status": "passed", "title": "should allow access to guest-only routes when unauthenticated", "duration": 0.4998849999997219, "failureMessages": [], "location": {"line": 171, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Guest-Only Routes"], "fullName": "Route Guards (TEST-003) Guest-Only Routes should redirect to home when accessing guest-only routes while authenticated", "status": "passed", "title": "should redirect to home when accessing guest-only routes while authenticated", "duration": 0.6622039999997469, "failureMessages": [], "location": {"line": 184, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Guest-Only Routes"], "fullName": "Route Guards (TEST-003) Guest-Only Routes should redirect authenticated users from register page", "status": "passed", "title": "should redirect authenticated users from register page", "duration": 0.9900680000000648, "failureMessages": [], "location": {"line": 197, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Guest-Only Routes"], "fullName": "Route Guards (TEST-003) Guest-Only Routes should redirect authenticated users from forgot-password page", "status": "passed", "title": "should redirect authenticated users from forgot-password page", "duration": 0.566845999999714, "failureMessages": [], "location": {"line": 210, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Admin Routes"], "fullName": "Route Guards (TEST-003) Admin Routes should allow access to admin routes when user is admin", "status": "passed", "title": "should allow access to admin routes when user is admin", "duration": 0.3779420000000755, "failureMessages": [], "location": {"line": 225, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Admin Routes"], "fullName": "Route Guards (TEST-003) Admin Routes should redirect to unauthorized when non-admin user accesses admin routes", "status": "failed", "title": "should redirect to unauthorized when non-admin user accesses admin routes", "duration": 7.929055999999946, "failureMessages": ["AssertionError: expected \"spy\" to be called with arguments: [ { path: '/unauthorized' } ]\u001b[90m\n\nReceived: \n\n\u001b[1m  1st spy call:\n\n\u001b[22m\u001b[32m- [\u001b[90m\n\u001b[32m-   {\u001b[90m\n\u001b[32m-     \"path\": \"/unauthorized\",\u001b[90m\n\u001b[32m-   },\u001b[90m\n\u001b[32m- ]\u001b[90m\n\u001b[31m+ []\u001b[90m\n\u001b[39m\u001b[90m\n\nNumber of calls: \u001b[1m1\u001b[22m\n\u001b[39m\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1355:10)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/router/guards.test.js:250:20\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected \"spy\" to be called with arguments: [ { path: '/unauthorized' } ]\u001b[90m\n\nReceived: \n\n\u001b[1m  1st spy call:\n\n\u001b[22m\u001b[32m- [\u001b[90m\n\u001b[32m-   {\u001b[90m\n\u001b[32m-     \"path\": \"/unauthorized\",\u001b[90m\n\u001b[32m-   },\u001b[90m\n\u001b[32m- ]\u001b[90m\n\u001b[31m+ []\u001b[90m\n\u001b[39m\u001b[90m\n\nNumber of calls: \u001b[1m1\u001b[22m\n\u001b[39m\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1355:10)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/router/guards.test.js:250:20\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected \"spy\" to be called with arguments: [ { path: '/unauthorized' } ]\u001b[90m\n\nReceived: \n\n\u001b[1m  1st spy call:\n\n\u001b[22m\u001b[32m- [\u001b[90m\n\u001b[32m-   {\u001b[90m\n\u001b[32m-     \"path\": \"/unauthorized\",\u001b[90m\n\u001b[32m-   },\u001b[90m\n\u001b[32m- ]\u001b[90m\n\u001b[31m+ []\u001b[90m\n\u001b[39m\u001b[90m\n\nNumber of calls: \u001b[1m1\u001b[22m\n\u001b[39m\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1355:10)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/router/guards.test.js:250:20\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 239, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Admin Routes"], "fullName": "Route Guards (TEST-003) Admin Routes should redirect to unauthorized when unauthenticated user accesses admin routes", "status": "failed", "title": "should redirect to unauthorized when unauthenticated user accesses admin routes", "duration": 2.7587719999996807, "failureMessages": ["AssertionError: expected \"spy\" to be called with arguments: [ { path: '/unauthorized' } ]\u001b[90m\n\nReceived: \n\n\u001b[1m  1st spy call:\n\n\u001b[22m\u001b[2m  [\u001b[22m\n\u001b[2m    {\u001b[22m\n\u001b[32m-     \"path\": \"/unauthorized\",\u001b[90m\n\u001b[31m+     \"path\": \"/login\",\u001b[90m\n\u001b[31m+     \"query\": {\u001b[90m\n\u001b[31m+       \"redirect\": \"/admin\",\u001b[90m\n\u001b[31m+     },\u001b[90m\n\u001b[2m    },\u001b[22m\n\u001b[2m  ]\u001b[22m\n\u001b[39m\u001b[90m\n\nNumber of calls: \u001b[1m1\u001b[22m\n\u001b[39m\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1355:10)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/router/guards.test.js:264:20\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected \"spy\" to be called with arguments: [ { path: '/unauthorized' } ]\u001b[90m\n\nReceived: \n\n\u001b[1m  1st spy call:\n\n\u001b[22m\u001b[2m  [\u001b[22m\n\u001b[2m    {\u001b[22m\n\u001b[32m-     \"path\": \"/unauthorized\",\u001b[90m\n\u001b[31m+     \"path\": \"/login\",\u001b[90m\n\u001b[31m+     \"query\": {\u001b[90m\n\u001b[31m+       \"redirect\": \"/admin\",\u001b[90m\n\u001b[31m+     },\u001b[90m\n\u001b[2m    },\u001b[22m\n\u001b[2m  ]\u001b[22m\n\u001b[39m\u001b[90m\n\nNumber of calls: \u001b[1m1\u001b[22m\n\u001b[39m\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1355:10)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/router/guards.test.js:264:20\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected \"spy\" to be called with arguments: [ { path: '/unauthorized' } ]\u001b[90m\n\nReceived: \n\n\u001b[1m  1st spy call:\n\n\u001b[22m\u001b[2m  [\u001b[22m\n\u001b[2m    {\u001b[22m\n\u001b[32m-     \"path\": \"/unauthorized\",\u001b[90m\n\u001b[31m+     \"path\": \"/login\",\u001b[90m\n\u001b[31m+     \"query\": {\u001b[90m\n\u001b[31m+       \"redirect\": \"/admin\",\u001b[90m\n\u001b[31m+     },\u001b[90m\n\u001b[2m    },\u001b[22m\n\u001b[2m  ]\u001b[22m\n\u001b[39m\u001b[90m\n\nNumber of calls: \u001b[1m1\u001b[22m\n\u001b[39m\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1355:10)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/router/guards.test.js:264:20\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 253, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Public Routes"], "fullName": "Route Guards (TEST-003) Public Routes should allow access to public routes regardless of authentication status", "status": "passed", "title": "should allow access to public routes regardless of authentication status", "duration": 0.4036989999999605, "failureMessages": [], "location": {"line": 269, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Public Routes"], "fullName": "Route Guards (TEST-003) Public Routes should allow authenticated users to access public routes", "status": "passed", "title": "should allow authenticated users to access public routes", "duration": 0.3336899999999332, "failureMessages": [], "location": {"line": 282, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Erro<PERSON>"], "fullName": "Route Guards (TEST-003) Error Handling should handle auth initialization errors gracefully", "status": "failed", "title": "should handle auth initialization errors gracefully", "duration": 1.5945580000002337, "failureMessages": ["Error: Auth initialization failed\n    at /Volumes/External Drive/Development/track-tasks/test/unit/router/guards.test.js:303:54\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)", "Error: Auth initialization failed\n    at /Volumes/External Drive/Development/track-tasks/test/unit/router/guards.test.js:303:54\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)", "Error: Auth initialization failed\n    at /Volumes/External Drive/Development/track-tasks/test/unit/router/guards.test.js:303:54\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)"], "location": {"line": 297, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Route Meta Combinations"], "fullName": "Route Guards (TEST-003) Route Meta Combinations should handle routes with multiple meta requirements", "status": "passed", "title": "should handle routes with multiple meta requirements", "duration": 0.32107700000005934, "failureMessages": [], "location": {"line": 314, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Route Meta Combinations"], "fullName": "Route Guards (TEST-003) Route Meta Combinations should prioritize authentication check over admin check", "status": "passed", "title": "should prioritize authentication check over admin check", "duration": 0.5289450000000215, "failureMessages": [], "location": {"line": 331, "column": 7}, "meta": {}}], "startTime": 1752959185846, "endTime": 1752959185874.529, "status": "failed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/unit/router/guards.test.js"}]}