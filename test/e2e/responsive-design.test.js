/**
 * Responsive Design E2E Tests (TEST-009)
 * Tests for responsive design on different devices
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'

// Device configurations for testing
const DEVICES = {
  mobile: {
    name: 'iPhone 12',
    viewport: { width: 390, height: 844 },
    userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15',
    isMobile: true,
    hasTouch: true,
  },
  tablet: {
    name: 'iPad',
    viewport: { width: 768, height: 1024 },
    userAgent: 'Mozilla/5.0 (iPad; CPU OS 14_0 like Mac OS X) AppleWebKit/605.1.15',
    isMobile: false,
    hasTouch: true,
  },
  desktop: {
    name: 'Desktop',
    viewport: { width: 1920, height: 1080 },
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    isMobile: false,
    hasTouch: false,
  },
  smallMobile: {
    name: 'iPhone SE',
    viewport: { width: 375, height: 667 },
    userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15',
    isMobile: true,
    hasTouch: true,
  },
}

// Mock browser environment
const mockBrowser = {
  page: {
    setViewportSize: vi.fn(),
    goto: vi.fn(),
    waitForSelector: vi.fn(),
    screenshot: vi.fn(),
    evaluate: vi.fn(),
    locator: vi.fn(),
    getByRole: vi.fn(),
    getByTestId: vi.fn(),
    click: vi.fn(),
    fill: vi.fn(),
    isVisible: vi.fn(),
    boundingBox: vi.fn(),
    hover: vi.fn(),
    tap: vi.fn(),
  },
  context: {
    setExtraHTTPHeaders: vi.fn(),
  }
}

// Helper functions
const setDevice = async (page, device) => {
  await page.setViewportSize(device.viewport)
  await mockBrowser.context.setExtraHTTPHeaders({
    'User-Agent': device.userAgent
  })
}

const checkElementVisibility = async (page, selector) => {
  const element = await page.locator(selector)
  return await element.isVisible()
}

const getElementBounds = async (page, selector) => {
  const element = await page.locator(selector)
  return await element.boundingBox()
}

const checkTouchTargetSize = async (page, selector) => {
  const bounds = await getElementBounds(page, selector)
  const minTouchTarget = 44 // 44px minimum touch target size
  return bounds && bounds.width >= minTouchTarget && bounds.height >= minTouchTarget
}

describe('Responsive Design E2E Tests (TEST-009)', () => {
  let page

  beforeEach(async () => {
    page = mockBrowser.page
    vi.clearAllMocks()
    
    // Mock default implementations
    page.setViewportSize.mockResolvedValue()
    page.goto.mockResolvedValue()
    page.waitForSelector.mockResolvedValue()
    page.screenshot.mockResolvedValue()
    page.evaluate.mockResolvedValue()
    page.click.mockResolvedValue()
    page.fill.mockResolvedValue()
    page.isVisible.mockResolvedValue(true)
    page.boundingBox.mockResolvedValue({ x: 0, y: 0, width: 100, height: 50 })
    page.hover.mockResolvedValue()
    page.tap.mockResolvedValue()
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('Authentication Forms Responsive Design', () => {
    it('should display login form correctly on mobile devices', async () => {
      // Step 1: Set mobile viewport
      await setDevice(page, DEVICES.mobile)
      expect(page.setViewportSize).toHaveBeenCalledWith(DEVICES.mobile.viewport)

      // Step 2: Navigate to login page
      await page.goto('http://localhost:3000/login')
      await page.waitForSelector('[data-testid="login-form"]')

      // Step 3: Check form layout on mobile
      page.isVisible.mockResolvedValue(true)
      const formVisible = await checkElementVisibility(page, '[data-testid="login-form"]')
      expect(formVisible).toBe(true)

      // Step 4: Check input field sizes
      page.boundingBox.mockResolvedValue({ x: 0, y: 0, width: 350, height: 56 })
      const emailInputBounds = await getElementBounds(page, '[data-testid="email-input"]')
      expect(emailInputBounds.width).toBeGreaterThan(300) // Should be wide enough on mobile

      // Step 5: Check touch target sizes
      page.boundingBox.mockResolvedValue({ x: 0, y: 0, width: 350, height: 48 })
      const loginButtonTouchable = await checkTouchTargetSize(page, '[data-testid="login-button"]')
      expect(loginButtonTouchable).toBe(true)
    })

    it('should display registration form correctly on tablet devices', async () => {
      // Step 1: Set tablet viewport
      await setDevice(page, DEVICES.tablet)

      // Step 2: Navigate to registration page
      await page.goto('http://localhost:3000/register')
      await page.waitForSelector('[data-testid="registration-form"]')

      // Step 3: Check form layout adapts to tablet size
      const formVisible = await checkElementVisibility(page, '[data-testid="registration-form"]')
      expect(formVisible).toBe(true)

      // Step 4: Check that form doesn't become too wide on tablet
      page.boundingBox.mockResolvedValue({ x: 100, y: 0, width: 568, height: 400 })
      const formBounds = await getElementBounds(page, '[data-testid="registration-form"]')
      expect(formBounds.width).toBeLessThan(600) // Should have reasonable max width
    })

    it('should display forms correctly on desktop', async () => {
      // Step 1: Set desktop viewport
      await setDevice(page, DEVICES.desktop)

      // Step 2: Navigate to login page
      await page.goto('http://localhost:3000/login')
      await page.waitForSelector('[data-testid="login-form"]')

      // Step 3: Check form is centered and has appropriate width
      page.boundingBox.mockResolvedValue({ x: 660, y: 200, width: 600, height: 400 })
      const formBounds = await getElementBounds(page, '[data-testid="login-form"]')
      expect(formBounds.width).toBeLessThanOrEqual(600) // Should not be too wide
      expect(formBounds.x).toBeGreaterThan(500) // Should be centered
    })

    it('should handle very small screens gracefully', async () => {
      // Step 1: Set very small mobile viewport
      await setDevice(page, DEVICES.smallMobile)

      // Step 2: Navigate to login page
      await page.goto('http://localhost:3000/login')
      await page.waitForSelector('[data-testid="login-form"]')

      // Step 3: Check form still fits and is usable
      const formVisible = await checkElementVisibility(page, '[data-testid="login-form"]')
      expect(formVisible).toBe(true)

      // Step 4: Check inputs are still appropriately sized
      page.boundingBox.mockResolvedValue({ x: 10, y: 0, width: 355, height: 48 })
      const emailInputBounds = await getElementBounds(page, '[data-testid="email-input"]')
      expect(emailInputBounds.width).toBeGreaterThan(300)
    })
  })

  describe('Navigation Responsive Design', () => {
    it('should show mobile navigation drawer on small screens', async () => {
      // Step 1: Set mobile viewport
      await setDevice(page, DEVICES.mobile)

      // Step 2: Navigate to main page
      await page.goto('http://localhost:3000/')
      await page.waitForSelector('[data-testid="app-layout"]')

      // Step 3: Check mobile navigation elements
      page.isVisible.mockImplementation((selector) => {
        if (selector === '[data-testid="mobile-nav-drawer"]') return Promise.resolve(false) // Initially hidden
        if (selector === '[data-testid="mobile-menu-button"]') return Promise.resolve(true)
        if (selector === '[data-testid="desktop-nav"]') return Promise.resolve(false)
        return Promise.resolve(true)
      })

      const mobileMenuVisible = await checkElementVisibility(page, '[data-testid="mobile-menu-button"]')
      const desktopNavVisible = await checkElementVisibility(page, '[data-testid="desktop-nav"]')

      expect(mobileMenuVisible).toBe(true)
      expect(desktopNavVisible).toBe(false)

      // Step 4: Test opening mobile drawer
      await page.click('[data-testid="mobile-menu-button"]')
      
      page.isVisible.mockImplementation((selector) => {
        if (selector === '[data-testid="mobile-nav-drawer"]') return Promise.resolve(true)
        return Promise.resolve(true)
      })

      const drawerVisible = await checkElementVisibility(page, '[data-testid="mobile-nav-drawer"]')
      expect(drawerVisible).toBe(true)
    })

    it('should show desktop navigation on large screens', async () => {
      // Step 1: Set desktop viewport
      await setDevice(page, DEVICES.desktop)

      // Step 2: Navigate to main page
      await page.goto('http://localhost:3000/')
      await page.waitForSelector('[data-testid="app-layout"]')

      // Step 3: Check desktop navigation elements
      page.isVisible.mockImplementation((selector) => {
        if (selector === '[data-testid="desktop-nav"]') return Promise.resolve(true)
        if (selector === '[data-testid="mobile-menu-button"]') return Promise.resolve(false)
        return Promise.resolve(true)
      })

      const desktopNavVisible = await checkElementVisibility(page, '[data-testid="desktop-nav"]')
      const mobileMenuVisible = await checkElementVisibility(page, '[data-testid="mobile-menu-button"]')

      expect(desktopNavVisible).toBe(true)
      expect(mobileMenuVisible).toBe(false)
    })

    it('should adapt navigation for tablet screens', async () => {
      // Step 1: Set tablet viewport
      await setDevice(page, DEVICES.tablet)

      // Step 2: Navigate to main page
      await page.goto('http://localhost:3000/')
      await page.waitForSelector('[data-testid="app-layout"]')

      // Step 3: Check tablet navigation (could be either mobile or desktop style)
      const hasDesktopNav = await checkElementVisibility(page, '[data-testid="desktop-nav"]')
      const hasMobileMenu = await checkElementVisibility(page, '[data-testid="mobile-menu-button"]')

      // Should have one or the other, but not both
      expect(hasDesktopNav || hasMobileMenu).toBe(true)
      expect(hasDesktopNav && hasMobileMenu).toBe(false)
    })
  })

  describe('Touch Interactions', () => {
    it('should handle touch interactions on mobile devices', async () => {
      // Step 1: Set mobile device with touch
      await setDevice(page, DEVICES.mobile)

      // Step 2: Navigate to login page
      await page.goto('http://localhost:3000/login')
      await page.waitForSelector('[data-testid="login-form"]')

      // Step 3: Test touch interactions
      await page.tap('[data-testid="email-input"]')
      expect(page.tap).toHaveBeenCalledWith('[data-testid="email-input"]')

      // Step 4: Test button touch targets
      page.boundingBox.mockResolvedValue({ x: 0, y: 0, width: 48, height: 48 })
      const buttonTouchable = await checkTouchTargetSize(page, '[data-testid="login-button"]')
      expect(buttonTouchable).toBe(true)
    })

    it('should provide adequate spacing between touch targets', async () => {
      // Step 1: Set mobile viewport
      await setDevice(page, DEVICES.mobile)

      // Step 2: Navigate to page with multiple buttons
      await page.goto('http://localhost:3000/profile')
      await page.waitForSelector('[data-testid="profile-actions"]')

      // Step 3: Check spacing between buttons
      page.boundingBox
        .mockResolvedValueOnce({ x: 10, y: 100, width: 120, height: 48 }) // First button
        .mockResolvedValueOnce({ x: 140, y: 100, width: 120, height: 48 }) // Second button

      const button1Bounds = await getElementBounds(page, '[data-testid="edit-profile-button"]')
      const button2Bounds = await getElementBounds(page, '[data-testid="change-password-button"]')

      const spacing = button2Bounds.x - (button1Bounds.x + button1Bounds.width)
      expect(spacing).toBeGreaterThanOrEqual(8) // Minimum 8px spacing
    })
  })

  describe('Content Layout Responsive Design', () => {
    it('should stack content vertically on mobile', async () => {
      // Step 1: Set mobile viewport
      await setDevice(page, DEVICES.mobile)

      // Step 2: Navigate to dashboard
      await page.goto('http://localhost:3000/')
      await page.waitForSelector('[data-testid="dashboard"]')

      // Step 3: Check content stacking
      page.evaluate.mockResolvedValue('column')
      const flexDirection = await page.evaluate(() => {
        const dashboard = document.querySelector('[data-testid="dashboard"]')
        return window.getComputedStyle(dashboard).flexDirection
      })

      expect(flexDirection).toBe('column')
    })

    it('should display content in rows on desktop', async () => {
      // Step 1: Set desktop viewport
      await setDevice(page, DEVICES.desktop)

      // Step 2: Navigate to dashboard
      await page.goto('http://localhost:3000/')
      await page.waitForSelector('[data-testid="dashboard"]')

      // Step 3: Check content layout
      page.evaluate.mockResolvedValue('row')
      const flexDirection = await page.evaluate(() => {
        const dashboard = document.querySelector('[data-testid="dashboard"]')
        return window.getComputedStyle(dashboard).flexDirection
      })

      expect(flexDirection).toBe('row')
    })

    it('should adjust font sizes for different screen sizes', async () => {
      // Test mobile font sizes
      await setDevice(page, DEVICES.mobile)
      await page.goto('http://localhost:3000/')
      await page.waitForSelector('[data-testid="page-title"]')

      page.evaluate.mockResolvedValue('24px')
      const mobileFontSize = await page.evaluate(() => {
        const title = document.querySelector('[data-testid="page-title"]')
        return window.getComputedStyle(title).fontSize
      })

      // Test desktop font sizes
      await setDevice(page, DEVICES.desktop)
      await page.goto('http://localhost:3000/')
      await page.waitForSelector('[data-testid="page-title"]')

      page.evaluate.mockResolvedValue('32px')
      const desktopFontSize = await page.evaluate(() => {
        const title = document.querySelector('[data-testid="page-title"]')
        return window.getComputedStyle(title).fontSize
      })

      expect(parseInt(desktopFontSize)).toBeGreaterThan(parseInt(mobileFontSize))
    })
  })

  describe('Form Input Responsive Design', () => {
    it('should adjust input field sizes for mobile', async () => {
      // Step 1: Set mobile viewport
      await setDevice(page, DEVICES.mobile)

      // Step 2: Navigate to form page
      await page.goto('http://localhost:3000/register')
      await page.waitForSelector('[data-testid="registration-form"]')

      // Step 3: Check input field responsiveness
      page.boundingBox.mockResolvedValue({ x: 20, y: 0, width: 350, height: 56 })
      const inputBounds = await getElementBounds(page, '[data-testid="email-input"]')

      expect(inputBounds.width).toBeGreaterThan(300) // Should use most of screen width
      expect(inputBounds.height).toBeGreaterThanOrEqual(48) // Should be tall enough for touch
    })

    it('should handle keyboard appearance on mobile', async () => {
      // Step 1: Set mobile viewport
      await setDevice(page, DEVICES.mobile)

      // Step 2: Navigate to login page
      await page.goto('http://localhost:3000/login')
      await page.waitForSelector('[data-testid="login-form"]')

      // Step 3: Focus on input field
      await page.tap('[data-testid="email-input"]')

      // Step 4: Simulate keyboard appearance (viewport height reduction)
      await page.setViewportSize({ width: 390, height: 400 }) // Reduced height

      // Step 5: Check form is still accessible
      const formVisible = await checkElementVisibility(page, '[data-testid="login-form"]')
      expect(formVisible).toBe(true)
    })
  })

  describe('Accessibility on Different Devices', () => {
    it('should maintain accessibility on mobile devices', async () => {
      // Step 1: Set mobile viewport
      await setDevice(page, DEVICES.mobile)

      // Step 2: Navigate to login page
      await page.goto('http://localhost:3000/login')
      await page.waitForSelector('[data-testid="login-form"]')

      // Step 3: Check touch target sizes meet accessibility guidelines
      const buttonTouchable = await checkTouchTargetSize(page, '[data-testid="login-button"]')
      expect(buttonTouchable).toBe(true)

      // Step 4: Check color contrast is maintained
      page.evaluate.mockResolvedValue(4.5)
      const contrastRatio = await page.evaluate(() => {
        // Mock contrast ratio calculation
        return 4.5 // Should meet WCAG AA standard
      })

      expect(contrastRatio).toBeGreaterThanOrEqual(4.5)
    })

    it('should support keyboard navigation on all devices', async () => {
      // Test on desktop
      await setDevice(page, DEVICES.desktop)
      await page.goto('http://localhost:3000/login')
      await page.waitForSelector('[data-testid="login-form"]')

      // Check tab navigation works
      await page.evaluate(() => {
        const emailInput = document.querySelector('[data-testid="email-input"]')
        emailInput.focus()
        
        // Simulate tab key
        const tabEvent = new KeyboardEvent('keydown', { key: 'Tab' })
        document.dispatchEvent(tabEvent)
      })

      // Verify focus moved to next element
      page.evaluate.mockResolvedValue('[data-testid="password-input"]')
      const focusedElement = await page.evaluate(() => {
        return document.activeElement.getAttribute('data-testid')
      })

      expect(focusedElement).toBe('[data-testid="password-input"]')
    })
  })
})
