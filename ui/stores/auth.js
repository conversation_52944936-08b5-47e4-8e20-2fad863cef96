import { defineStore } from 'pinia'
import { pocketBaseService } from '../../common/services/pocketbase.js'

export const useAuthStore = defineStore('auth', {
  state: () => ({
    user: null,
    token: null,
    isAuthenticated: false,
    loading: false,
    error: null,
    initialized: false,
  }),

  getters: {
    isLoggedIn: (state) => state.isAuthenticated && !!state.token,
    currentUser: (state) => state.user,
    authToken: (state) => state.token,
    isLoading: (state) => state.loading,
    authError: (state) => state.error,
    isInitialized: (state) => state.initialized,
  },

  actions: {
    async login(credentials) {
      this.loading = true
      this.error = null

      try {
        // Validate credentials
        if (!credentials || !credentials.email || !credentials.password) {
          this.error = 'Email and password are required'
          return { success: false, error: this.error }
        }

        // Trim and validate non-empty
        const email = credentials.email.trim()
        const password = credentials.password.trim()

        if (!email || !password) {
          this.error = 'Email and password cannot be empty'
          return { success: false, error: this.error }
        }

        const response = await pocketBaseService.login(email, password)

        if (response.success) {
          this.user = response.user
          this.token = response.token
          this.isAuthenticated = true

          // Persist to localStorage
          this.persistAuthState()

          return { success: true, user: response.user }
        } else {
          this.error = response.error || 'Login failed'
          return { success: false, error: this.error }
        }
      } catch (error) {
        this.error = error.message || 'Login failed'
        // Clear auth state but preserve error
        this.user = null
        this.token = null
        this.isAuthenticated = false
        this.clearPersistedState()
        return { success: false, error: this.error }
      } finally {
        this.loading = false
      }
    },

    async logout() {
      this.loading = true
      this.error = null

      try {
        const response = await pocketBaseService.logout()

        this.clearAuthState()
        this.clearPersistedState()

        return { success: true }
      } catch (error) {
        this.error = error.message || 'Logout failed'
        // Clear local state even if API call fails
        this.clearAuthState()
        this.clearPersistedState()
        return { success: false, error: this.error }
      } finally {
        this.loading = false
      }
    },

    async refreshToken() {
      this.loading = true
      this.error = null

      try {
        // Use the refreshToken method from pocketBaseService
        const response = await pocketBaseService.refreshToken()

        if (response.success && response.token && response.user) {
          this.token = response.token
          this.user = response.user
          this.isAuthenticated = true
          this.persistAuthState()
          return { success: true, token: response.token }
        } else {
          // Handle invalid response (missing token or user)
          if (response.success && (!response.token || !response.user)) {
            this.error = 'Invalid refresh response: missing token or user data'
          } else {
          this.error = response.error || 'Token refresh failed'
          // Clear auth state but preserve error
          this.user = null
          this.token = null
          this.isAuthenticated = false
          this.clearPersistedState()
          return { success: false, error: this.error }
        }
      } catch (error) {
        this.error = error.message || 'Token refresh failed'
        // Clear auth state but preserve error
        this.user = null
        this.token = null
        this.isAuthenticated = false
        this.clearPersistedState()
        return { success: false, error: this.error }
      } finally {
        this.loading = false
      }
    },

    async initializeAuth() {
      this.loading = true
      this.error = null

      try {
        const isAuthenticated = pocketBaseService.isAuthenticated()

        if (isAuthenticated) {
          const userResponse = await pocketBaseService.getCurrentUser()
          const token = pocketBaseService.getToken()

          if (userResponse.success) {
            this.user = userResponse.user
            this.token = token
            this.isAuthenticated = true
            this.persistAuthState()
            return { success: true, user: this.user }
          } else {
            this.clearAuthState()
            return { success: false, error: 'No valid session found' }
          }
        } else {
          this.clearAuthState()
          return { success: false, error: 'No valid session found' }
        }
      } catch (error) {
        this.error = error.message || 'Auth initialization failed'
        this.clearAuthState()
        return { success: false, error: this.error }
      } finally {
        this.loading = false
        this.initialized = true
      }
    },

    async register(userData) {
      this.loading = true
      this.error = null

      try {
        const response = await pocketBaseService.register(userData)

        if (response.success) {
          this.user = response.user
          this.token = response.token
          this.isAuthenticated = true

          // Persist to localStorage
          this.persistAuthState()

          return { success: true, user: response.user }
        } else {
          this.error = response.error || 'Registration failed'
          return { success: false, error: this.error }
        }
      } catch (error) {
        this.error = error.message || 'Registration failed'
        return { success: false, error: this.error }
      } finally {
        this.loading = false
      }
    },

    async updateProfile(userData) {
      this.loading = true
      this.error = null

      try {
        const response = await pocketBaseService.updateProfile(userData)

        if (response.success) {
          this.user = { ...this.user, ...response.user }
          this.persistAuthState()
          return { success: true, user: this.user }
        } else {
          this.error = response.error || 'Profile update failed'
          return { success: false, error: this.error }
        }
      } catch (error) {
        this.error = error.message || 'Profile update failed'
        return { success: false, error: this.error }
      } finally {
        this.loading = false
      }
    },

    async requestPasswordReset(email) {
      this.loading = true
      this.error = null

      try {
        const response = await pocketBaseService.requestPasswordReset(email)
        return response
      } catch (error) {
        this.error = error.message || 'Password reset request failed'
        return { success: false, error: this.error }
      } finally {
        this.loading = false
      }
    },

    async confirmPasswordReset(token, password) {
      this.loading = true
      this.error = null

      try {
        const response = await pocketBaseService.confirmPasswordReset(token, password)
        return response
      } catch (error) {
        this.error = error.message || 'Password reset confirmation failed'
        return { success: false, error: this.error }
      } finally {
        this.loading = false
      }
    },

    // Reset password method that calls the service directly
    async resetPassword(tokenOrData, password) {
      this.loading = true
      this.error = null

      try {
        let response
        // Handle both object format and separate parameters
        if (typeof tokenOrData === 'object') {
          response = await pocketBaseService.resetPassword(tokenOrData)
        } else {
          response = await pocketBaseService.resetPassword({ token: tokenOrData, password })
        }

        if (response.success) {
          return { success: true }
        } else {
          this.error = response.error || 'Password reset failed'
          return { success: false, error: this.error }
        }
      } catch (error) {
        this.error = error.message || 'Password reset failed'
        return { success: false, error: this.error }
      } finally {
        this.loading = false
      }
    },

    async changePassword(currentPasswordOrData, newPassword) {
      this.loading = true
      this.error = null

      try {
        let response
        // Handle both object format and separate parameters
        if (typeof currentPasswordOrData === 'object') {
          response = await pocketBaseService.changePassword(currentPasswordOrData)
        } else {
          response = await pocketBaseService.changePassword(currentPasswordOrData, newPassword)
        }

        if (response.success) {
          return { success: true }
        } else {
          this.error = response.error || 'Password change failed'
          return { success: false, error: this.error }
        }
      } catch (error) {
        this.error = error.message || 'Password change failed'
        return { success: false, error: this.error }
      } finally {
        this.loading = false
      }
    },

    restoreAuthState() {
      const persistedState = this.getPersistedState()

      // Handle partial data gracefully
      if (persistedState.token) {
        this.token = persistedState.token

        // Only set authenticated if we have both token and user
        if (persistedState.user) {
          this.user = persistedState.user
          this.isAuthenticated = true
        } else {
          // Token without user - keep token but don't authenticate
          this.user = null
          this.isAuthenticated = false
        }
      } else {
        // No token - clear everything
        this.token = null
        this.user = null
        this.isAuthenticated = false
      }
    },

    clearError() {
      this.error = null
    },

    clearAuthState() {
      this.user = null
      this.token = null
      this.isAuthenticated = false
      this.clearPersistedState()
    },

    persistAuthState() {
      if (typeof window !== 'undefined' && window.localStorage) {
        try {
          localStorage.setItem('auth_user', JSON.stringify(this.user))
          localStorage.setItem('auth_token', this.token)
        } catch (error) {
          // Handle localStorage errors gracefully (quota exceeded, disabled, etc.)
          console.warn('Failed to persist auth state:', error.message)
          // Don't throw - this is not critical for functionality
        }
      }
    },

    getPersistedState() {
      if (typeof window !== 'undefined' && window.localStorage) {
        try {
          const user = localStorage.getItem('auth_user')
          const token = localStorage.getItem('auth_token')

          return {
            user: user ? JSON.parse(user) : null,
            token: token || null,
          }
        } catch (error) {
          // Handle localStorage errors gracefully (corrupted data, disabled, etc.)
          console.warn('Failed to get persisted auth state:', error.message)
          return { user: null, token: null }
        }
      }

      return { user: null, token: null }
    },

    clearPersistedState() {
      if (typeof window !== 'undefined' && window.localStorage) {
        try {
          localStorage.removeItem('auth_user')
          localStorage.removeItem('auth_token')
        } catch (error) {
          // Handle localStorage errors gracefully
          console.warn('Failed to clear persisted auth state:', error.message)
          // Don't throw - this is not critical for functionality
        }
      }
    },
  },
})
